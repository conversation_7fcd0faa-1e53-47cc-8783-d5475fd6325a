# Lock Types and Behaviors

This document provides an overview of the lock types supported by the Redis Locking Framework and their associated behaviors. It also explains the concept of lock-type semantic isolation and how it enhances the safety and reliability of distributed locking operations.

## Lock Types

The Redis Locking Framework supports four distinct lock types, each designed for specific use cases and offering unique capabilities:

### 1. Reentrant Locks

Reentrant locks provide traditional mutual exclusion with support for re-entrance by the same thread or process.

- **Use Case**: Ideal for scenarios where a single thread or process may need to acquire the same lock multiple times without causing a deadlock.
- **Behavior**: Allows the lock holder to acquire the lock multiple times, with a corresponding number of releases required to fully release the lock.

### 2. Stamped Locks

Stamped locks are high-performance read-write locks with optimistic read capabilities.

- **Use Case**: Suitable for scenarios with high read concurrency and relatively infrequent writes.
- **Behavior**: Supports multiple concurrent readers with optimistic locking for read operations, allowing for high throughput in read-heavy workloads.

### 3. State Locks

State locks facilitate application state management with atomic state transitions.

- **Use Case**: Useful for managing complex application states where atomic transitions between predefined states are crucial.
- **Behavior**: Ensures that state transitions occur atomically and consistently across distributed systems.

### 4. Semaphore Locks

Semaphore locks provide resource counting and throttling capabilities.

- **Use Case**: Ideal for controlling access to a fixed number of resources or limiting concurrent operations.
- **Behavior**: Allows a specified number of lock acquisitions before blocking subsequent attempts, enabling fine-grained control over resource utilization.

## Lock-Type Semantic Isolation

The Redis Locking Framework implements lock-type semantic isolation to prevent accidental cross-type operations and enhance the overall safety of the locking mechanism.

### Key Concepts

1. **Redis Key Format**: 
   ```
   <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}
   ```
   This format ensures that each lock type has its own namespace within Redis.

2. **Cross-Type Prevention**: The lock-type segment in the Redis key prevents operations intended for one lock type from affecting locks of a different type.

3. **Type-Specific Behaviors**: Each lock type maintains its unique behavior and constraints, isolated from other types.

### Benefits of Semantic Isolation

- **Enhanced Safety**: Prevents unintended interactions between different lock types, reducing the risk of logical errors in distributed systems.
- **Clear Separation of Concerns**: Each lock type can be optimized for its specific use case without impacting other types.
- **Improved Debugging**: Makes it easier to trace and debug locking issues by clearly separating different lock types in Redis.

## Virtual Thread Architecture

The Redis Locking Framework leverages Java's Virtual Thread architecture for efficient asynchronous operations:

- **Non-blocking Operations**: Utilizes Virtual Threads for non-blocking, high-throughput lock operations.
- **Scalability**: Enables handling of a large number of concurrent lock operations without the overhead of traditional thread pools.
- **Simplified Concurrency**: Eliminates the need for manual thread pool management, as Virtual Threads are managed by the JVM.

## Watchdog Mechanism

The framework implements a watchdog mechanism to maintain lock integrity:

- **Safety Buffer Calculation**: 
  ```
  safetyBufferMillis = watchdogInterval * watchdogSafetyFactor
  ```
- **Lease Time Preservation**: The `originalLeaseTimeMillis` is tracked separately from relative times to ensure accurate lock expiration.
- **Heartbeat Strategy**: Uses the calculated safety buffer to determine when to refresh locks, preventing premature expiration.

## Idempotency Support

The framework provides built-in support for idempotent operations:

- **Request Tracking**: Uses a `requestUuid` for operation deduplication.
- **Response Caching**: Implements `responseCacheTtl` for caching results of idempotent operations.
- **Cache Key Pattern**: Includes lock-type segments in the cache key to maintain isolation.

## Conclusion

The Redis Locking Framework's diverse lock types, semantic isolation, and advanced features like Virtual Thread support and idempotency handling provide a robust and flexible solution for distributed locking in complex systems. By understanding these lock types and behaviors, developers can effectively leverage the framework to build reliable and scalable distributed applications.