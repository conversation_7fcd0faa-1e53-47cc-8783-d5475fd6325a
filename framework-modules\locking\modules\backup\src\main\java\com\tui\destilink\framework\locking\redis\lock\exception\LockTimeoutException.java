package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock acquisition times out.
 * <p>
 * This exception is thrown when an asynchronous or blocking lock acquisition
 * fails to acquire the lock within the specified timeout period.
 * </p>
 */
public class LockTimeoutException extends AbstractRedisLockException {

    private final long timeoutMillis;
    private final int attempts;

    /**
     * Constructs a new LockTimeoutException with the specified details.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be
     *                      null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be
     *                      null)
     * @param timeoutMillis The timeout duration in milliseconds
     * @param attempts      The number of acquisition attempts made before timing
     *                      out
     * @param message       Descriptive error message
     */
    public LockTimeoutException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long timeoutMillis, int attempts, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.timeoutMillis = timeoutMillis;
        this.attempts = attempts;
    }

    /**
     * Constructs a new LockTimeoutException with the specified details and cause.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be
     *                      null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be
     *                      null)
     * @param timeoutMillis The timeout duration in milliseconds
     * @param attempts      The number of acquisition attempts made before timing
     *                      out
     * @param message       Descriptive error message
     * @param cause         The underlying cause of this exception
     */
    public LockTimeoutException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long timeoutMillis, int attempts, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.timeoutMillis = timeoutMillis;
        this.attempts = attempts;
    }

    /**
     * Gets the timeout duration in milliseconds.
     *
     * @return The timeout duration in milliseconds
     */
    public long getTimeoutMillis() {
        return timeoutMillis;
    }

    /**
     * Gets the number of acquisition attempts made before timing out.
     *
     * @return The number of acquisition attempts
     */
    public int getAttempts() {
        return attempts;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.timeoutMillis", timeoutMillis);
        contextMap.put("lock.attempts", attempts);
    }
}