# Redis Locking Module: Configuration Guide

[... previous content unchanged ...]

### 5.3. Other Global Settings (`destilink.fw.locking.redis.*`)

These properties are defined directly in `RedisLockProperties` (not in nested classes).

| Property (YAML Key)         | Java Field (`RedisLockProperties`) | Description                                                                                                                   | Default (Java)           | Configuration Level | Programmatic Bucket Override                                | Programmatic Instance Override | Effective Value Logic                                      | Developer Override Safety  | Notes                                                                     |
| :-------------------------- | :--------------------------------- | :---------------------------------------------------------------------------------------------------------------------------- | :----------------------- | :------------------ | :---------------------------------------------------------- | :----------------------------- | :--------------------------------------------------------- | :------------------------- | :------------------------------------------------------------------------ |
| `enabled`                   | `enabled`                          | Enables or disables the entire Redis locking module.                                                                          | `true`                   | Global              | N/A                                                         | N/A                            | Global `RedisLockProperties.enabled`.                      | Internal                   | Controls module activation.                                               |
| `state-key-expiration`      | `stateKeyExpiration`               | Default expiration time for Redis keys used by state-aware locks (e.g., `StateLock`) to store internal state.                 | `PT5M`                   | Global              | `LockBucketBuilder.withDefaultStateKeyExpiration(Duration)` | N/A                            | Bucket > Global                                            | Bucket Overrideable (Safe) | Ensures cleanup of auxiliary state keys.                                  |
| `response-cache-ttl`        | `responseCacheTtl`                 | TTL for response cache entries used for idempotency.                                                                          | `PT10S`                  | Global              | N/A                                                         | N/A                            | Global `RedisLockProperties.responseCacheTtl`.             | Advanced                   | Controls duration for idempotency response caching.                       |

## 6. Idempotency Mechanism

The `responseCacheTtl` property is crucial for the idempotency mechanism. It defines how long the results of lock operations are cached in Redis to prevent duplicate executions of the same operation.

- **Purpose**: Ensures that retried operations (due to network issues or lost responses) do not cause unintended side effects.
- **Mechanism**: Uses a unique `requestUuid` for each logical operation to cache its result.
- **Cache Duration**: Set by `responseCacheTtl`, should be long enough to cover typical client retry windows.

For more details, see the [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md) documentation.

## 7. Watchdog Mechanism

The watchdog automatically extends leases for long-running locks. Its behavior is controlled by the `interval` and `factor` properties in the `WatchdogProperties`.

### Safety Buffer Calculation

The `safetyBufferMillis` is calculated as: `interval * factor`

- **`interval`**: How often the watchdog checks and renews locks.
- **`factor`**: Multiplier to determine the safety buffer.
- **`safetyBufferMillis`**: Minimum lease time for watchdog eligibility and target TTL for renewals.

Example:
- If `interval` is 5 seconds and `factor` is 0.5:
- `safetyBufferMillis` = 5s * 0.5 = 2.5s

This means:
1. Only locks with a lease time > 2.5s are eligible for watchdog management.
2. The watchdog aims to maintain at least a 2.5s TTL for managed locks.

For more details, see the [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) documentation.

## 8. Configuration Example

Here's a comprehensive configuration example including all the new properties:

```yaml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        state-key-expiration: PT5M
        response-cache-ttl: PT10S
        watchdog:
          interval: PT5S
          factor: 0.5
          core-pool-size: 2
          thread-name-prefix: dl-lock-watchdog-
          shutdown-await-termination: PT30S
        defaults:
          lease-time: PT60S
          retry-interval: PT0.1S
          max-retries: 3
          acquire-timeout: PT5S
```

## 9. Property Precedence

The configuration follows a clear precedence order:

1. **Instance-specific settings**: Set via builder methods like `withLeaseTime()` on specific lock instances.
2. **Bucket-level settings**: Set via `LockBucketBuilder` methods like `withDefaultLeaseTime()`.
3. **Global defaults**: Defined in YAML configuration or `RedisLockProperties.Defaults`.

This allows for flexible, granular configuration while maintaining sensible defaults.

[... rest of the document unchanged ...]