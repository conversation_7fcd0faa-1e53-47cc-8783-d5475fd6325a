package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a stamp validation operation fails.
 * <p>
 * This exception is thrown when an attempt to validate a stamp fails,
 * typically for optimistic read stamps.
 * </p>
 */
public class StampValidationException extends AbstractRedisLockException {

    private final String stamp;
    private final String details;

    /**
     * Creates a new stamp validation exception.
     *
     * @param lockKey     The key of the lock associated with the stamp
     * @param lockType    The type of the lock
     * @param lockOwnerId The ID of the lock owner
     * @param stamp       The stamp that failed validation
     * @param message     A descriptive message about the exception
     * @param details     Additional details about the exception
     * @param cause       The underlying cause of the exception
     */
    public StampValidationException(
            String lockKey,
            String lockType,
            String lockOwnerId,
            String stamp,
            String message,
            String details,
            Throwable cause) {
        super(lockKey, lockType, lockOwnerId, null, message, cause);
        this.stamp = stamp;
        this.details = details;
    }

    /**
     * Gets the stamp associated with the lock.
     *
     * @return The stamp
     */
    public String getStamp() {
        return stamp;
    }

    /**
     * Gets additional details about the exception.
     *
     * @return The details
     */
    public String getDetails() {
        return details;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (stamp != null) {
            contextMap.put("lock.stamp", stamp);
        }
        if (details != null) {
            contextMap.put("lock.details", details);
        }
    }
}