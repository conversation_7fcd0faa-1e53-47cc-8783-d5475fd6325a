# Redis Locking Module: Performance Tuning Guide

## 1. Introduction

This guide provides comprehensive performance tuning strategies for the Redis locking module. It covers optimization techniques for different deployment scenarios, load patterns, and performance requirements.

For monitoring performance metrics, see [Monitoring and Metrics](monitoring_and_metrics.md). For troubleshooting performance issues, see [Troubleshooting Guide](troubleshooting_guide.md).

## 2. Performance Fundamentals

### 2.1. Key Performance Metrics

**Primary Metrics**:
- **Lock Acquisition Latency**: Time to acquire a lock (target: <50ms P95)
- **Lock Acquisition Success Rate**: Percentage of successful acquisitions (target: >99%)
- **Throughput**: Locks acquired per second
- **Redis Operation Latency**: Individual Redis command execution time (target: <10ms P95)

**Secondary Metrics**:
- **Lock Hold Duration**: How long locks are held
- **Watchdog Renewal Latency**: Time for watchdog renewals
- **Memory Usage**: Redis memory consumption
- **Connection Pool Utilization**: Redis connection usage

### 2.2. Performance Baseline

Establish baselines for your environment:

```yaml
# Example performance baselines
performance_targets:
  lock_acquisition:
    p50_latency: 5ms
    p95_latency: 20ms
    p99_latency: 50ms
    success_rate: 99.5%
    throughput: 1000/sec
  
  redis_operations:
    script_execution: 5ms
    key_operations: 2ms
    connection_acquisition: 1ms
  
  watchdog:
    renewal_cycle: 100ms
    renewal_success_rate: 99.9%
```

## 3. Redis Configuration Optimization

### 3.1. Redis Server Configuration

**Memory Optimization**:
```conf
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 10

# Disable persistence for better performance (if acceptable)
save ""
appendonly no

# Network optimization
tcp-keepalive 60
tcp-backlog 511
timeout 300

# Threading (Redis 6+)
io-threads 4
io-threads-do-reads yes
```

**Performance Tuning**:
```conf
# Increase client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Optimize hash tables
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# Set appropriate timeouts
lua-time-limit 5000
```

### 3.2. Redis Cluster Configuration

For high-throughput scenarios, consider Redis Cluster:

```conf
# Cluster-specific settings
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
cluster-announce-ip *************
cluster-announce-port 7000
cluster-announce-bus-port 17000

# Optimize cluster communication
cluster-migration-barrier 1
cluster-require-full-coverage no
```

### 3.3. Redis Sentinel Configuration

For high availability with performance considerations:

```conf
# sentinel.conf
sentinel monitor mymaster 127.0.0.1 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 10000

# Performance tuning
sentinel deny-scripts-reconfig yes
```

## 4. Application-Level Optimization

### 4.1. Connection Pool Tuning

**Lettuce Configuration**:
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 20      # Increase for high concurrency
        max-idle: 10        # Keep connections ready
        min-idle: 5         # Maintain minimum connections
        max-wait: 2000ms    # Reasonable wait time
      shutdown-timeout: 100ms
    timeout: 1000ms         # Redis operation timeout
    connect-timeout: 1000ms # Connection establishment timeout
```

**Jedis Configuration** (alternative):
```yaml
spring:
  redis:
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
    timeout: 1000ms
```

### 4.2. Lock Configuration Optimization

**Default Settings Tuning**:
```yaml
destilink:
  fw:
    locking:
      redis:
        defaults:
          lease-time: PT30S           # Shorter for high turnover
          retry-interval: PT0.05S     # 50ms - balance responsiveness/load
          max-retries: 3              # Reasonable retry count
          acquire-timeout: PT2S       # Quick failure for responsiveness
          redis-operation-timeout: PT0.5S # Fast Redis operations
```

**Watchdog Optimization**:
```yaml
destilink:
  fw:
    locking:
      redis:
        watchdog:
          enabled: true
          interval: PT3S              # Balance renewal frequency/load
          core-pool-size: 4           # Scale with expected concurrent locks
          min-lease-time-for-activation: PT10S
          max-ttl-for-renewal-check: PT15S
          redis-operation-timeout: PT1S
```

### 4.3. Application Code Optimization

**Minimize Critical Section Duration**:
```java
// Bad: Long critical section
var lock = lockBucket.lockConfig("user-update").reentrantLock().build();
if (lock.tryAcquire()) {
    try {
        // Expensive database operations
        User user = userRepository.findById(userId);
        user.setLastLogin(Instant.now());
        user.setLoginCount(user.getLoginCount() + 1);
        
        // Expensive external API call
        ProfileData profile = externalApiClient.getProfile(userId);
        user.setProfile(profile);
        
        userRepository.save(user);
    } finally {
        lock.release();
    }
}

// Good: Minimize critical section
// Prepare data outside lock
User user = userRepository.findById(userId);
ProfileData profile = externalApiClient.getProfile(userId);

var lock = lockBucket.lockConfig("user-update").reentrantLock().build();
if (lock.tryAcquire()) {
    try {
        // Quick update only
        user.setLastLogin(Instant.now());
        user.setLoginCount(user.getLoginCount() + 1);
        user.setProfile(profile);
        userRepository.save(user);
    } finally {
        lock.release();
    }
}
```

**Use Appropriate Lock Granularity**:
```java
// Bad: Coarse-grained locking
var lock = lockBucket.lockConfig("all-users").reentrantLock().build();

// Good: Fine-grained locking
var lock = lockBucket.lockConfig("user-" + userId).reentrantLock().build();

// Better: Partitioned locking for high concurrency
int partition = userId.hashCode() % 100;
var lock = lockBucket.lockConfig("user-partition-" + partition).reentrantLock().build();
```

**Optimize Lock Configuration per Use Case**:
```java
// High-frequency, short operations
var quickLock = lockBucket.lockConfig("quick-operation")
    .reentrantLock()
    .withLeaseTime(Duration.ofSeconds(10))
    .withRetryInterval(Duration.ofMillis(25))
    .withMaxRetries(5)
    .build();

// Long-running operations
var longLock = lockBucket.lockConfig("long-operation")
    .reentrantLock()
    .withLeaseTime(Duration.ofMinutes(5))
    .withRetryInterval(Duration.ofMillis(200))
    .withMaxRetries(2)
    .build();

// Critical operations with high success requirement
var criticalLock = lockBucket.lockConfig("critical-operation")
    .reentrantLock()
    .withLeaseTime(Duration.ofSeconds(30))
    .withRetryInterval(Duration.ofMillis(100))
    .withMaxRetries(10)
    .withAcquireTimeout(Duration.ofSeconds(5))
    .build();
```

## 5. Lua Script Optimization

### 5.1. Script Performance Analysis

Monitor script execution times:
```bash
# Check slow log for Lua scripts
redis-cli slowlog get 10 | grep -A 5 -B 5 EVAL

# Monitor script execution in real-time
redis-cli monitor | grep EVAL
```

### 5.2. Script Optimization Techniques

**Minimize Redis Operations in Scripts**:
```lua
-- Bad: Multiple Redis calls
local lock_key = KEYS[1]
local owner = ARGV[1]
local lease_time = ARGV[2]

local current_owner = redis.call('GET', lock_key)
if current_owner == false then
    redis.call('SET', lock_key, owner)
    redis.call('EXPIRE', lock_key, lease_time)
    return 1
end
return 0

-- Good: Combine operations
local lock_key = KEYS[1]
local owner = ARGV[1]
local lease_time = ARGV[2]

local result = redis.call('SET', lock_key, owner, 'EX', lease_time, 'NX')
if result then
    return 1
else
    return 0
end
```

**Use Efficient Data Structures**:
```lua
-- Use appropriate Redis data types
-- For counters: INCR/DECR
-- For sets: SADD/SREM
-- For sorted sets: ZADD/ZREM with scores
```

**Optimize Conditional Logic**:
```lua
-- Bad: Multiple conditional checks
if condition1 then
    if condition2 then
        if condition3 then
            -- action
        end
    end
end

-- Good: Early returns
if not condition1 then
    return 0
end
if not condition2 then
    return 0
end
if not condition3 then
    return 0
end
-- action
```

## 6. Network and Infrastructure Optimization

### 6.1. Network Configuration

**Reduce Network Latency**:
- Deploy Redis close to application instances
- Use dedicated network connections for Redis traffic
- Configure appropriate network buffer sizes
- Enable TCP_NODELAY for low-latency connections

**Network Tuning**:
```bash
# System-level network optimization
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 65536 16777216' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 16777216' >> /etc/sysctl.conf
sysctl -p
```

### 6.2. Hardware Optimization

**Redis Server Hardware**:
- **CPU**: High single-core performance (Redis is mostly single-threaded)
- **Memory**: Sufficient RAM to avoid swapping
- **Storage**: Fast SSD for persistence (if enabled)
- **Network**: Low-latency, high-bandwidth network interface

**Application Server Hardware**:
- **CPU**: Multiple cores for application threads
- **Memory**: Adequate for connection pools and application logic
- **Network**: Consistent low-latency connection to Redis

### 6.3. Container and Orchestration Optimization

**Docker Configuration**:
```dockerfile
# Redis container optimization
FROM redis:7-alpine

# Optimize Redis configuration
COPY redis.conf /usr/local/etc/redis/redis.conf

# Set resource limits
RUN echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf
RUN echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf

EXPOSE 6379
CMD ["redis-server", "/usr/local/etc/redis/redis.conf"]
```

**Kubernetes Configuration**:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-lock
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-lock
  template:
    metadata:
      labels:
        app: redis-lock
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
---
apiVersion: v1
kind: Service
metadata:
  name: redis-lock-service
spec:
  selector:
    app: redis-lock
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
```

## 7. Load Testing and Benchmarking

### 7.1. Load Testing Strategy

**Test Scenarios**:
1. **High Concurrency**: Many threads competing for same lock
2. **High Throughput**: Many different locks acquired rapidly
3. **Mixed Workload**: Combination of short and long-held locks
4. **Failure Scenarios**: Redis unavailability, network issues

**Sample Load Test**:
```java
@Component
public class LockPerformanceTest {
    
    @Autowired
    private LockBucketRegistry lockBucketRegistry;
    
    public void runConcurrencyTest(int threadCount, int operationsPerThread) {
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String lockKey = "perf-test-" + (threadId % 10); // 10 different locks
                        
                        var lock = lockBucketRegistry.builder("performance-test")
                            .lockConfig(lockKey)
                            .reentrantLock()
                            .withLeaseTime(Duration.ofSeconds(1))
                            .withAcquireTimeout(Duration.ofMillis(500))
                            .build();
                        
                        if (lock.tryAcquire()) {
                            try {
                                // Simulate work
                                Thread.sleep(10);
                                successCount.incrementAndGet();
                            } finally {
                                lock.release();
                            }
                        } else {
                            failureCount.incrementAndGet();
                        }
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
            long duration = System.currentTimeMillis() - startTime;
            
            System.out.printf("Performance Test Results:%n");
            System.out.printf("Threads: %d%n", threadCount);
            System.out.printf("Operations per thread: %d%n", operationsPerThread);
            System.out.printf("Total operations: %d%n", threadCount * operationsPerThread);
            System.out.printf("Successful operations: %d%n", successCount.get());
            System.out.printf("Failed operations: %d%n", failureCount.get());
            System.out.printf("Success rate: %.2f%%%n", 
                (successCount.get() * 100.0) / (threadCount * operationsPerThread));
            System.out.printf("Duration: %d ms%n", duration);
            System.out.printf("Throughput: %.2f ops/sec%n", 
                (successCount.get() * 1000.0) / duration);
                
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
    }
}
```

### 7.2. Benchmarking Tools

**Redis Benchmarking**:
```bash
# Basic Redis performance test
redis-benchmark -h localhost -p 6379 -n 100000 -c 50

# Test specific operations
redis-benchmark -h localhost -p 6379 -t set,get -n 100000 -c 50

# Test with pipeline
redis-benchmark -h localhost -p 6379 -n 100000 -c 50 -P 16
```

**Application-Level Benchmarking**:
```bash
# JMeter test plan for lock operations
# Apache Bench for HTTP endpoints that use locks
ab -n 10000 -c 100 http://localhost:8080/api/locked-operation

# Custom benchmarking with detailed metrics
```

## 8. Monitoring and Profiling

### 8.1. Performance Monitoring Setup

**Key Metrics to Monitor**:
```yaml
# Prometheus alerts for performance
groups:
  - name: redis-lock-performance
    rules:
      - alert: HighLockAcquisitionLatency
        expr: histogram_quantile(0.95, rate(redis_lock_acquisition_duration_seconds_bucket[5m])) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High lock acquisition latency detected"
          
      - alert: LowLockSuccessRate
        expr: rate(redis_lock_acquisition_attempts_total{result="success"}[5m]) / rate(redis_lock_acquisition_attempts_total[5m]) < 0.95
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Lock success rate below threshold"
```

### 8.2. Profiling Techniques

**Application Profiling**:
```java
// Use profiling annotations
@Timed(name = "lock.acquisition", description = "Time spent acquiring locks")
public boolean acquireLockWithTiming(String lockKey) {
    // Lock acquisition logic
}

// Custom timing
Timer.Sample sample = Timer.start(meterRegistry);
try {
    // Lock operation
} finally {
    sample.stop(Timer.builder("lock.operation")
        .tag("operation", "acquire")
        .register(meterRegistry));
}
```

**Redis Profiling**:
```bash
# Monitor Redis operations
redis-cli monitor | grep -E "SET|GET|DEL|EVAL" | head -100

# Check memory usage patterns
redis-cli --latency-history -i 1

# Monitor key space
redis-cli --scan --pattern "dl:lock:*" | wc -l
```

## 9. Environment-Specific Optimizations

### 9.1. Development Environment

```yaml
# Optimized for development speed
destilink:
  fw:
    locking:
      redis:
        defaults:
          lease-time: PT10S
          retry-interval: PT0.1S
          max-retries: 2
          acquire-timeout: PT1S
        watchdog:
          enabled: false  # Disable for faster restarts
```

### 9.2. Testing Environment

```yaml
# Optimized for test reliability
destilink:
  fw:
    locking:
      redis:
        defaults:
          lease-time: PT5S   # Short for fast tests
          retry-interval: PT0.05S
          max-retries: 1
          acquire-timeout: PT0.5S
        watchdog:
          enabled: true
          interval: PT1S     # Frequent for test scenarios
```

### 9.3. Production Environment

```yaml
# Optimized for production performance and reliability
destilink:
  fw:
    locking:
      redis:
        defaults:
          lease-time: PT60S
          retry-interval: PT0.1S
          max-retries: 5
          acquire-timeout: PT3S
          redis-operation-timeout: PT1S
        watchdog:
          enabled: true
          interval: PT5S
          core-pool-size: 8
          redis-operation-timeout: PT2S
        health-indicator-enabled: true
        unlock-message-listener-enabled: true
```

## 10. Performance Optimization Checklist

### 10.1. Redis Level
- [ ] Appropriate Redis configuration for workload
- [ ] Sufficient memory allocation
- [ ] Optimized network settings
- [ ] Persistence settings aligned with requirements
- [ ] Connection pooling properly configured

### 10.2. Application Level
- [ ] Minimal critical section duration
- [ ] Appropriate lock granularity
- [ ] Optimal timeout and retry settings
- [ ] Efficient error handling
- [ ] Proper resource cleanup

### 10.3. Infrastructure Level
- [ ] Low-latency network between app and Redis
- [ ] Adequate hardware resources
- [ ] Proper container resource limits
- [ ] Network optimization applied
- [ ] Monitoring and alerting in place

### 10.4. Code Level
- [ ] Lock usage patterns reviewed
- [ ] Performance testing completed
- [ ] Profiling results analyzed
- [ ] Bottlenecks identified and addressed
- [ ] Load testing validates performance targets

This performance tuning guide provides a comprehensive approach to optimizing the Redis locking module for various scenarios and requirements. Regular performance testing and monitoring will help maintain optimal performance as your application scales.