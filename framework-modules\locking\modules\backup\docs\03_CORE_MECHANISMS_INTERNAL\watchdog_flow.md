# Redis Locking Module: Watchdog Flow

## 1. Introduction

The Watchdog mechanism is a critical component of the Redis locking module that provides automatic lease extension for long-running operations. It prevents locks from expiring while legitimate operations are still in progress, ensuring system reliability and preventing race conditions.

This document provides a comprehensive overview of the watchdog flow, its activation logic, renewal process, and integration with the broader locking system. For architectural context, see [Architecture Overview](../01_OVERVIEW_AND_CONCEPTS/architecture.md).

## 2. Watchdog Overview

### 2.1. Purpose and Benefits

- **Automatic Lease Extension**: Extends lock TTL for active operations
- **Deadlock Prevention**: Prevents locks from being held indefinitely
- **Reliability**: Ensures long-running operations don't lose their locks
- **Performance**: Reduces the need for manual lease management
- **Fault Tolerance**: Handles application crashes gracefully

### 2.2. Key Principles

- **Selective Activation**: Only monitors eligible locks
- **Application Instance Binding**: Only extends locks owned by the current instance
- **Safety First**: Conservative approach to prevent lock stealing
- **Resource Efficiency**: Minimal overhead for short-lived locks

## 3. Watchdog Activation Logic

### 3.1. Activation Conditions

A lock becomes eligible for watchdog monitoring when ALL of the following conditions are met:

```mermaid
flowchart TD
    A[Lock Acquisition Request] --> B{Is Watchdog Enabled?}
    B -->|No| Z[No Watchdog Monitoring]
    B -->|Yes| C{Application Instance Binding?}
    C -->|No| Z
    C -->|Yes| D{Sufficient Lease Time?}
    D -->|No| Z
    D -->|Yes| E{Lock Owner Supplier Available?}
    E -->|No| Z
    E -->|Yes| F[Register with Watchdog]
    
    F --> G[Watchdog Monitoring Active]
    
    style A fill:#e1f5fe
    style G fill:#e8f5e8
    style Z fill:#ffebee
    style B fill:#fff3e0
    style C fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#fff3e0
```

### 3.2. Detailed Activation Criteria

#### 3.2.1. Global Watchdog Enablement

```yaml
destilink:
  fw:
    locking:
      redis:
        watchdog:
          enabled: true  # Must be true
```

#### 3.2.2. Application Instance Binding

```java
public interface LockOwnerSupplier {
    /**
     * Determines if the watchdog can manage locks for this owner ID
     * @param lockKey The lock key
     * @param ownerId The owner ID
     * @return true if this application instance can use watchdog for this lock
     */
    boolean canUseWatchdog(String lockKey, String ownerId);
}

@Component
public class DefaultLockOwnerSupplier implements LockOwnerSupplier {
    
    private final String applicationInstanceId;
    
    @Override
    public boolean canUseWatchdog(String lockKey, String ownerId) {
        // Only allow watchdog for locks owned by this application instance
        return ownerId.startsWith(applicationInstanceId + ":");
    }
}
```

#### 3.2.3. Lease Time Threshold

```java
public class WatchdogActivationChecker {
    
    private final RedisLockProperties.WatchdogProperties watchdogProperties;
    
    public boolean isSufficientLeaseTime(Duration leaseTime) {
        Duration minLeaseTime = watchdogProperties.getMinLeaseTimeForActivation();
        return leaseTime.compareTo(minLeaseTime) >= 0;
    }
}
```

**Default Configuration:**
```yaml
destilink:
  fw:
    locking:
      redis:
        watchdog:
          min-lease-time-for-activation: PT10S  # Minimum 10 seconds
```

### 3.3. Registration Process

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class LockWatchdog {
    
    private final Map<String, WatchdogEntry> monitoredLocks = new ConcurrentHashMap<>();
    private final LockOwnerSupplier lockOwnerSupplier;
    
    public void registerLock(String lockKey, String ownerId, Duration leaseTime, 
                           Supplier<Boolean> lockValidator) {
        
        if (!shouldMonitor(lockKey, ownerId, leaseTime)) {
            log.debug("Lock not eligible for watchdog monitoring: {}", lockKey);
            return;
        }
        
        WatchdogEntry entry = WatchdogEntry.builder()
            .lockKey(lockKey)
            .ownerId(ownerId)
            .leaseTime(leaseTime)
            .lockValidator(lockValidator)
            .registrationTime(Instant.now())
            .lastExtensionTime(Instant.now())
            .extensionCount(0)
            .build();
        
        monitoredLocks.put(lockKey, entry);
        log.info("Registered lock for watchdog monitoring: {} (owner: {})", lockKey, ownerId);
    }
    
    private boolean shouldMonitor(String lockKey, String ownerId, Duration leaseTime) {
        return watchdogProperties.isEnabled() &&
               lockOwnerSupplier.canUseWatchdog(lockKey, ownerId) &&
               isSufficientLeaseTime(leaseTime);
    }
}
```

## 4. Watchdog Renewal Process

### 4.1. Scheduled Renewal Cycle

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class LockWatchdog {
    
    @Scheduled(fixedDelayString = "#{@redisLockProperties.watchdog.interval}")
    public void renewLocks() {
        if (!watchdogProperties.isEnabled()) {
            return;
        }
        
        log.debug("Starting watchdog renewal cycle for {} locks", monitoredLocks.size());
        
        List<CompletableFuture<Void>> renewalTasks = monitoredLocks.values().stream()
            .map(this::renewLockAsync)
            .collect(Collectors.toList());
        
        // Wait for all renewals to complete
        CompletableFuture.allOf(renewalTasks.toArray(new CompletableFuture[0]))
            .thenRun(() -> log.debug("Watchdog renewal cycle completed"))
            .exceptionally(throwable -> {
                log.error("Error during watchdog renewal cycle", throwable);
                return null;
            });
    }
    
    private CompletableFuture<Void> renewLockAsync(WatchdogEntry entry) {
        return CompletableFuture.runAsync(() -> renewLock(entry), watchdogExecutor);
    }
}
```

### 4.2. Individual Lock Renewal Logic

```java
private void renewLock(WatchdogEntry entry) {
    try {
        // Validate lock is still active and owned by us
        if (!entry.getLockValidator().get()) {
            log.debug("Lock validation failed, removing from watchdog: {}", entry.getLockKey());
            monitoredLocks.remove(entry.getLockKey());
            return;
        }
        
        // Check if renewal is needed based on remaining TTL
        Long remainingTtl = getRemainingTtl(entry.getLockKey());
        if (remainingTtl == null) {
            log.warn("Lock no longer exists in Redis, removing from watchdog: {}", entry.getLockKey());
            monitoredLocks.remove(entry.getLockKey());
            return;
        }
        
        Duration remainingDuration = Duration.ofMillis(remainingTtl);
        Duration maxTtlForRenewal = watchdogProperties.getMaxTtlForRenewalCheck();
        
        if (remainingDuration.compareTo(maxTtlForRenewal) > 0) {
            log.debug("Lock TTL still high, skipping renewal: {} (remaining: {})", 
                     entry.getLockKey(), remainingDuration);
            return;
        }
        
        // Perform renewal
        boolean renewed = extendLockTtl(entry);
        if (renewed) {
            entry.setLastExtensionTime(Instant.now());
            entry.setExtensionCount(entry.getExtensionCount() + 1);
            log.debug("Successfully renewed lock: {} (extensions: {})", 
                     entry.getLockKey(), entry.getExtensionCount());
        } else {
            log.warn("Failed to renew lock, removing from watchdog: {}", entry.getLockKey());
            monitoredLocks.remove(entry.getLockKey());
        }
        
    } catch (Exception e) {
        log.error("Error renewing lock: {}", entry.getLockKey(), e);
        // Don't remove on error - might be temporary
    }
}
```

### 4.3. TTL Extension Lua Script

```lua
-- extend_lock_ttl.lua
local lockKey = KEYS[1]
local expectedOwner = ARGV[1]
local newTtlMs = tonumber(ARGV[2])

-- Check current owner
local currentOwner = redis.call('GET', lockKey)
if not currentOwner then
    return {false, "LOCK_NOT_EXISTS"}
end

if currentOwner ~= expectedOwner then
    return {false, "NOT_OWNER", currentOwner}
end

-- Extend TTL
redis.call('PEXPIRE', lockKey, newTtlMs)
local newTtl = redis.call('PTTL', lockKey)

return {true, "SUCCESS", newTtl}
```

```java
private boolean extendLockTtl(WatchdogEntry entry) {
    try {
        List<Object> result = redisTemplate.execute(
            extendTtlScript,
            Collections.singletonList(entry.getLockKey()),
            entry.getOwnerId(),
            String.valueOf(entry.getLeaseTime().toMillis())
        );
        
        if (result != null && result.size() > 0) {
            Boolean success = (Boolean) result.get(0);
            if (!success) {
                String reason = result.size() > 1 ? (String) result.get(1) : "UNKNOWN";
                log.debug("Lock extension failed for {}: {}", entry.getLockKey(), reason);
            }
            return success;
        }
        
        return false;
    } catch (Exception e) {
        log.error("Error executing TTL extension script for lock: {}", entry.getLockKey(), e);
        return false;
    }
}
```

## 5. Watchdog Entry Management

### 5.1. WatchdogEntry Data Structure

```java
@Data
@Builder
@AllArgsConstructor
public class WatchdogEntry {
    private final String lockKey;
    private final String ownerId;
    private final Duration leaseTime;
    private final Supplier<Boolean> lockValidator;
    private final Instant registrationTime;
    
    @Setter
    private Instant lastExtensionTime;
    
    @Setter
    private int extensionCount;
    
    // Derived properties
    public Duration getTimeSinceRegistration() {
        return Duration.between(registrationTime, Instant.now());
    }
    
    public Duration getTimeSinceLastExtension() {
        return Duration.between(lastExtensionTime, Instant.now());
    }
    
    public boolean isStale(Duration maxAge) {
        return getTimeSinceLastExtension().compareTo(maxAge) > 0;
    }
}
```

### 5.2. Lock Validator Implementation

```java
public class RedisReentrantLock implements ReentrantLock {
    
    private Supplier<Boolean> createLockValidator() {
        return () -> {
            try {
                String currentOwner = redisTemplate.opsForValue().get(lockKey);
                return ownerId.equals(currentOwner);
            } catch (Exception e) {
                log.debug("Lock validation failed for {}: {}", lockKey, e.getMessage());
                return false;
            }
        };
    }
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        boolean acquired = doTryLock(time, unit);
        
        if (acquired && shouldUseWatchdog()) {
            lockWatchdog.registerLock(
                lockKey, 
                ownerId, 
                leaseTime, 
                createLockValidator()
            );
        }
        
        return acquired;
    }
}
```

### 5.3. Cleanup and Deregistration

```java
@Service
public class LockWatchdog {
    
    public void deregisterLock(String lockKey) {
        WatchdogEntry removed = monitoredLocks.remove(lockKey);
        if (removed != null) {
            log.debug("Deregistered lock from watchdog: {} (extensions: {})", 
                     lockKey, removed.getExtensionCount());
        }
    }
    
    @Scheduled(fixedDelay = 300000) // Every 5 minutes
    public void cleanupStaleEntries() {
        Duration maxAge = watchdogProperties.getInterval().multipliedBy(5);
        
        List<String> staleKeys = monitoredLocks.entrySet().stream()
            .filter(entry -> entry.getValue().isStale(maxAge))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        
        staleKeys.forEach(key -> {
            monitoredLocks.remove(key);
            log.info("Removed stale watchdog entry: {}", key);
        });
        
        if (!staleKeys.isEmpty()) {
            log.info("Cleaned up {} stale watchdog entries", staleKeys.size());
        }
    }
}
```

## 6. Integration with Lock Types

### 6.1. Reentrant Lock Integration

```java
public class RedisReentrantLock implements ReentrantLock {
    
    @Override
    public void unlock() {
        try {
            doUnlock();
        } finally {
            // Always deregister from watchdog on unlock
            lockWatchdog.deregisterLock(lockKey);
        }
    }
    
    private boolean shouldUseWatchdog() {
        return lockWatchdog.shouldMonitor(lockKey, ownerId, leaseTime);
    }
}
```

### 6.2. State Lock Integration

```java
public class RedisStateLock implements StateLock {
    
    @Override
    public boolean tryLockWithState(String expectedState, String newState, long time, TimeUnit unit) 
            throws InterruptedException {
        
        boolean acquired = doTryLockWithState(expectedState, newState, time, unit);
        
        if (acquired && shouldUseWatchdog()) {
            lockWatchdog.registerLock(
                lockKey, 
                ownerId, 
                leaseTime, 
                createStateLockValidator()
            );
        }
        
        return acquired;
    }
    
    private Supplier<Boolean> createStateLockValidator() {
        return () -> {
            try {
                String currentOwner = redisTemplate.opsForValue().get(lockKey);
                return ownerId.equals(currentOwner);
            } catch (Exception e) {
                return false;
            }
        };
    }
}
```

### 6.3. Semaphore Lock Integration

```java
public class RedisSemaphoreLock implements SemaphoreLock {
    
    @Override
    public boolean tryAcquire(long timeout, TimeUnit unit) throws InterruptedException {
        boolean acquired = doTryAcquire(timeout, unit);
        
        // Semaphores typically don't use watchdog due to their nature
        // But could be enabled for specific use cases
        if (acquired && shouldUseWatchdog()) {
            lockWatchdog.registerLock(
                semaphoreKey, 
                ownerId, 
                leaseTime, 
                createSemaphoreValidator()
            );
        }
        
        return acquired;
    }
}
```

## 7. Configuration and Tuning

### 7.1. Watchdog Configuration Properties

```yaml
destilink:
  fw:
    locking:
      redis:
        watchdog:
          # Enable/disable watchdog globally
          enabled: true
          
          # Renewal interval (how often to check and renew locks)
          interval: PT5S
          
          # Minimum lease time for watchdog activation
          min-lease-time-for-activation: PT10S
          
          # Skip renewal if TTL is above this threshold
          max-ttl-for-renewal-check: PT15S
          
          # Thread pool configuration
          core-pool-size: 2
          thread-name-prefix: "dl-lock-watchdog-"
          
          # Shutdown configuration
          shutdown-await-termination: PT30S
          
          # Redis operation timeout for watchdog operations
          redis-operation-timeout: PT1S
```

### 7.2. Performance Tuning Guidelines

#### 7.2.1. Interval Tuning

- **Short intervals** (1-5s): Better responsiveness, higher CPU usage
- **Long intervals** (10-30s): Lower overhead, risk of lock expiration
- **Recommended**: 1/3 of minimum lease time

#### 7.2.2. TTL Threshold Tuning

```java
// Rule of thumb: maxTtlForRenewalCheck should be > interval * 2
Duration interval = Duration.ofSeconds(5);
Duration maxTtlForRenewal = interval.multipliedBy(3); // 15 seconds
```

#### 7.2.3. Thread Pool Sizing

```java
// Base calculation: 1 thread per 50-100 concurrent long-lived locks
int expectedLongLivedLocks = 200;
int recommendedCorePoolSize = Math.max(2, expectedLongLivedLocks / 75);
```

## 8. Monitoring and Observability

### 8.1. Watchdog Metrics

```java
@Component
@RequiredArgsConstructor
public class WatchdogMetrics {
    
    private final MeterRegistry meterRegistry;
    
    private final Gauge monitoredLocksGauge = Gauge.builder("locking.watchdog.monitored.locks")
        .description("Number of locks currently monitored by watchdog")
        .register(meterRegistry);
    
    private final Counter renewalSuccessCounter = Counter.builder("locking.watchdog.renewals.success")
        .description("Number of successful lock renewals")
        .register(meterRegistry);
    
    private final Counter renewalFailureCounter = Counter.builder("locking.watchdog.renewals.failure")
        .description("Number of failed lock renewals")
        .register(meterRegistry);
    
    private final Timer renewalDurationTimer = Timer.builder("locking.watchdog.renewal.duration")
        .description("Duration of watchdog renewal operations")
        .register(meterRegistry);
    
    public void updateMonitoredLocksCount(int count) {
        monitoredLocksGauge.set(count);
    }
    
    public void recordRenewalSuccess() {
        renewalSuccessCounter.increment();
    }
    
    public void recordRenewalFailure() {
        renewalFailureCounter.increment();
    }
    
    public Timer.Sample startRenewalTimer() {
        return Timer.start(meterRegistry);
    }
}
```

### 8.2. Health Indicator

```java
@Component
@RequiredArgsConstructor
public class WatchdogHealthIndicator implements HealthIndicator {
    
    private final LockWatchdog lockWatchdog;
    private final RedisLockProperties.WatchdogProperties watchdogProperties;
    
    @Override
    public Health health() {
        if (!watchdogProperties.isEnabled()) {
            return Health.up()
                .withDetail("status", "disabled")
                .build();
        }
        
        try {
            int monitoredLocks = lockWatchdog.getMonitoredLocksCount();
            Instant lastRenewalCycle = lockWatchdog.getLastRenewalCycleTime();
            
            Duration timeSinceLastCycle = Duration.between(lastRenewalCycle, Instant.now());
            Duration maxExpectedDelay = watchdogProperties.getInterval().multipliedBy(3);
            
            if (timeSinceLastCycle.compareTo(maxExpectedDelay) > 0) {
                return Health.down()
                    .withDetail("status", "renewal-cycle-delayed")
                    .withDetail("monitored-locks", monitoredLocks)
                    .withDetail("time-since-last-cycle", timeSinceLastCycle.toString())
                    .build();
            }
            
            return Health.up()
                .withDetail("status", "active")
                .withDetail("monitored-locks", monitoredLocks)
                .withDetail("last-renewal-cycle", lastRenewalCycle.toString())
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("status", "error")
                .withException(e)
                .build();
        }
    }
}
```

## 9. Error Handling and Recovery

### 9.1. Redis Connection Failures

```java
private void renewLock(WatchdogEntry entry) {
    int maxRetries = 3;
    int attempt = 0;
    
    while (attempt < maxRetries) {
        try {
            boolean renewed = extendLockTtl(entry);
            if (renewed) {
                entry.setLastExtensionTime(Instant.now());
                entry.setExtensionCount(entry.getExtensionCount() + 1);
                return;
            } else {
                log.warn("Lock renewal failed on attempt {}: {}", attempt + 1, entry.getLockKey());
                break; // Don't retry logical failures
            }
        } catch (RedisConnectionFailureException e) {
            attempt++;
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(100 * attempt); // Exponential backoff
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            } else {
                log.error("Failed to renew lock after {} attempts: {}", maxRetries, entry.getLockKey(), e);
            }
        } catch (Exception e) {
            log.error("Unexpected error renewing lock: {}", entry.getLockKey(), e);
            break;
        }
    }
    
    // Remove failed lock from monitoring
    monitoredLocks.remove(entry.getLockKey());
}
```

### 9.2. Application Shutdown Handling

```java
@PreDestroy
public void shutdown() {
    log.info("Shutting down lock watchdog...");
    
    try {
        // Stop accepting new locks
        watchdogEnabled = false;
        
        // Cancel scheduled tasks
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            
            if (!scheduledExecutor.awaitTermination(
                    watchdogProperties.getShutdownAwaitTermination().toSeconds(), 
                    TimeUnit.SECONDS)) {
                log.warn("Watchdog executor did not terminate gracefully, forcing shutdown");
                scheduledExecutor.shutdownNow();
            }
        }
        
        // Clear monitored locks
        int clearedLocks = monitoredLocks.size();
        monitoredLocks.clear();
        
        log.info("Lock watchdog shutdown complete. Cleared {} monitored locks", clearedLocks);
        
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.error("Interrupted during watchdog shutdown", e);
    }
}
```

## 10. Best Practices and Troubleshooting

### 10.1. Best Practices

1. **Appropriate Lease Times**: Use lease times 3-5x longer than expected operation duration
2. **Monitor Renewal Ratios**: Track successful vs failed renewals
3. **Resource Limits**: Set appropriate thread pool sizes based on expected load
4. **Graceful Degradation**: Application should handle watchdog failures gracefully
5. **Testing**: Test watchdog behavior under various failure scenarios

### 10.2. Common Issues and Solutions

#### Issue: High CPU Usage
**Symptoms**: High CPU usage from watchdog threads
**Solutions**:
- Increase renewal interval
- Reduce number of long-lived locks
- Optimize lock validation logic

#### Issue: Locks Expiring Despite Watchdog
**Symptoms**: Locks expire even with watchdog active
**Solutions**:
- Check application instance binding configuration
- Verify lease time meets minimum threshold
- Review Redis connectivity and performance

#### Issue: Memory Leaks
**Symptoms**: Growing memory usage in watchdog
**Solutions**:
- Ensure proper lock deregistration
- Enable stale entry cleanup
- Monitor watchdog entry lifecycle

### 10.3. Debugging Tools

```java
@RestController
@RequiredArgsConstructor
public class WatchdogDebugController {
    
    private final LockWatchdog lockWatchdog;
    
    @GetMapping("/debug/watchdog/status")
    public Map<String, Object> getWatchdogStatus() {
        return Map.of(
            "enabled", lockWatchdog.isEnabled(),
            "monitored-locks", lockWatchdog.getMonitoredLocksCount(),
            "last-renewal-cycle", lockWatchdog.getLastRenewalCycleTime(),
            "total-renewals", lockWatchdog.getTotalRenewals(),
            "failed-renewals", lockWatchdog.getFailedRenewals()
        );
    }
    
    @GetMapping("/debug/watchdog/locks")
    public List<Map<String, Object>> getMonitoredLocks() {
        return lockWatchdog.getMonitoredLocks().stream()
            .map(entry -> Map.of(
                "lock-key", entry.getLockKey(),
                "owner-id", entry.getOwnerId(),
                "registration-time", entry.getRegistrationTime(),
                "extension-count", entry.getExtensionCount(),
                "time-since-last-extension", entry.getTimeSinceLastExtension()
            ))
            .collect(Collectors.toList());
    }
}
```

The watchdog flow is essential for maintaining lock reliability in long-running operations while providing the safety mechanisms needed to prevent deadlocks and resource leaks in distributed systems.