package com.tui.destilink.framework.locking.redis.lock.service.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockCommandException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockConnectionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockOperationException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockTimeoutException;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.service.ScriptLoader;
import com.tui.destilink.framework.locking.redis.lock.util.LockKeyBuilder;
import com.tui.destilink.framework.locking.redis.lock.util.RedisLockContextDecorator;
import com.tui.destilink.framework.locking.redis.lock.util.VirtualThreadContextUtils;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * Redis-based implementation of distributed lock operations with comprehensive retry logic
 * and Virtual Thread integration for improved I/O handling and resilience.
 */
@Slf4j
@Component
public class RedisLockOperationsImpl implements RedisLockOperations {

    private final ClusterCommandExecutor clusterCommandExecutor;
    private final ScriptLoader scriptLoader;
    private final RedisLockProperties properties;
    private final ExecutorService virtualThreadExecutor;
    private final ImmutableLettuceScript<Long> tryLockScript;
    private final ImmutableLettuceScript<Long> unlockScript;
    private final ImmutableLettuceScript<Long> extendLockScript;
    private final ImmutableLettuceScript<Long> checkLockScript;
    private final ImmutableLettuceScript<Long> updateStateScript;
    private final ImmutableLettuceScript<Long> updateStateIfEqualsScript;
    private final ImmutableLettuceScript<Long> tryStateLockScript;
    private final ImmutableLettuceScript<Long> unlockStateLockScript;

    // Circuit breaker state tracking
    private final AtomicInteger circuitBreakerFailureCount = new AtomicInteger(0);
    private volatile long circuitBreakerLastFailureTime = 0;
    private volatile boolean circuitBreakerOpen = false;

    // Random instance for jitter calculation
    private final Random random = new Random();

    // Retryable exception types
    private final Set<Class<? extends Exception>> retryableExceptions = Set.of(
        RedisConnectionFailureException.class,
        QueryTimeoutException.class,
        DataAccessException.class
    );

    public RedisLockOperationsImpl(ClusterCommandExecutor clusterCommandExecutor,
                                   ScriptLoader scriptLoader,
                                   RedisLockProperties properties,
                                   ExecutorService virtualThreadExecutor) {
        this.clusterCommandExecutor = clusterCommandExecutor;
        this.scriptLoader = scriptLoader;
        this.properties = properties;
        this.virtualThreadExecutor = virtualThreadExecutor;
        this.tryLockScript = (ImmutableLettuceScript<Long>) scriptLoader.getTryLockScript();
        this.unlockScript = (ImmutableLettuceScript<Long>) scriptLoader.getReleaseLockScript();
        this.extendLockScript = (ImmutableLettuceScript<Long>) scriptLoader.getExtendLockScript();
        this.checkLockScript = (ImmutableLettuceScript<Long>) scriptLoader.getCheckLockScript();
        this.updateStateScript = (ImmutableLettuceScript<Long>) scriptLoader.getUpdateStateScript();
        this.updateStateIfEqualsScript = (ImmutableLettuceScript<Long>) scriptLoader.getUpdateStateIfEqualsScript();
        this.tryStateLockScript = (ImmutableLettuceScript<Long>) scriptLoader.getTryStateLockScript();
        this.unlockStateLockScript = (ImmutableLettuceScript<Long>) scriptLoader.getUnlockStateLockScript();
    }

    /**
     * Generates a unique UUID for request idempotency.
     * This centralizes UUID generation for all lock operations.
     *
     * @return A unique UUID string for the current operation
     */
    private String generateRequestUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * Gets the response cache TTL in seconds from configuration.
     *
     * @return Response cache TTL in seconds as a string
     */
    private String getResponseCacheTtlSeconds() {
        return String.valueOf(properties.getResponseCacheTtl().getSeconds());
    }

    /**
     * Executes a Redis operation with retry logic using Virtual Thread-friendly delays.
     * This method implements application-level retries for individual Redis operations
     * as specified in the detailed plan.
     *
     * @param operationName Name of the operation for logging
     * @param operation The operation to execute
     * @param <T> Return type of the operation
     * @return CompletableFuture with the operation result
     */
    private <T> CompletableFuture<T> executeWithRetry(String operationName, Supplier<CompletableFuture<T>> operation) {
        return CompletableFuture.supplyAsync(() -> {
            int maxRetries = properties.getDefaults().getMaxRetries();
            long retryIntervalMillis = properties.getDefaults().getRetryInterval().toMillis();

            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return operation.get().join();
                } catch (Exception e) {
                    lastException = e;

                    // Check if this is a retryable exception
                    boolean isRetryable = retryableExceptions.stream()
                            .anyMatch(retryableType -> retryableType.isInstance(e) ||
                                     (e.getCause() != null && retryableType.isInstance(e.getCause())));

                    if (!isRetryable || attempt >= maxRetries) {
                        log.error("Operation {} failed after {} attempts (non-retryable or max retries reached)",
                                operationName, attempt + 1, e);
                        throw e;
                    }

                    log.warn("Operation {} failed on attempt {}/{}, retrying in {}ms",
                            operationName, attempt + 1, maxRetries + 1, retryIntervalMillis, e);

                    // Use Thread.sleep() for Virtual Thread-friendly delay
                    try {
                        Thread.sleep(retryIntervalMillis);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Retry interrupted", ie);
                    }
                }
            }

            // This should never be reached, but just in case
            throw new RuntimeException("Operation " + operationName + " failed after all retries", lastException);
        }, virtualThreadExecutor);
    }

    @Override
    public CompletableFuture<Boolean> acquireLock(String lockKey, String lockValue, Duration ttl) {
        return tryLock(lockKey, lockValue, ttl);
    }

    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String lockValue) {
        return unlock(lockKey, lockValue)
                .thenApply(unused -> true) // Successfully executed
                .exceptionally(ex -> {
                    log.error("Lock release failed for key: {}, value: {}", lockKey, lockValue, ex);
                    return false;
                });
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String lockValue, Duration ttl) {
        log.debug("tryLock called: key={}, value={}, ttl={}", lockKey, lockValue, ttl);

        return executeWithRetry("tryLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    lockValue,
                    String.valueOf(ttl.toMillis()),
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    tryLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code (negative indicates success)
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode < 0; // Negative indicates success
                        }
                        return false;
                    });
        });
    }

    @Override
    public CompletableFuture<Void> unlock(String lockKey, String lockValue) {
        log.debug("unlock called: key={}, value={}", lockKey, lockValue);

        return executeWithRetry("unlock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;
            String unlockChannel = lockKey + ":unlock";

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey, unlockChannel };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    lockValue,
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockScript,
                    keys,
                    args).thenAccept(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("Lock not held by current owner: {}, {}", lockKey, lockValue);
                            } else if (statusCode == -1) {
                                log.warn("Lock held by different owner: {}, {}", lockKey, lockValue);
                            }
                        }
                    });
        });
    }

    // Other methods remain as placeholders for now
    // Will be implemented in subsequent steps

    @Override
    public CompletableFuture<Boolean> extendLock(String lockKey, String lockValue, Duration ttl) {
        log.debug("extendLock called: key={}, value={}, ttl={}", lockKey, lockValue, ttl);

        return executeWithRetry("extendLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    lockValue,
                    String.valueOf(ttl.toMillis()),
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    extendLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code (positive indicates success)
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }

    @Override
    public CompletableFuture<Boolean> checkLock(String lockKey, String lockValue) {
        log.debug("checkLock called: key={}, value={}", lockKey, lockValue);
        String[] keys = new String[] { lockKey };
        String[] args = new String[] { lockValue };

        // Use updateStateScript as a generic check script (or we could define a
        // dedicated check script)
        return clusterCommandExecutor.executeScript(
                lockKey,
                updateStateScript,
                keys,
                args).thenApply(result -> {
                    // If result is > 0, the lock is held by this owner
                    return result != null && ((Long) result) > 0;
                });
    }

    @Override
    public CompletableFuture<String> tryStateLock(String lockKey, String responseCacheKey, String requestUuid,
            String leaseTimeMs, String lockOwnerId, String expectedState,
            boolean initializeIfAbsent, String initialState,
            String responseCacheTTLSeconds) {
        log.debug("tryStateLock called: key={}, owner={}, expectedState={}", lockKey, lockOwnerId, expectedState);
        
        // Generate UUID centrally if not provided
        String effectiveRequestUuid = (requestUuid != null && !requestUuid.isEmpty()) ? requestUuid : generateRequestUuid();
        
        // Use configured response cache TTL if not provided
        String effectiveResponseCacheTtl = (responseCacheTTLSeconds != null && !responseCacheTTLSeconds.isEmpty())
            ? responseCacheTTLSeconds : getResponseCacheTtlSeconds();
        
        String[] keys = new String[] { lockKey, responseCacheKey };
        String[] args = new String[] {
                effectiveRequestUuid,
                leaseTimeMs,
                lockOwnerId,
                expectedState,
                String.valueOf(initializeIfAbsent),
                initialState,
                effectiveResponseCacheTtl
        };
        
        return clusterCommandExecutor.executeScript(
                lockKey,
                tryStateLockScript,
                keys,
                args).thenApply(result -> {
                    log.debug("tryStateLock result: {}", result);
                    return result != null ? result.toString() : null;
                });
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout) {
        log.debug("tryLock with timeout called: key={}, value={}, ttl={}, timeout={}", lockKey, ownerId, ttl,
                acquireTimeout);
        return CompletableFuture.completedFuture(false);
    }

    @Override
    public CompletableFuture<Boolean> isLocked(String lockKey) {
        log.debug("isLocked called: key={}", lockKey);

        // Use checkLock script to determine if the lock exists
        String[] keys = new String[] { lockKey };
        String[] args = new String[] { "" }; // Empty owner ID to just check existence

        return clusterCommandExecutor.executeScript(
                lockKey,
                checkLockScript,
                keys,
                args).thenApply(result -> {
                    // If result is > 0, the lock exists
                    return result != null && ((Long) result) > 0;
                });
    }

    @Override
    public CompletableFuture<String> releaseStateLock(String lockKey, String responseCacheKey, String requestUuid,
            String lockOwnerId, String newState) {
        log.debug("releaseStateLock called: key={}, owner={}", lockKey, lockOwnerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> extendStateLock(String lockKey, String responseCacheKey, String requestUuid,
            String newLeaseTimeMs, String lockOwnerId) {
        log.debug("extendStateLock called: key={}, owner={}", lockKey, lockOwnerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<String> getStateLockState(String lockKey) {
        log.debug("getStateLockState called: key={}", lockKey);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> tryReadLock(String lockKey, String responseCacheKey, String requestUuid,
            String leaseTimeMs, String readerId, String responseCacheTTLSeconds) {
        log.debug("tryReadLock called: key={}, reader={}", lockKey, readerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Boolean> hset(String key, String field, String value) {
        log.debug("hset called: key={}, field={}, value={}", key, field, value);
        return CompletableFuture.completedFuture(false);
    }

    @Override
    public CompletableFuture<String> unlockReadLock(String lockKey, String readerId, String requestUuid) {
        log.debug("unlockReadLock called: key={}, reader={}", lockKey, readerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> tryWriteLock(String lockKey, String responseCacheKey, String requestUuid,
            String leaseTimeMs, String writerId, String responseCacheTTLSeconds, String mode) {
        log.debug("tryWriteLock called: key={}, writer={}", lockKey, writerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<String> unlockWriteLock(String lockKey, String writerId, String requestUuid,
            String command) {
        log.debug("unlockWriteLock called: key={}, writer={}", lockKey, writerId);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> publishUnlockMessage(String lockKey, String message) {
        log.debug("publishUnlockMessage called: key={}, message={}", lockKey, message);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Void> subscribeToUnlockChannel(String lockKey,
            java.util.function.Consumer<String> callback) {
        log.debug("subscribeToUnlockChannel called: key={}", lockKey);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Void> unsubscribeFromUnlockChannel(String lockKey) {
        log.debug("unsubscribeFromUnlockChannel called: key={}", lockKey);
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<String> getString(String key) {
        log.debug("getString called: key={}", key);
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Use the script loader to load the get_value.lua script with proper casting
                ImmutableLettuceScript<String> getScript = (ImmutableLettuceScript<String>) scriptLoader
                        .loadScript("get_value.lua", String.class);
                String result = clusterCommandExecutor.executeScript(
                        key,
                        getScript,
                        new String[] { key },
                        new String[] {}).get();
                log.debug("getString result: {}", result);
                return result;
            } catch (Exception e) {
                log.error("Error getting string value for key: {}", key, e);
                return null;
            }
        });
    }

    @Override
    public CompletableFuture<Integer> updateState(
            String lockKey,
            String responseCacheKey,
            String requestUuid,
            String lockOwnerId,
            String newState,
            String stateKeySuffix,
            String stateExpirationMs,
            String responseCacheTtlSeconds) {
        log.debug("updateState called: key={}, owner={}, newState={}", lockKey, lockOwnerId, newState);

        String[] keys = new String[] { lockKey, responseCacheKey };
        String[] args = new String[] {
                requestUuid,
                lockOwnerId,
                newState,
                stateKeySuffix,
                stateExpirationMs,
                responseCacheTtlSeconds
        };

        return clusterCommandExecutor.executeScript(
                lockKey,
                updateStateScript,
                keys,
                args).thenApply(result -> {
                    log.debug("updateState result: {}", result);
                    return result.intValue();
                });
    }

    @Override
    public CompletableFuture<Integer> updateStateIfEquals(
            String lockKey,
            String responseCacheKey,
            String requestUuid,
            String lockOwnerId,
            String expectedState,
            String newState,
            String stateKeySuffix,
            String stateExpirationMs,
            String responseCacheTtlSeconds) {
        log.debug("updateStateIfEquals called: key={}, owner={}, expectedState={}, newState={}",
                lockKey, lockOwnerId, expectedState, newState);

        String[] keys = new String[] { lockKey, responseCacheKey };
        String[] args = new String[] {
                requestUuid,
                lockOwnerId,
                expectedState,
                newState,
                stateKeySuffix,
                stateExpirationMs,
                responseCacheTtlSeconds
        };

        return clusterCommandExecutor.executeScript(
                lockKey,
                updateStateIfEqualsScript,
                keys,
                args).thenApply(result -> {
                    log.debug("updateStateIfEquals result: {}", result);
                    return result.intValue();
                });
    }

    @Override
    public CompletableFuture<Void> unlockStateLock(
            String lockKey,
            String unlockChannelPrefix,
            String responseCacheKey,
            String requestUuid,
            String lockOwnerId,
            String newState,
            String stateKeySuffix,
            String stateExpirationMs,
            String responseCacheTtlSeconds) {
        log.debug("unlockStateLock called: key={}, owner={}, newState={}", lockKey, lockOwnerId, newState);

        String[] keys = new String[] { lockKey, responseCacheKey };
        String[] args = new String[] {
                requestUuid,
                lockOwnerId,
                newState,
                stateKeySuffix,
                stateExpirationMs,
                unlockChannelPrefix,
                responseCacheTtlSeconds
        };

        return clusterCommandExecutor.executeScript(
                lockKey,
                unlockStateLockScript,
                keys,
                args).thenAccept(result -> {
                    if ((Long) result == 0) {
                        log.warn("State lock not held by current owner: {}, {}", lockKey, lockOwnerId);
                    } else {
                        log.debug("unlockStateLock result: {}", result);
                    }
                });
    }
}