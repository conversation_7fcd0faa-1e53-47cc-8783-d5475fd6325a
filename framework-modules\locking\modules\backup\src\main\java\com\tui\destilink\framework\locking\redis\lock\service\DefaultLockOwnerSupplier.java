package com.tui.destilink.framework.locking.redis.lock.service;

import org.springframework.stereotype.Component;

/**
 * Default implementation of {@link LockOwnerSupplier} that provides a unique
 * identifier
 * for the current lock owner.
 * <p>
 * This implementation generates owner IDs by combining the application instance
 * ID
 * and the current thread ID, ensuring uniqueness across a distributed system.
 * It also
 * determines whether the watchdog mechanism can be used for specific locks.
 * </p>
 * <p>
 * The watchdog is responsible for automatically extending the lease of active
 * locks
 * to prevent premature expiration while they are still in use.
 * </p>
 */
@Component
public class DefaultLockOwnerSupplier implements LockOwnerSupplier {

    /**
     * Returns a unique identifier for the current lock owner.
     * <p>
     * This implementation combines the application instance ID and the current
     * thread ID
     * to create a unique identifier that can be used to determine lock ownership
     * across
     * a distributed system.
     * </p>
     *
     * @return a unique owner ID string
     */
    @Override
    public String get() {
        // Use a placeholder for application instance ID
        // In a real implementation, this would be injected from Spring's environment
        String applicationInstanceId = System.getProperty("spring.application.instance_id", "default-instance-id");

        // Combine with the current thread ID for uniqueness
        long threadId = Thread.currentThread().getId();

        return applicationInstanceId + "-" + threadId;
    }

    /**
     * Determines if the watchdog can be used for the specific lock instance.
     * <p>
     * This default implementation always returns {@code true}, indicating that
     * all locks created by this supplier are eligible for watchdog lease extension.
     * </p>
     * <p>
     * The actual watchdog activation logic is handled by the {@code LockWatchdog}
     * based on
     * multiple conditions, including lock configuration, lease time, and
     * application state.
     * </p>
     *
     * @param lockIdentifier the unique identifier of the lock
     * @param ownerId        the owner ID of the lock
     * @return {@code true} indicating that the watchdog can be used for this lock
     */
    @Override
    public boolean canUseWatchdog(String lockIdentifier, String ownerId) {
        // Default implementation always allows watchdog usage
        // The actual watchdog activation logic is handled by the LockWatchdog
        return true;
    }
}