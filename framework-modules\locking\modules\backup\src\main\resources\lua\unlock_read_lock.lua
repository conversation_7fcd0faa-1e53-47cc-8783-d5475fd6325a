-- KEYS[1] mainLockKey (e.g., myApp:__lock_buckets__:resource:__locks__:{myRWLock})
-- KEYS[2] unlockChannelBaseName (e.g., myApp:__lock_buckets__:resource:__unlock_channels__)
-- KEYS[3] timeoutPrefixForCurrentReaderInstance (e.g. myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerIdFromArgv>)
-- KEYS[4] generalTimeoutKeyPrefixForAllReaders (e.g. myApp:__lock_buckets__:resource:__rwttl__:{myRWLock})
-- KEYS[5] responseCacheKey (optional)

-- ARGV[1] requestUuid (used if KEYS[5] is provided)
-- ARGV[2] readerId (field name in KEYS[1] for the reader releasing)
-- ARGV[3] publishCommand (e.g., "PUBLISH")
-- ARGV[4] unlockMessagePayload (UnlockType string, e.g., "RW_READ_RELEASED_WAKEN_READERS")
-- ARGV[5] responseCacheTTLSeconds (optional, in seconds)
-- ARGV[6] writerHolderFieldNameInMainHash (e.g., "write_owner_id")
-- ARGV[7] modeFieldNameInMainHash (e.g., "mode")

if ARGV[5] ~= nil and KEYS[5] ~= nil and redis.call('exists', KEYS[5]) == 1 then
    local cachedValue = redis.call('get', KEYS[5]);
    if cachedValue ~= false then return tonumber(cachedValue); end
end;

local mode = redis.call('hget', KEYS[1], ARGV[7]);
if (mode == false) then -- Main lock key itself doesn't exist
    local key1_parts = {}
    for part in string.gmatch(KEYS[1], "[^:]+") do table.insert(key1_parts, part) end
    local lockNameSuffix = key1_parts[#key1_parts] 
    redis.call(ARGV[3], KEYS[2] .. ':' .. lockNameSuffix, ARGV[4]);
    if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '1', 'px', tonumber(ARGV[5]) * 1000); end; -- TTL in ms for SET
    return 1;
end;

local currentReentrantCount = tonumber(redis.call('hget', KEYS[1], ARGV[2]));
if (currentReentrantCount == nil or currentReentrantCount == 0) then
    if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '0', 'px', tonumber(ARGV[5]) * 1000); end;
    return 0; -- Not held by this readerId or count error
end;

-- Delete the individual timeout key for the specific reentrant instance being released
redis.call('del', KEYS[3] .. ':' .. currentReentrantCount); 

local newReentrantCount = redis.call('hincrby', KEYS[1], ARGV[2], -1);
if (newReentrantCount == 0) then
    redis.call('hdel', KEYS[1], ARGV[2]); -- Remove reader field from hash
end;

local maxRemainTime = -3; 
local activeElements = false;

-- Check if writer holds the lock
if redis.call('hexists', KEYS[1], ARGV[6]) == 1 then
    activeElements = true;
    maxRemainTime = redis.call('pttl', KEYS[1]); -- Writer active, main key TTL is authoritative for now
else
    -- No writer, check other readers
    local allFields = redis.call('hkeys', KEYS[1]);
    for i, fieldName in ipairs(allFields) do
        if fieldName ~= ARGV[7] and fieldName ~= ARGV[6] then -- Exclude mode and writer fields
            local readerCount = tonumber(redis.call('hget', KEYS[1], fieldName));
            if readerCount ~= nil and readerCount > 0 then
                activeElements = true;
                for k=1, readerCount do
                    -- Construct general timeout key for this other reader's instance
                    local otherReaderTimeoutKey = KEYS[4] .. ':' .. fieldName .. ':' .. k; 
                    local pttl = redis.call('pttl', otherReaderTimeoutKey);
                    if pttl > maxRemainTime then
                        maxRemainTime = pttl;
                    end;
                end;
            end;
        end;
    end;
end;

if not activeElements then
    redis.call('del', KEYS[1]); -- Delete main lock hash
elseif maxRemainTime > 0 then
    redis.call('pexpire', KEYS[1], maxRemainTime);
elseif redis.call('pttl', KEYS[1]) < 0 then -- No positive PTTL from active elements, ensure cleanup
     redis.call('del', KEYS[1]);
end;

-- Publish unlock message
local key1_parts_pub = {}
for part_pub in string.gmatch(KEYS[1], "[^:]+") do table.insert(key1_parts_pub, part_pub) end
local lockNameSuffix_pub = key1_parts_pub[#key1_parts_pub] 
redis.call(ARGV[3], KEYS[2] .. ':' .. lockNameSuffix_pub, ARGV[4]);

if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '1', 'px', tonumber(ARGV[5]) * 1000); end;
return 1;