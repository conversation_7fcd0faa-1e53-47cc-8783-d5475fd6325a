# Redis Locking Module: Migration Guide

## 1. Introduction

This guide provides step-by-step instructions for migrating between different versions of the Redis locking module. It includes code examples, configuration changes, and best practices to ensure smooth transitions.

For version history and breaking changes, see [Changelog](changelog.md). For current configuration options, see [Configuration](../02_USAGE_AND_CONFIGURATION/configuration.md).

## 2. Migration Overview

### 2.1. Migration Complexity Matrix

| From Version | To Version | Complexity | Effort | Risk | Rollback |
|--------------|------------|------------|--------|------|----------|
| 1.x → 2.0.x  | High       | High       | High   | Medium | Easy |
| 2.0.x → 2.1.x| Low        | Low        | Low    | Low    | Easy |
| 1.x → 2.1.x  | High       | High       | High   | Medium | Easy |

### 2.2. General Migration Strategy

1. **Assessment Phase**: Analyze current usage and identify required changes
2. **Preparation Phase**: Update dependencies and configuration
3. **Code Migration Phase**: Update application code to new APIs
4. **Testing Phase**: Comprehensive testing of migrated functionality
5. **Deployment Phase**: Gradual rollout with monitoring
6. **Validation Phase**: Verify functionality and performance

## 3. Migration from 1.x to 2.x

### 3.1. Overview

Version 2.0 introduced a complete API redesign with breaking changes. This migration requires significant code changes but provides improved functionality and performance.

**Key Changes**:
- New builder pattern API
- Reorganized package structure
- New configuration properties
- Enhanced lock types and features
- Improved error handling

### 3.2. Pre-Migration Checklist

- [ ] Backup existing Redis data
- [ ] Document current lock usage patterns
- [ ] Identify all code locations using the locking module
- [ ] Plan for application downtime during migration
- [ ] Set up rollback procedure

### 3.3. Dependency Updates

**Maven Dependencies**:

```xml
<!-- Before (1.x) -->
<dependency>
    <groupId>com.tui.destilink.framework</groupId>
    <artifactId>locking-redis</artifactId>
    <version>1.2.3</version>
</dependency>

<!-- After (2.x) -->
<dependency>
    <groupId>com.tui.destilink.framework</groupId>
    <artifactId>locking-redis-lock</artifactId>
    <version>2.1.0</version>
</dependency>
```

**Gradle Dependencies**:

```gradle
// Before (1.x)
implementation 'com.tui.destilink.framework:locking-redis:1.2.3'

// After (2.x)
implementation 'com.tui.destilink.framework:locking-redis-lock:2.1.0'
```

### 3.4. Configuration Migration

**Application Properties**:

```yaml
# Before (1.x)
locking:
  redis:
    enabled: true
    lease-time: 60s
    retry-attempts: 3
    retry-interval: 100ms

# After (2.x)
destilink:
  fw:
    locking:
      redis:
        enabled: true
        defaults:
          lease-time: PT60S
          max-retries: 3
          retry-interval: PT0.1S
          acquire-timeout: PT5S
        watchdog:
          enabled: true
          interval: PT5S
```

**Configuration Class Migration**:

```java
// Before (1.x)
@Configuration
@EnableRedisLocking
public class LockingConfig {
    
    @Bean
    public LockManager lockManager(RedisTemplate<String, String> redisTemplate) {
        return new RedisLockManager(redisTemplate);
    }
}

// After (2.x) - Auto-configuration handles this
// Remove custom configuration class if only using defaults
// Or customize specific beans:
@Configuration
public class LockingConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public LockOwnerSupplier customLockOwnerSupplier() {
        return new CustomLockOwnerSupplier();
    }
}
```

### 3.5. Code Migration

**Basic Lock Usage**:

```java
// Before (1.x)
@Service
public class UserService {
    
    @Autowired
    private LockManager lockManager;
    
    public void updateUser(String userId) {
        Lock lock = lockManager.getLock("user-" + userId);
        try {
            lock.acquire(Duration.ofSeconds(30));
            // Critical section
        } finally {
            lock.release();
        }
    }
}

// After (2.x)
@Service
@RequiredArgsConstructor
public class UserService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void updateUser(String userId) {
        var lock = lockBucketRegistry.builder("user-operations")
            .lockConfig("user-" + userId)
            .reentrantLock()
            .withLeaseTime(Duration.ofSeconds(30))
            .build();
            
        if (lock.tryAcquire()) {
            try {
                // Critical section
            } finally {
                lock.release();
            }
        }
    }
}
```

**Advanced Lock Configuration**:

```java
// Before (1.x)
@Service
public class OrderService {
    
    @Autowired
    private LockManager lockManager;
    
    public void processOrder(String orderId) {
        LockConfig config = LockConfig.builder()
            .leaseTime(Duration.ofMinutes(5))
            .retryAttempts(5)
            .retryInterval(Duration.ofMillis(200))
            .build();
            
        Lock lock = lockManager.getLock("order-" + orderId, config);
        // ... rest of the logic
    }
}

// After (2.x)
@Service
@RequiredArgsConstructor
public class OrderService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void processOrder(String orderId) {
        var lock = lockBucketRegistry.builder("order-processing")
            .withDefaultLeaseTime(Duration.ofMinutes(5))
            .withDefaultMaxRetries(5)
            .withDefaultRetryInterval(Duration.ofMillis(200))
            .lockConfig("order-" + orderId)
            .reentrantLock()
            .build();
            
        if (lock.tryAcquire()) {
            try {
                // Critical section
            } finally {
                lock.release();
            }
        }
    }
}
```

**Error Handling Migration**:

```java
// Before (1.x)
try {
    lock.acquire(Duration.ofSeconds(10));
    // Critical section
} catch (LockAcquisitionException e) {
    log.error("Failed to acquire lock", e);
} catch (LockTimeoutException e) {
    log.warn("Lock acquisition timed out", e);
} finally {
    try {
        lock.release();
    } catch (LockReleaseException e) {
        log.error("Failed to release lock", e);
    }
}

// After (2.x)
var lock = lockBucketRegistry.builder("my-bucket")
    .lockConfig("my-lock")
    .reentrantLock()
    .withAcquireTimeout(Duration.ofSeconds(10))
    .build();
    
try {
    if (lock.tryAcquire()) {
        try {
            // Critical section
        } finally {
            lock.release();
        }
    } else {
        log.warn("Failed to acquire lock within timeout");
    }
} catch (RedisLockException e) {
    log.error("Lock operation failed", e);
}
```

### 3.6. Testing Migration

**Unit Test Updates**:

```java
// Before (1.x)
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private LockManager lockManager;
    
    @Mock
    private Lock lock;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void shouldUpdateUser() {
        when(lockManager.getLock(anyString())).thenReturn(lock);
        when(lock.acquire(any(Duration.class))).thenReturn(true);
        
        userService.updateUser("user123");
        
        verify(lock).acquire(Duration.ofSeconds(30));
        verify(lock).release();
    }
}

// After (2.x)
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private LockBucketRegistry lockBucketRegistry;
    
    @Mock
    private LockBucketBuilder lockBucketBuilder;
    
    @Mock
    private LockConfigBuilder lockConfigBuilder;
    
    @Mock
    private ReentrantLockConfigBuilder reentrantLockBuilder;
    
    @Mock
    private ReentrantLock lock;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void shouldUpdateUser() {
        when(lockBucketRegistry.builder("user-operations")).thenReturn(lockBucketBuilder);
        when(lockBucketBuilder.lockConfig("user-user123")).thenReturn(lockConfigBuilder);
        when(lockConfigBuilder.reentrantLock()).thenReturn(reentrantLockBuilder);
        when(reentrantLockBuilder.withLeaseTime(any())).thenReturn(reentrantLockBuilder);
        when(reentrantLockBuilder.build()).thenReturn(lock);
        when(lock.tryAcquire()).thenReturn(true);
        
        userService.updateUser("user123");
        
        verify(lock).tryAcquire();
        verify(lock).release();
    }
}
```

**Integration Test Updates**:

```java
// Before (1.x)
@SpringBootTest
@TestPropertySource(properties = {
    "locking.redis.enabled=true"
})
class LockingIntegrationTest {
    
    @Autowired
    private LockManager lockManager;
    
    @Test
    void shouldAcquireAndReleaseLock() {
        Lock lock = lockManager.getLock("test-lock");
        assertTrue(lock.acquire(Duration.ofSeconds(5)));
        lock.release();
    }
}

// After (2.x)
@SpringBootTest
@TestPropertySource(properties = {
    "destilink.fw.locking.redis.enabled=true"
})
class LockingIntegrationTest {
    
    @Autowired
    private LockBucketRegistry lockBucketRegistry;
    
    @Test
    void shouldAcquireAndReleaseLock() {
        var lock = lockBucketRegistry.builder("test-bucket")
            .lockConfig("test-lock")
            .reentrantLock()
            .withAcquireTimeout(Duration.ofSeconds(5))
            .build();
            
        assertTrue(lock.tryAcquire());
        lock.release();
    }
}
```

### 3.7. Data Migration

**Redis Key Migration**:

The new version uses a different key schema. If you need to preserve existing locks during migration:

```bash
# Script to migrate Redis keys from 1.x to 2.x format
#!/bin/bash

# Backup existing keys
redis-cli --scan --pattern "lock:*" > old_keys_backup.txt

# Migrate keys (example - adjust based on your key patterns)
for key in $(redis-cli --scan --pattern "lock:*"); do
    value=$(redis-cli get "$key")
    ttl=$(redis-cli ttl "$key")
    
    # Convert to new format: dl:lock:bucket:key
    new_key="dl:lock:default:${key#lock:}"
    
    if [ "$ttl" -gt 0 ]; then
        redis-cli setex "$new_key" "$ttl" "$value"
    else
        redis-cli set "$new_key" "$value"
    fi
done

# Verify migration
echo "Old keys count: $(redis-cli --scan --pattern 'lock:*' | wc -l)"
echo "New keys count: $(redis-cli --scan --pattern 'dl:lock:*' | wc -l)"
```

**⚠️ Warning**: Test this migration script thoroughly in a non-production environment first.

### 3.8. Deployment Strategy

**Blue-Green Deployment**:

1. **Prepare Green Environment**:
   - Deploy new version to green environment
   - Run migration scripts
   - Perform comprehensive testing

2. **Switch Traffic**:
   - Gradually route traffic to green environment
   - Monitor for issues
   - Keep blue environment ready for rollback

3. **Cleanup**:
   - After successful validation, decommission blue environment
   - Clean up old Redis keys if migration was successful

**Rolling Deployment**:

1. **Phase 1**: Deploy to subset of instances
2. **Phase 2**: Monitor and validate functionality
3. **Phase 3**: Gradually roll out to remaining instances
4. **Phase 4**: Complete rollout and cleanup

## 4. Migration from 2.0.x to 2.1.x

### 4.1. Overview

This is a minor version upgrade with backward compatibility. Most changes are additive with some configuration enhancements.

**Key Changes**:
- New state lock and semaphore lock types
- Enhanced configuration options
- Improved monitoring and metrics
- Performance optimizations

### 4.2. Dependency Updates

```xml
<!-- Update version -->
<dependency>
    <groupId>com.tui.destilink.framework</groupId>
    <artifactId>locking-redis-lock</artifactId>
    <version>2.1.0</version>
</dependency>
```

### 4.3. Configuration Enhancements

**Optional New Configuration**:

```yaml
destilink:
  fw:
    locking:
      redis:
        # New in 2.1.0 - all optional
        state-key-expiration: PT5M
        uuid-cache-ttl-seconds: 300
        health-indicator-enabled: true
        unlock-message-listener-enabled: true
        
        # Enhanced watchdog configuration
        watchdog:
          min-lease-time-for-activation: PT10S
          max-ttl-for-renewal-check: PT15S
          shutdown-await-termination: PT30S
```

### 4.4. New Features Usage

**State Locks** (New in 2.1.0):

```java
// New state lock functionality
var stateLock = lockBucketRegistry.builder("application-state")
    .lockConfig("deployment-status")
    .stateLock()
    .withStateKeyExpiration(Duration.ofMinutes(10))
    .build();

if (stateLock.tryAcquire("DEPLOYING")) {
    try {
        // Deployment logic
        stateLock.updateState("DEPLOYED");
    } finally {
        stateLock.release();
    }
}
```

**Semaphore Locks** (New in 2.1.0):

```java
// New semaphore lock functionality
var semaphoreLock = lockBucketRegistry.builder("resource-pool")
    .lockConfig("database-connections")
    .semaphoreLock()
    .withPermits(10)
    .build();

if (semaphoreLock.tryAcquire()) {
    try {
        // Use database connection
    } finally {
        semaphoreLock.release();
    }
}
```

### 4.5. Monitoring Enhancements

**New Metrics** (Available in 2.1.0):

```java
// Custom metrics integration
@Component
@RequiredArgsConstructor
public class BusinessMetrics {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void onLockAcquisition(LockAcquisitionEvent event) {
        Counter.builder("business.locks.acquired")
            .tag("operation", event.getOperationType())
            .register(meterRegistry)
            .increment();
    }
}
```

## 5. Common Migration Issues and Solutions

### 5.1. Compilation Errors

**Issue**: Import statements not found
```java
// Error: Cannot resolve symbol 'LockManager'
import com.tui.destilink.framework.locking.LockManager;
```

**Solution**: Update imports
```java
// Correct import for 2.x
import com.tui.destilink.framework.locking.redis.service.LockBucketRegistry;
```

**Issue**: Method not found
```java
// Error: Cannot resolve method 'acquire(Duration)'
lock.acquire(Duration.ofSeconds(30));
```

**Solution**: Use new API
```java
// Correct usage in 2.x
if (lock.tryAcquire()) {
    // Critical section
}
```

### 5.2. Runtime Issues

**Issue**: Configuration properties not loaded
```
Property 'locking.redis.enabled' not found
```

**Solution**: Update property names
```yaml
# Change from:
locking.redis.enabled: true

# To:
destilink.fw.locking.redis.enabled: true
```

**Issue**: Bean not found
```
No qualifying bean of type 'LockManager'
```

**Solution**: Update dependency injection
```java
// Change from:
@Autowired
private LockManager lockManager;

// To:
@Autowired
private LockBucketRegistry lockBucketRegistry;
```

### 5.3. Performance Issues

**Issue**: Increased lock acquisition latency

**Investigation**:
1. Check new configuration defaults
2. Verify Redis connection pool settings
3. Monitor watchdog performance

**Solution**:
```yaml
destilink:
  fw:
    locking:
      redis:
        defaults:
          redis-operation-timeout: PT0.2S  # Reduce if needed
        watchdog:
          interval: PT3S  # Adjust based on requirements
```

## 6. Migration Testing Strategy

### 6.1. Test Plan Template

**Phase 1: Unit Testing**
- [ ] All lock operations work with new API
- [ ] Error handling behaves correctly
- [ ] Configuration is properly loaded
- [ ] Mock objects work with new interfaces

**Phase 2: Integration Testing**
- [ ] Redis connectivity works
- [ ] Lock acquisition and release function correctly
- [ ] Watchdog operates as expected
- [ ] Metrics are collected properly

**Phase 3: Performance Testing**
- [ ] Lock acquisition latency is acceptable
- [ ] Throughput meets requirements
- [ ] Memory usage is stable
- [ ] No resource leaks detected

**Phase 4: End-to-End Testing**
- [ ] Full application functionality works
- [ ] Business operations complete successfully
- [ ] Error scenarios are handled gracefully
- [ ] Monitoring and alerting function correctly

### 6.2. Rollback Testing

**Rollback Scenarios**:
1. **Configuration Rollback**: Revert to old configuration format
2. **Code Rollback**: Revert to previous version with old API
3. **Data Rollback**: Restore Redis keys to old format
4. **Full Rollback**: Complete environment restoration

**Rollback Validation**:
- [ ] Application starts successfully
- [ ] All functionality works as before
- [ ] No data loss occurred
- [ ] Performance is restored

## 7. Post-Migration Validation

### 7.1. Functional Validation

**Checklist**:
- [ ] All critical business operations work
- [ ] Lock acquisition success rate > 99%
- [ ] No lock leaks detected
- [ ] Error handling works correctly
- [ ] Monitoring shows healthy metrics

### 7.2. Performance Validation

**Metrics to Monitor**:
- Lock acquisition latency (P95 < 50ms)
- Lock acquisition success rate (> 99%)
- Redis operation latency (P95 < 10ms)
- Memory usage stability
- CPU usage patterns

### 7.3. Operational Validation

**Checklist**:
- [ ] Health checks pass
- [ ] Metrics are being collected
- [ ] Alerts are configured and working
- [ ] Logs contain expected information
- [ ] Documentation is updated

## 8. Migration Support

### 8.1. Getting Help

**Internal Support**:
- Framework team documentation
- Internal Slack channels
- Architecture review sessions

**External Resources**:
- Redis documentation
- Spring Boot migration guides
- Community forums

### 8.2. Common Questions

**Q: Can I run both versions simultaneously?**
A: Not recommended. The Redis key schemas are different and may conflict.

**Q: How long does migration typically take?**
A: For 1.x → 2.x: 1-2 weeks for medium applications. For 2.0.x → 2.1.x: 1-2 days.

**Q: What if I encounter issues during migration?**
A: Follow the rollback procedures and contact the framework team for support.

**Q: Are there any data loss risks?**
A: Minimal if proper backup and testing procedures are followed.

This migration guide provides comprehensive instructions for upgrading between versions of the Redis locking module. Always test migrations thoroughly in non-production environments before applying to production systems.