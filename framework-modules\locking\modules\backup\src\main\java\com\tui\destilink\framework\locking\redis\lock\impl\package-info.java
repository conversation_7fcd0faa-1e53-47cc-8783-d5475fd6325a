/**
 * Internal implementation details for Redis-based locking mechanism.
 * 
 * This package contains implementation classes that are not intended for direct
 * use
 * by clients. These classes implement the service interfaces defined in the
 * service
 * package and provide the actual functionality for Redis-based distributed
 * locking.
 */
package com.tui.destilink.framework.locking.redis.lock.impl;