-- KEYS[1] mainLockKey (hash, e.g., myApp:__lock_buckets__:resource:__locks__:{myRWLock})
-- KEYS[2] readWriteTimeoutNamePrefix (base for individual read timeout keys for this reader, e.g., myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerIdFromArgv>)
-- ARGV[1] leaseTimeMs (for this specific read lock instance)
-- ARGV[2] readerId (current thread's read lock name, used as field in KEYS[1] and part of KEYS[2])
-- ARGV[3] writerId (current thread's write lock name, to check if it holds the write lock)

local mode = redis.call('hget', KEYS[1], 'mode');
if (mode == false) then
    redis.call('hset', KEYS[1], 'mode', 'read');
    redis.call('hset', KEYS[1], ARGV[2], 1); -- Store readerId as field, count as value
    redis.call('set', KEYS[2] .. ':1', 1, 'px', ARGV[1]); -- Individual timeout key for this first instance
    redis.call('pexpire', KEYS[1], ARGV[1]); -- Expire main hash
    return nil; -- Lock acquired
end;

if (mode == 'read') or (mode == 'write' and redis.call('hexists', KEYS[1], ARGV[3]) == 1) then
    local ind = redis.call('hincrby', KEYS[1], ARGV[2], 1); -- Increment reentrant count for this reader
    local individualTimeoutKey = KEYS[2] .. ':' .. ind;
    redis.call('set', individualTimeoutKey, 1, 'px', ARGV[1]); -- Set TTL for this specific reentrant acquisition
    
    local mainKeyCurrentPttl = redis.call('pttl', KEYS[1]);
    if mainKeyCurrentPttl < 0 then mainKeyCurrentPttl = 0 end; -- Treat expired or non-existent as 0 for max
    redis.call('pexpire', KEYS[1], math.max(mainKeyCurrentPttl, tonumber(ARGV[1]))); -- Ensure main hash lives at least as long as this new read
    return nil; -- Lock acquired reentrantly or by write lock holder
end;

return redis.call('pttl', KEYS[1]); -- Write lock held by another, return its PTTL