package com.tui.destilink.framework.locking.redis.lock.service;

import java.util.regex.Pattern;

/**
 * An interface responsible for providing a unique identifier for the current
 * lock owner.
 * <p>
 * This interface is used by the locking mechanism to identify the owner of a
 * lock,
 * typically combining an application instance ID and a thread ID. The default
 * implementation
 * is provided by {@code DefaultLockOwnerSupplier} bean.
 * </p>
 * <p>
 * The owner ID is crucial for distributed locking as it helps determine if a
 * lock
 * is held by the current application instance and thread, enabling features
 * like
 * reentrancy and watchdog lease extension.
 * </p>
 */
public interface LockOwnerSupplier {

    /**
     * Returns a unique identifier for the current lock owner.
     * <p>
     * The implementation should typically combine an application instance ID and a
     * thread ID
     * to create a unique identifier that can be used to determine lock ownership
     * across
     * a distributed system.
     * </p>
     *
     * @return a unique owner ID string
     */
    String get();

    /**
     * Determines if the watchdog can be used for the specific lock instance.
     * <p>
     * This method is crucial for determining watchdog eligibility for locks. The
     * watchdog
     * is responsible for automatically extending the lease of active,
     * application-instance-bound
     * locks in Redis to prevent premature expiration.
     * </p>
     * <p>
     * Implementations should consider factors such as:
     * <ul>
     * <li>Whether the lock is application-instance-bound</li>
     * <li>Whether the lock has a lease time that exceeds a minimum threshold</li>
     * <li>Whether the lock is eligible for automatic lease extension</li>
     * </ul>
     * </p>
     *
     * @param lockIdentifier the unique identifier of the lock
     * @param ownerId        the owner ID of the lock
     * @return {@code true} if the watchdog can be used for this lock, {@code false}
     *         otherwise
     */
    boolean canUseWatchdog(String lockIdentifier, String ownerId);

    /**
     * Regular expression for validating lock owner IDs.
     * <p>
     * Breakdown:
     * <ul>
     * <li>{@code ^[a-zA-Z0-9]}: Starts with an alphanumeric character.</li>
     * <li>{@code [a-zA-Z0-9_\\.-]*}: Contains zero or more alphanumeric characters,
     * underscores, hyphens, or periods.</li>
     * <li>{@code [a-zA-Z0-9]$}: Ends with an alphanumeric character.</li>
     * <li>Combined with length check: {@code {1,100}} in practical validation,
     * though regex itself doesn't enforce length directly here.
     * The actual length validation (1 to 100 characters) is typically handled
     * separately after the pattern match,
     * or could be incorporated using lookaheads if a single regex solution is
     * strictly needed,
     * e.g., {@code ^(?=.{1,100}$)[a-zA-Z0-9][a-zA-Z0-9_\\.-]*[a-zA-Z0-9]$}
     * However, for clarity and performance, separating pattern and length checks is
     * often preferred.
     * This regex focuses on character validity and start/end constraints.
     * </li>
     * </ul>
     * This specific regex ensures the ID starts and ends with an alphanumeric
     * character,
     * and only contains alphanumeric characters, underscores, hyphens, or periods
     * in between.
     * The length constraint (1-100 characters) should be checked separately for
     * robustness.
     * For a single regex enforcing length:
     * {@code ^(?=.{1,100}$)[a-zA-Z0-9](?:[a-zA-Z0-9_.-]*[a-zA-Z0-9])?$}
     * This version handles single character IDs correctly as well.
     * </p>
     */
    Pattern LOCK_OWNER_ID_PATTERN = Pattern.compile("^(?=.{1,100}$)[a-zA-Z0-9](?:[a-zA-Z0-9_.-]*[a-zA-Z0-9])?$");
}