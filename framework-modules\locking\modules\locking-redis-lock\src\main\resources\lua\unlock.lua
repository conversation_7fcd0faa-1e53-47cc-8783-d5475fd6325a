-- Redis Unlock Script with Idempotency
-- This script atomically releases a lock with idempotency support
--
-- KEYS[1] - The main lock key (format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>})
-- KEYS[2] - The lock data key (Redis Hash for metadata)
-- KEYS[3] - The response cache key for idempotency
-- KEYS[4] - The unlock notification channel
--
-- ARGV[1] - Request UUID for idempotency
-- ARGV[2] - Response cache TTL in seconds
-- ARGV[3] - Lock owner ID
-- ARGV[4] - Lock type (e.g., "reentrant", "stamped", "state")

local lockKey = KEYS[1]
local lockDataKey = KEYS[2]
local responseCacheKey = KEYS[3]
local unlockChannel = KEYS[4]

local requestUuid = ARGV[1]
local responseCacheTtl = tonumber(ARGV[2])
local ownerId = ARGV[3]
local lockType = ARGV[4]

-- ========================================
-- IDEMPOTENCY CHECK
-- ========================================
-- Check if this operation was already executed
local cachedResponse = redis.call('GET', responseCacheKey)
if cachedResponse then
    -- Operation already completed, return cached result
    local result = cjson.decode(cachedResponse)
    return result
end

-- ========================================
-- CORE UNLOCK LOGIC
-- ========================================
local currentOwner = redis.call('GET', lockKey)
local statusCode = 0  -- Default: not unlocked
local unlocked = false

if currentOwner == ownerId then
    -- Lock is held by the requesting owner, release it
    redis.call('DEL', lockKey)
    redis.call('DEL', lockDataKey)

    -- Publish unlock notification for waiting clients
    redis.call('PUBLISH', unlockChannel, lockType)

    statusCode = 1  -- Successfully unlocked
    unlocked = true

elseif currentOwner == false then
    -- Lock doesn't exist (already unlocked or expired)
    statusCode = 0  -- Not unlocked (already released)

else
    -- Lock is held by a different owner
    statusCode = -1  -- Not unlocked (not owner)
end

-- ========================================
-- PREPARE STRUCTURED RESPONSE
-- ========================================
local response = {
    statusCode,
    unlocked and 1 or 0,  -- 1 if unlocked, 0 otherwise
    0  -- No expiration info for unlock operations
}

-- ========================================
-- CACHE RESPONSE FOR IDEMPOTENCY
-- ========================================
-- Store the response in cache for future idempotency checks
redis.call('SETEX', responseCacheKey, responseCacheTtl, cjson.encode(response))

-- TODO: Step 9.6 - Return structured response when infrastructure supports it
-- Structured format: {statusCode, unlocked, expirationInfo}
-- For now, return just the status code for compatibility
return statusCode