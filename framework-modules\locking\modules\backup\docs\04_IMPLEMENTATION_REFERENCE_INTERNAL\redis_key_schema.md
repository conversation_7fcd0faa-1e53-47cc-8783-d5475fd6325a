# Redis Locking Module: Redis Key Schema

## 1. Introduction

The Redis locking module uses a structured and consistent key naming scheme to organize lock data, ensure proper isolation, and enable efficient operations. This document provides a comprehensive reference for all Redis key patterns used by the module, incorporating the new lock-type segments for enhanced functionality and semantic isolation.

## 2. New Redis Key Format

### 2.1 Key Components

The new Redis key format follows this structure:

```
<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}
```

Components:
- `<prefix>`: Application-wide prefix (e.g., `myApp`)
- `<bucketName>`: Logical grouping (e.g., `orders`)
- `__locks__`: Fixed segment indicating lock-related key
- `<lockType>`: Mandatory lock type identifier
- `{<lockName>}`: Actual lock name (curly braces for Redis Cluster compatibility)

### 2.2 Lock-Type Segments

The module supports four mandatory lock-type segments:

1. `reentrant`: Traditional mutual exclusion locks
2. `stamped`: High-performance read-write locks
3. `state`: Application state management locks
4. `semaphore`: Resource counting locks

## 3. Semantic Isolation

The introduction of lock-type segments in the key format provides semantic isolation between different lock types. This isolation ensures that:

1. Operations specific to one lock type cannot accidentally affect locks of another type.
2. It's impossible to acquire a lock of one type and release it as another type.
3. Monitoring and debugging become easier as lock types are clearly distinguished in the key structure.

For example, a reentrant lock operation will never interfere with a semaphore lock, even if they share the same bucket and lock name, because the lock-type segment differentiates them.

## 4. Redis Cluster Compatibility

The use of curly braces `{}` around the `<lockName>` portion of the key ensures Redis Cluster compatibility. This design guarantees that all keys related to a specific lock are hashed to the same slot in Redis Cluster, allowing for atomic multi-key operations within the same `{lockName}`. This is crucial for maintaining lock integrity in a distributed environment.

## 5. Key Patterns by Lock Type

### 5.1 Reentrant Lock

```
# Primary Lock Key
myApp:orders:__locks__:reentrant:{order-123}

# Lock Metadata Key
myApp:orders:__locks__:reentrant:meta:{order-123}
```

### 5.2 Stamped Lock

```
# Primary Lock Key
myApp:inventory:__locks__:stamped:{product-abc}

# Read Lock Counter Key
myApp:inventory:__locks__:stamped:read-count:{product-abc}

# Write Lock Key
myApp:inventory:__locks__:stamped:write:{product-abc}
```

### 5.3 State Lock

```
# Primary Lock Key
myApp:workflow:__locks__:state:{process-xyz}

# State Storage Key
myApp:workflow:__locks__:state:storage:{process-xyz}

# State History Key
myApp:workflow:__locks__:state:history:{process-xyz}
```

### 5.4 Semaphore Lock

```
# Semaphore Counter Key
myApp:connections:__locks__:semaphore:{pool-main}

# Semaphore Owner Tracking Key
myApp:connections:__locks__:semaphore:owners:{pool-main}
```

## 6. Idempotency Key Patterns

Idempotency keys now incorporate the lock-type segment for consistency and improved organization:

```
myApp:__locks__:idempotency:<lockType>:<operation>:{<hash>}
```

Examples:
```
myApp:__locks__:idempotency:reentrant:ACQUIRE_LOCK:{sha256-abc123}
myApp:__locks__:idempotency:stamped:RELEASE_READ:{sha256-def456}
myApp:__locks__:idempotency:state:TRANSITION:{sha256-ghi789}
myApp:__locks__:idempotency:semaphore:ACQUIRE_PERMIT:{sha256-jkl012}
```

## 7. LockKey and LockDataKey Relationship

The `lockKey` and `lockDataKey` maintain a consistent relationship in the new format:

- `lockKey`: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`
- `lockDataKey`: `<prefix>:<bucketName>:__locks__:<lockType>:meta:{<lockName>}`

The `lockDataKey` is derived from the `lockKey` by inserting `:meta:` before the `{<lockName>}` segment. This consistent relationship allows for easy lookup of metadata associated with a specific lock.

## 8. Key Lifecycle Management

### 8.1 TTL Management

All lock-related keys should have appropriate TTL (Time-To-Live) settings:

```java
@Component
@RequiredArgsConstructor
public class KeyTtlManager {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisLockProperties properties;
    
    public void setLockTtl(String lockKey, Duration ttl) {
        redisTemplate.expire(lockKey, ttl);
    }
    
    public void setStateTtl(String stateKey, Duration ttl) {
        Duration stateTtl = properties.getStateKeyExpiration();
        redisTemplate.expire(stateKey, stateTtl);
    }
    
    public void setIdempotencyTtl(String idempotencyKey, Duration ttl) {
        Duration idempotencyTtl = Duration.ofSeconds(properties.getUuidCacheTtlSeconds());
        redisTemplate.expire(idempotencyKey, idempotencyTtl);
    }
    
    public void extendTtl(String key, Duration extension) {
        Long currentTtl = redisTemplate.getExpire(key, TimeUnit.MILLISECONDS);
        if (currentTtl != null && currentTtl > 0) {
            Duration newTtl = Duration.ofMillis(currentTtl).plus(extension);
            redisTemplate.expire(key, newTtl);
        }
    }
}
```

### 8.2 Key Cleanup Strategies

#### 8.2.1 Automatic Expiration

Set TTL for automatic cleanup:

- Lock Keys: Set to lease time + safety margin
- State Keys: Configurable expiration (default: 5 minutes)
- Idempotency Keys: Short TTL (default: 5 minutes)
- Metadata Keys: Longer TTL for historical data

#### 8.2.2 Manual Cleanup

Implement a cleanup service for expired keys:

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class KeyCleanupService {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final LuaScriptRegistry scriptRegistry;
    
    @Scheduled(fixedDelay = 300000) // Every 5 minutes
    public void cleanupExpiredKeys() {
        String pattern = "myApp:*:__locks__:*";
        
        List<Object> result = redisTemplate.execute(
            scriptRegistry.getScript("cleanup_expired_locks"),
            Collections.singletonList(pattern),
            "100", // batch size
            String.valueOf(System.currentTimeMillis())
        );
        
        if (result != null && result.size() >= 3) {
            Boolean success = (Boolean) result.get(0);
            if (success) {
                Map<String, Object> stats = (Map<String, Object>) result.get(2);
                log.info("Cleaned up {} expired lock keys (scanned: {})",
                        stats.get("cleaned"), stats.get("scanned"));
            }
        }
    }
}
```

## 9. Monitoring and Debugging

### 9.1 Key Inspection Tools

Implement a service for inspecting lock keys:

```java
@Component
@RequiredArgsConstructor
public class KeyInspectionService {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    public Map<String, Object> inspectLockKey(String lockKey) {
        Map<String, Object> info = new HashMap<>();
        
        info.put("exists", redisTemplate.hasKey(lockKey));
        info.put("ttl", redisTemplate.getExpire(lockKey, TimeUnit.SECONDS));
        info.put("value", redisTemplate.opsForValue().get(lockKey));
        
        String metaKey = buildMetaKey(lockKey);
        info.put("metaKey", Map.of(
            "key", metaKey,
            "exists", redisTemplate.hasKey(metaKey),
            "value", redisTemplate.opsForValue().get(metaKey)
        ));
        
        return info;
    }
    
    private String buildMetaKey(String lockKey) {
        List<String> segments = new ArrayList<>(Arrays.asList(lockKey.split(":")));
        segments.add(segments.size() - 1, "meta");
        return String.join(":", segments);
    }
}
```

### 9.2 Key Metrics and Monitoring

Implement a metrics collector for Redis keys:

```java
@Component
@RequiredArgsConstructor
public class KeyMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final RedisTemplate<String, String> redisTemplate;
    
    @Scheduled(fixedDelay = 60000) // Every minute
    public void collectKeyMetrics() {
        Map<String, Long> stats = getKeyStatistics();
        
        stats.forEach((keyType, count) -> {
            Gauge.builder("locking.redis.keys")
                .tag("type", keyType)
                .description("Number of Redis keys by type")
                .register(meterRegistry)
                .set(count);
        });
    }
    
    private Map<String, Long> getKeyStatistics() {
        Map<String, Long> stats = new HashMap<>();
        
        stats.put("reentrant", countKeysByPattern("*:__locks__:reentrant:*"));
        stats.put("stamped", countKeysByPattern("*:__locks__:stamped:*"));
        stats.put("state", countKeysByPattern("*:__locks__:state:*"));
        stats.put("semaphore", countKeysByPattern("*:__locks__:semaphore:*"));
        
        return stats;
    }
    
    private Long countKeysByPattern(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        return keys != null ? (long) keys.size() : 0L;
    }
}
```

This comprehensive rewrite of the Redis key schema documentation incorporates all the required changes, including the new key format with lock-type segments, semantic isolation explanation, Redis Cluster compatibility, and updated examples for all lock types. The document now provides a clear and consistent reference for the Redis locking module's key structure and management.