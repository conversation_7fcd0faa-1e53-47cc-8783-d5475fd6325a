package com.tui.destilink.framework.locking.redis.lock.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Supplier;

/**
 * Defines the global default configuration properties for the Redis-based
 * distributed
 * locking module. These settings serve as the base configuration and can be
 * overridden at more specific levels (e.g., by programmatic lock builders) to
 * provide fine-grained control.
 * <p>
 * The configuration follows a clear precedence: Instance-specific settings (via
 * builders) > Programmatic Bucket Configuration (via builders) > Global
 * Configuration (this class).
 *
 * @see com.tui.destilink.framework.locking.redis.lock.docs.configuration
 */
@Getter
@ToString
@RequiredArgsConstructor // For final fields, Spring Boot will use this for property binding
@Validated
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
public class RedisLockProperties {

    /** Whether the Redis locking mechanism is enabled. */
    private final boolean enabled;

    /** The expiration time for the state key. */
    @NotNull
    private final Duration stateKeyExpiration;

    /**
     * Default Redis lock properties, applied to all locks unless overridden at the
     * bucket or individual lock level.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty // Ensures this nested object is treated as a configuration property group
    private final Defaults defaults;

    /**
     * Configuration for the lock watchdog mechanism.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty // Ensures this nested object is treated as a configuration property group
    private final WatchdogProperties watchdog;

    /**
     * Defines the Time-To-Live (TTL) for idempotency records in Redis. These
     * records are stored in a response
     * cache, keyed by a unique request UUID. This mechanism ensures that if a
     * client retries an operation
     * (e.g., lock, unlock) due to a lost response, the operation is not executed
     * multiple times if the
     * original attempt succeeded. The TTL should be long enough to cover typical
     * client retry windows.
     * <p>
     * The idempotency mechanism, including the generation of the request UUID and
     * management of the response
     * cache, is handled within {@code RedisLockOperationsImpl} and the underlying
     * Lua scripts.
     */
    @NotNull
    private final Duration responseCacheTtl;

    /** Supplier for the retry executor service. */
    private final Supplier<ScheduledExecutorService> retryExecutorSupplier = () -> Executors
            .newSingleThreadScheduledExecutor(r -> {
                Thread thread = new Thread(r, "redis-lock-retry-scheduler");
                thread.setDaemon(true);
                return thread;
            });

    /**
     * Gets the retry executor service.
     *
     * @return The ScheduledExecutorService for retry operations.
     */
    public ScheduledExecutorService getRetryExecutor() {
        return retryExecutorSupplier.get();
    }

    /**
     * Gets the Time-To-Live (TTL) for idempotency records in Redis.
     * These records ensure that retried operations (e.g., due to lost responses)
     * are not executed multiple times
     * if the original attempt succeeded. The TTL should cover typical client retry
     * windows.
     *
     * @return the response cache TTL.
     */
    public Duration getResponseCacheTtl() {
        return responseCacheTtl;
    }

    /**
     * Default properties for lock implementations. These values can be overridden
     * by lock-specific builders. Configured under
     * 'destilink.fw.locking.redis.defaults'.
     *
     * @see com.tui.destilink.framework.locking.redis.lock.docs.configuration
     */
    @Getter
    @ToString
    @RequiredArgsConstructor // For final fields, Spring Boot will use this for property binding
    @Validated // Added for validation of the properties within this nested class
    @ConfigurationProperties(prefix = "destilink.fw.locking.redis.defaults") // Added to specify the prefix for these
                                                                             // default properties
    public static class Defaults {

        /**
         * The default lease time for a lock. This is the initial TTL for the lock in
         * Redis.
         */
        @NotNull
        private final Duration leaseTime;

        /**
         * The default interval between retry attempts when trying to acquire a lock.
         */
        @NotNull
        private final Duration retryInterval;

        /**
         * The default maximum number of retry attempts when trying to acquire a lock. 0
         * means no retries beyond the initial attempt.
         */
        @Min(0)
        private final int maxRetries;

        /**
         * The default timeout for the entire lock acquisition operation (e.g., for
         * tryLock(timeout, unit)).
         */
        @NotNull
        private final Duration acquireTimeout;

        /**
         * Default timeout for individual Redis operations performed during lock
         * acquisition or release.
         * This is distinct from acquireTimeout, which is for the overall lock attempt.
         */
        @NotNull
        private final Duration redisOperationTimeout;
    }

    /**
     * Configuration properties for retry logic in Redis lock operations.
     * These properties are configured under 'destilink.fw.locking.redis.retry'.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty
    private final RetryConfig retry;

    /**
     * Configuration properties for the LockWatchdog.
     * These properties are configured under 'destilink.fw.locking.redis.watchdog'.
     */
    @Getter
    @ToString
    @RequiredArgsConstructor // For final fields, Spring Boot will use this for property binding
    public static class WatchdogProperties {

        /**
         * Interval at which the watchdog attempts to extend the lock lease.
         * This should be significantly shorter than the lock's leaseTime.
         * For example, if leaseTime is 30s, this could be 10s.
         */
        @NotNull
        private final Duration interval;

        /**
         * Maximum number of retries for a single watchdog lease extension operation if
         * it fails.
         * Note: The watchdog itself runs periodically. This is for retries within one
         * scheduled run.
         */
        @Min(1)
        private final int operationMaxRetries;

        /** Timeout for a single watchdog lease extension command sent to Redis. */
        @NotNull
        private final Duration operationTimeout;

        /**
         * Core pool size for the watchdog's scheduled executor service.
         */
        @Min(1)
        private final int corePoolSize;

        /** Thread name prefix for watchdog executor threads. */
        @NotBlank
        private final String threadNamePrefix;

        /**
         * Timeout for waiting for the watchdog scheduler to terminate during shutdown.
         */
        @NotNull
        private final Duration shutdownAwaitTermination;
    }

    /**
     * Configuration properties for retry logic in Redis lock operations.
     * These properties are configured under 'destilink.fw.locking.redis.retry'.
     */
    @Getter
    @ToString
    @RequiredArgsConstructor // For final fields, Spring Boot will use this for property binding
    @Validated
    public static class RetryConfig {

        /**
         * Maximum number of retry attempts for Redis operations.
         * Default: 3 attempts (initial + 2 retries)
         */
        @Min(1)
        private final int maxAttempts;

        /**
         * Initial delay between retry attempts.
         * Default: 100ms
         */
        @NotNull
        private final Duration initialDelay;

        /**
         * Backoff multiplier for exponential backoff.
         * Each retry delay = previous delay * backoffMultiplier
         * Default: 2.0 (doubles each time)
         */
        @Min(1)
        private final double backoffMultiplier;

        /**
         * Maximum delay between retry attempts.
         * Prevents exponential backoff from growing too large.
         * Default: 5 seconds
         */
        @NotNull
        private final Duration maxDelay;

        /**
         * Whether to add random jitter to retry delays.
         * Helps prevent thundering herd problems.
         * Default: true
         */
        private final boolean jitterEnabled;

        /**
         * Circuit breaker failure threshold.
         * Number of consecutive failures before opening the circuit.
         * Default: 5
         */
        @Min(1)
        private final int circuitBreakerFailureThreshold;

        /**
         * Circuit breaker recovery timeout.
         * Time to wait before attempting to close the circuit.
         * Default: 30 seconds
         */
        @NotNull
        private final Duration circuitBreakerRecoveryTimeout;
    }
}