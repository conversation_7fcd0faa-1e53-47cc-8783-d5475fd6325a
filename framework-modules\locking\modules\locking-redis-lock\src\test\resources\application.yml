debug: true
spring:
  application:
    name: test-redis-lock

destilink:
  fw:
    locking:
      redis:
        enabled: true
        state-key-expiration: PT5M
        response-cache-ttl: PT1M
        defaults:
          lease-time: PT60S
          retry-interval: PT0.1S
          max-retries: 3
          acquire-timeout: PT10S
        watchdog:
          interval: PT5S
          factor: 3.0
          core-pool-size: 2
          thread-name-prefix: "test-lock-watchdog-"
          shutdown-await-termination: PT30S
