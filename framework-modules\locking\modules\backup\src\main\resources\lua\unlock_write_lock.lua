-- KEYS[1] mainLockKey (e.g., myApp:__lock_buckets__:resource:__locks__:{myRWLock})
-- KEYS[2] unlockChannelBaseName (e.g., myApp:__lock_buckets__:resource:__unlock_channels__)
-- KEYS[3] responseCacheKey (optional)

-- ARGV[1] requestUuid (used if KEYS[3] is provided)
-- ARGV[2] writerId (field name in KEYS[1] for the writer releasing)
-- ARGV[3] publishCommand (e.g., "PUBLISH")
-- ARGV[4] unlockMessagePayload (UnlockType string, e.g., "RW_WRITE_RELEASED_WAKEN_WRITERS")
-- ARGV[5] responseCacheTTLSeconds (optional, in seconds)
-- ARGV[6] modeFieldNameInMainHash (e.g., "mode")
-- ARGV[7] writerHolderFieldNameInMainHash (e.g., "write_owner_id")

-- Check if the lock exists
if redis.call('exists', KEYS[1]) == 0 then
    -- Lock doesn't exist, nothing to unlock
    if ARGV[5] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '0', 'px', tonumber(ARGV[5]) * 1000); end;
    return 0;
end;

-- Check if this writer holds the lock
if redis.call('hget', KEYS[1], ARGV[7]) ~= ARGV[2] then
    -- Lock is held by someone else, can't unlock
    if ARGV[5] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '0', 'px', tonumber(ARGV[5]) * 1000); end;
    return 0;
end;

-- Delete the lock
redis.call('del', KEYS[1]);

-- Publish unlock message to notify waiting threads
if KEYS[2] ~= nil and ARGV[3] ~= nil and ARGV[4] ~= nil then
    redis.call(ARGV[3], KEYS[2], ARGV[4]);
end;

if ARGV[5] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '1', 'px', tonumber(ARGV[5]) * 1000); end;
return 1;