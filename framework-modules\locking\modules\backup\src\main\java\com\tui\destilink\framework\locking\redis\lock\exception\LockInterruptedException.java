package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock operation is interrupted.
 * <p>
 * This exception is thrown when a thread waiting to acquire a lock
 * is interrupted via {@link Thread#interrupt()}.
 * </p>
 */
public class LockInterruptedException extends AbstractRedisLockException {

    /**
     * Constructs a new LockInterruptedException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     * @param cause       The underlying InterruptedException
     */
    public LockInterruptedException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message, InterruptedException cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // No additional specific markers for this exception type
        // Base class already handles common lock context markers
    }
}