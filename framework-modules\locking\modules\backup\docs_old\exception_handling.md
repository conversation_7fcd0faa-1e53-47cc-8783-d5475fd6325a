# Redis Locking Module: Final Exception Handling Strategy

## 1. Introduction

Effective error handling is crucial. The `locking-redis-lock` module employs a structured exception hierarchy for clear, contextual information, integrating with Destilink Framework's structured logging via `ExceptionMarkerProvider`. This strategy accounts for the "Async-First" design (with Virtual Threads), `redis-core` interaction, and centralized idempotency.

`RedisLockErrorHandler` translates low-level Redis exceptions (from `ClusterCommandExecutor`) into module-specific exceptions. `RedisLockOperationsImpl` handles retries for certain exception types.

## 2. Base Exception: `AbstractRedisLockException`

All custom exceptions extend `AbstractRedisLockException`.

*   **Purpose**: Common type, carries common context, integrates with `ExceptionMarkerProvider`.
*   **Required Contextual Fields**: `lockName` (full key with lock-type segment), `lockType`, `lockOwnerId`, `requestUuid` (from `RedisLockOperationsImpl`), `message`, `cause`.
*   **`ExceptionMarkerProvider` Implementation**: Populates SLF4J `Marker` with common and specific fields.

## 3. Specialized Exception Classes

Extend `AbstractRedisLockException`. Exceptions often propagate via `CompletableFuture`s from Virtual Threads.

*   **`LockAcquisitionException`**: Failure during lock acquisition (not timeout/interruption).
*   **`LockTimeoutException` / `LockAcquisitionTimeoutException`**:
    *   **Purpose**: Asynchronous or blocking acquisition failed within specified `defaults.acquireTimeout`.
    *   **Specific Context**: `lock.timeoutMillis`, `lock.attempts`.
    *   **Note**: Raised only if `acquireTimeout` expires AND any in-flight Redis op has completed without acquiring the lock and without a Redis-specific error.
*   **`LockReleaseException`**: Failure during lock release.
*   **`LeaseExtensionException` / `LockExtensionException`**: Watchdog or user-initiated extension failed.
*   **`LockInterruptedException`**: Wraps `InterruptedException` during synchronous lock wait.
*   **`LockCommandException`**: Lua script or Redis command execution failures from `ClusterCommandExecutor`.
*   **`LockConnectionException`**: Underlying Redis connection problems from `redis-core`.
*   **`LockNotOwnedException` / `LockIllegalMonitorStateException`**: Operation on a lock not held by caller.
*   **`RetryableLockException`**: Indicates a transient error that `RedisLockOperationsImpl` can retry (e.g., temporary network issue for an individual Redis command).
*   **`NonRetryableLockException`**: Indicates a permanent error that should not be retried by `RedisLockOperationsImpl` (e.g., invalid arguments).

### 3.1 Idempotency-Related Exceptions

*   **`IdempotencyViolationException`**: Idempotency system detects inconsistent state. Context: `lock.requestUuid`, `lock.idempotency.cacheKey`.
*   **`IdempotencyTimeoutException`**: Operations on idempotency response cache timeout. Context: `lock.requestUuid`. (Potentially retryable by `RedisLockOperationsImpl`).
*   **`ResponseCacheException`**: General failures in response cache system. Context: `lock.requestUuid`.

## 4. Error Handling by `RedisLockErrorHandler`

*   Catches low-level Redis exceptions (from `ClusterCommandExecutor`).
*   Translates them into `AbstractRedisLockException` subtypes with full context, including `requestUuid`.
*   Handles `CompletionException` / `ExecutionException` from `CompletableFuture`s.

## 5. Error Handling by `RedisLockOperationsImpl`

*   Catches exceptions from `ClusterCommandExecutor` / Lua script executions.
*   Maps these to `RetryableLockException` or `NonRetryableLockException`.
*   **Internal Retries**: For *individual Redis operations* that result in `RetryableLockException`, implements retry logic using `defaults.maxRetries` and `defaults.retryInterval` (using `Thread.sleep()` on the Virtual Thread).
*   **Immediate Propagation**: `NonRetryableLockException` types are propagated immediately.

## 6. `acquireTimeout` Precedence

*   The `defaults.acquireTimeout` is an *approximate* overall limit for user-facing `tryLock` attempts.
*   **Non-Interruption**: If `acquireTimeout` expires while a Redis command is in flight, that command **MUST NOT be interrupted** by the locking module. It completes or times out based on its own lower-level settings.
*   **Result Precedence**: The actual result of the in-flight Redis operation takes precedence. If it acquires the lock, it's acquired. If it fails with a Redis error (after `RedisLockOperationsImpl` retries), that error is propagated.
*   **`LockAcquisitionTimeoutException` Condition**: Raised only if `acquireTimeout` passes, AND the latest Redis operation has completed *without* acquiring the lock, AND *without* a Redis-specific exception.

## 7. Logging Integration

*   `ExceptionMarkerProvider` ensures structured JSON log output via Destilink Core logging.
*   General logging via SLF4J with parameterized messages.
*   `LockContextDecorator` enriches logs with MDC data (propagated to Virtual Threads).