package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when a state update operation fails.
 * <p>
 * This exception is specific to StateLock operations and indicates that
 * an attempt to update a state value in Redis has failed.
 * </p>
 */
public class StateUpdateException extends AbstractRedisLockException {

    private final String stateKey;
    private final String newValue;

    /**
     * Constructs a new StateUpdateException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param stateKey    The key identifying the state
     * @param newValue    The new value that failed to be set
     * @param message     Descriptive error message
     */
    public StateUpdateException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String newValue, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.stateKey = stateKey;
        this.newValue = newValue;
    }

    /**
     * Constructs a new StateUpdateException with the specified details and cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param stateKey    The key identifying the state
     * @param newValue    The new value that failed to be set
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public StateUpdateException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String newValue, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.stateKey = stateKey;
        this.newValue = newValue;
    }

    /**
     * Gets the key identifying the state.
     *
     * @return The state key
     */
    public String getStateKey() {
        return stateKey;
    }

    /**
     * Gets the new value that failed to be set.
     *
     * @return The new value
     */
    public String getNewValue() {
        return newValue;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (stateKey != null) {
            contextMap.put("lock.state.key", stateKey);
        }

        if (newValue != null) {
            contextMap.put("lock.state.newValue", newValue);
        }
    }
}