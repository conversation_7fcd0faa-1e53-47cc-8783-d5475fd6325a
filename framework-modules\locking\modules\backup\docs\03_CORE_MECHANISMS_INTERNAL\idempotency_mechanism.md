# Redis Locking Module: Idempotency Mechanism

## 1. Introduction

Idempotency is a critical property for distributed systems, ensuring that performing an operation multiple times has the same effect as performing it once. The `locking-redis-lock` module implements a centralized idempotency mechanism for all its mutating Redis operations (e.g., lock acquisition, release, lease extension, state updates). This mechanism protects against duplicate operations that might occur due to network retries, client-side retries, or other transient failures.

This document details the design and flow of this centralized idempotency system.

## 2. Core Principles

*   **Centralized Responsibility**: `RedisLockOperationsImpl` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockoperations)) is the single point of responsibility for generating unique `requestUuid` values for each logical lock operation and ensuring these UUIDs are used consistently.
*   **Request UUID (`requestUuid`)**: Each logical mutating operation (e.g., a single call to `tryLock()`, `unlock()`, `extendLease()`) is assigned a unique `requestUuid`. This same `requestUuid` is used for all internal retry attempts of that single logical operation.
*   **Response Cache**: A Redis-based cache (using simple Redis String keys) stores the *result* of successfully completed mutating operations, keyed by the `requestUuid`.
*   **<PERSON>a Script Integration (Idempotency Wrapper)**: The idempotency check (looking up the `requestUuid` in the response cache) and the caching of the result are performed atomically *within* the Lua scripts that execute the core lock logic.
*   **Configurable Cache TTL**: The duration for which responses are cached is configurable via `responseCacheTtl`.

## 3. Idempotency Flow

```mermaid
flowchart TD
    A[Client Initiates Mutating Lock Operation (e.g., tryLock, unlock)] --> B[RedisLockOperationsImpl receives call];
    B --> C[Generate NEW Unique requestUuid for this logical operation];
    C --> D[Construct Response Cache Key:<br/>&lt;prefix&gt;:&lt;bucket&gt;:__resp_cache__:&lt;lockType&gt;:{&lt;lockName&gt;}:&lt;requestUuid&gt; <br/>(See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md))];
    D --> E[Call appropriate Lua Script via ClusterCommandExecutor,<br/>passing requestUuid, responseCacheKey, and responseCacheTtl];

    subgraph LuaScriptExecution [Lua Script Execution on Redis Server]
        LS1[Receive requestUuid, responseCacheKey, responseCacheTtl, and operation parameters]
        LS1 --> LS2{Check Response Cache (KEYS[responseCacheKey]) for requestUuid?}
        LS2 -- "Cache Hit: Result Found" --> LS3[Return Cached Result<br/>(Operation already completed)]
        LS2 -- "Cache Miss: No Result Found" --> LS4[Execute Core Lock Operation Logic (e.g., acquire, release)]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Success" --> LS6[Store Operation Result in Response Cache<br/>Key: responseCacheKey, Value: result, TTL: responseCacheTtl]
        LS6 --> LS7[Return Actual Operation Result]
        LS5 -- "Failure" --> LS8[Return Error/Failure Result<br/>(Typically, failures are NOT cached to allow retries of the operation itself)]
    end

    E --> LS1;
    LS3 --> F[RedisLockOperationsImpl receives Cached Result];
    F --> G[Return Cached Result to Client - Idempotent behavior achieved];
    LS7 --> H[RedisLockOperationsImpl receives Fresh Result];
    H --> I[Return Fresh Result to Client - Operation completed successfully];
    LS8 --> J[RedisLockOperationsImpl receives Error/Failure Result];
    J --> K[Propagate Error to Client - Operation failed, can be retried with SAME requestUuid if applicable];

    subgraph RetryScenario [Client-Side or Internal Retry Scenario]
        L[Network Failure/Timeout During Initial Request OR Internal Retry Logic] --> M[Operation Retried with SAME requestUuid];
        M --> B; # Retried call goes back to RedisLockOperationsImpl
        B -- On Retry --> C_Retry[Use EXISTING requestUuid for this logical operation's retry];
        C_Retry --> D;
    end
```

**Detailed Steps:**

1.  **Operation Initiation**: A client calls a mutating method on a lock instance (e.g., `redisReentrantLock.tryLock()`, `redisStateLock.updateStateAsync()`). This call eventually reaches a method in `RedisLockOperationsImpl`.
2.  **`requestUuid` Generation**: For each *new logical operation*, `RedisLockOperationsImpl` generates a unique `requestUuid` (e.g., using `UUID.randomUUID().toString()`). This `requestUuid` will be associated with this specific attempt (and its potential retries) to perform the operation.
3.  **Response Cache Key Construction**: `RedisLockOperationsImpl` constructs the full Redis key for the response cache entry. This key includes the `requestUuid` and follows the standard key schema (e.g., `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`).
4.  **Lua Script Invocation**: `RedisLockOperationsImpl` invokes the relevant Lua script (e.g., `try_lock.lua`, `unlock.lua`). It passes:
    *   The `responseCacheKey` as one of the `KEYS` arguments.
    *   The `requestUuid` as one of the `ARGV` arguments.
    *   The `responseCacheTtl` (from `RedisLockProperties`, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)) as one of the `ARGV` arguments.
5.  **Lua Script Idempotency Check (Atomic)**:
    *   The Lua script first attempts to `GET` the `responseCacheKey`.
    *   **Cache Hit**: If the key exists and a value is found, this indicates the operation was already successfully completed. The script immediately returns the cached value.
    *   **Cache Miss**: If the key does not exist, the script proceeds to execute its core logic (e.g., attempting to acquire the lock, release the lock).
6.  **Lua Script Core Logic & Result Caching (Atomic)**:
    *   If the core logic is executed (cache miss):
        *   **Success**: If the operation succeeds, the script stores its result (e.g., a structured array response) into the `responseCacheKey` using `SET` with `PX` (for TTL in milliseconds, derived from `responseCacheTtl`). It then returns the actual result.
        *   **Failure**: If the core operation fails (e.g., lock not acquired because it's held by another, owner mismatch on unlock), the script typically does *not* cache this failure. It returns the failure indication (e.g., PTTL of current lock holder, "0" for unlock failure). This allows the operation itself to be retried if appropriate.
7.  **Result Propagation**: `RedisLockOperationsImpl` receives the result from the Lua script (either fresh or cached) and returns it to the lock instance, which then propagates it to the original caller.
8.  **Retries**: If an operation needs to be retried (either by the client or by internal retry logic within `RedisLockOperationsImpl` for transient issues), the **same `requestUuid`** generated in Step 2 is reused for the retry attempts. This ensures that if the original attempt actually succeeded but its response was lost, the retry will hit the response cache.

## 4. Key Benefits

*   **Consistency Across Lock Types**: All lock types and mutating operations benefit from this uniform mechanism.
*   **Network Resilience**: Protects against duplicate executions due to transient network issues where a command might have succeeded on Redis but the response failed to reach the client.
*   **Simplified Client Logic**: Clients (or higher layers in the locking module) can retry operations more safely.
*   **Atomic Guarantees**: The check-cache-execute-store-cache logic is performed atomically within Lua scripts.

## 5. Configuration

*   `destilink.fw.locking.redis.response-cache-ttl`: Defines the Time-To-Live for entries in the response cache. This should be configured to be long enough to cover typical client retry windows and potential clock drift, but not so long as to consume excessive Redis memory if `requestUuid`s are very numerous.

## 6. Exception Handling

Specific exceptions related to idempotency issues are defined:
*   `IdempotencyViolationException`
*   `IdempotencyTimeoutException`
*   `DuplicateRequestException`
*   `ResponseCacheException`

(See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md#31-idempotency-related-exceptions) for details).

This centralized idempotency mechanism significantly enhances the robustness and reliability of the distributed locking module.