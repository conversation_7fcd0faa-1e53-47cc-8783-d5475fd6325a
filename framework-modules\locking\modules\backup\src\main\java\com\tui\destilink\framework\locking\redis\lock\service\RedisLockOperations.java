package com.tui.destilink.framework.locking.redis.lock.service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Defines operations for Redis-based locks.
 * <p>
 * This interface provides asynchronous methods for acquiring, releasing,
 * extending,
 * and checking locks in Redis. All operations return {@link CompletableFuture}
 * to
 * allow for non-blocking execution.
 * </p>
 * <p>
 * The operations are implemented using Lua scripts for atomicity and executed
 * through the Redis cluster connection pool.
 * </p>
 */
public interface RedisLockOperations {

        /**
         * Acquires a lock with the specified key, value, and TTL.
         * <p>
         * If the lock is already held by the same owner (same value), the TTL is
         * refreshed.
         * If the lock is held by another owner, the operation fails.
         * </p>
         *
         * @param lockKey   The key for the lock in Redis
         * @param lockValue The value (owner identifier) for the lock
         * @param ttl       The time-to-live for the lock
         * @return A CompletableFuture that completes with true if the lock was
         *         acquired, false otherwise
         */
        CompletableFuture<Boolean> acquireLock(String lockKey, String lockValue, Duration ttl);

        /**
         * Releases a lock with the specified key and value.
         * <p>
         * The lock is only released if it is held by the specified owner (value).
         * </p>
         *
         * @param lockKey   The key for the lock in Redis
         * @param lockValue The value (owner identifier) for the lock
         * @return A CompletableFuture that completes with true if the lock was
         *         released, false otherwise
         */
        CompletableFuture<Boolean> releaseLock(String lockKey, String lockValue);

        /**
         * Unlocks/releases a lock with the specified key and owner ID.
         * This is an alternative to releaseLock, potentially using a different
         * underlying script or logic.
         *
         * @param lockKey The key for the lock in Redis.
         * @param ownerId The identifier of the lock owner.
         * @return A CompletableFuture that completes when the unlock operation is
         *         attempted; specific result depends on implementation (e.g., boolean
         *         for success, or void).
         */
        CompletableFuture<Void> unlock(String lockKey, String ownerId); // Added to match RedisWriteLock usage

        /**
         * Extends the TTL of an existing lock.
         * <p>
         * The lock TTL is only extended if it is held by the specified owner (value).
         * </p>
         *
         * @param lockKey   The key for the lock in Redis
         * @param lockValue The value (owner identifier) for the lock
         * @param ttl       The new time-to-live for the lock
         * @return A CompletableFuture that completes with true if the lock TTL was
         *         extended, false otherwise
         */
        CompletableFuture<Boolean> extendLock(String lockKey, String lockValue, Duration ttl);

        /**
         * Checks if a lock exists and is held by the specified owner.
         *
         * @param lockKey   The key for the lock in Redis
         * @param lockValue The value (owner identifier) for the lock
         * @return A CompletableFuture that completes with true if the lock exists and
         *         is held by the specified owner, false otherwise
         */
        CompletableFuture<Boolean> checkLock(String lockKey, String lockValue);

        /**
         * Attempts to acquire a lock in a non-blocking way using Duration for TTL.
         * <p>
         * Unlike {@link #acquireLock}, this method does not wait if the lock is held by
         * another owner.
         * </p>
         *
         * @param lockKey   The key for the lock in Redis
         * @param lockValue The value (owner identifier) for the lock
         * @param ttl       The time-to-live for the lock as a Duration
         * @return A CompletableFuture that completes with true if the lock was
         *         acquired, false otherwise
         */
        CompletableFuture<Boolean> tryLock(String lockKey, String lockValue, Duration ttl);

        /**
         * Attempts to acquire a lock with a specific owner ID and lease time (TTL),
         * retrying for a specified duration.
         *
         * @param lockKey        The key for the lock in Redis.
         * @param ownerId        The identifier of the entity attempting to acquire the
         *                       lock.
         * @param ttl            The time-to-live for the lock if acquired.
         * @param acquireTimeout The maximum duration to wait while attempting to
         *                       acquire the lock.
         * @return A CompletableFuture that completes with true if the lock was
         *         acquired, false otherwise.
         */
        CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout);

        /**
         * Checks if a lock with the given key is currently held by any owner.
         *
         * @param lockKey The key for the lock in Redis.
         * @return A CompletableFuture that completes with true if the lock is held,
         *         false otherwise.
         */
        CompletableFuture<Boolean> isLocked(String lockKey);

        /**
         * Attempts to acquire a state lock with the specified key, owner, expected
         * state, and optional initialization.
         *
         * @param lockKey                 The main key for the lock in Redis.
         * @param responseCacheKey        The fully constructed key for the response
         *                                cache, passed by the client.
         * @param requestUuid             Unique ID for request idempotency.
         * @param leaseTimeMs             Lock expiration time in milliseconds.
         * @param lockOwnerId             Identifier of the client attempting to acquire
         *                                the lock.
         * @param expectedState           The state the lock must be in for acquisition
         *                                to
         *                                succeed.
         * @param initializeIfAbsent      Flag indicating whether to initialize the lock
         *                                state if it doesn't exist.
         * @param initialState            The initial state to set if initializing.
         * @param responseCacheTtlSeconds TTL for the response cache entry.
         * @return A CompletableFuture that completes with the current state of the lock
         *         if acquired or already owned,
         *         or null if acquisition failed due to state mismatch or other
         *         reasons.
         */
        CompletableFuture<String> tryStateLock(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String leaseTimeMs,
                        String lockOwnerId,
                        String expectedState,
                        boolean initializeIfAbsent,
                        String initialState,
                        String responseCacheTtlSeconds);

        /**
         * Releases a state lock, optionally setting a new state.
         *
         * @param lockKey          The main key for the lock in Redis.
         * @param responseCacheKey The fully constructed key for the response cache,
         *                         passed by the client.
         * @param requestUuid      Unique ID for request idempotency.
         * @param lockOwnerId      Identifier of the client attempting to release the
         *                         lock.
         * @param newState         The new state to set upon successful release (can be
         *                         null if no state change is desired).
         * @return A CompletableFuture that completes with a String indicating the
         *         result of the operation (e.g., "OK", "NOT_OWNER").
         */
        CompletableFuture<String> releaseStateLock(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String lockOwnerId,
                        String newState);

        /**
         * Extends the lease time of an acquired state lock.
         *
         * @param lockKey          The main key for the lock in Redis.
         * @param responseCacheKey The fully constructed key for the response cache,
         *                         passed by the client.
         * @param requestUuid      Unique ID for request idempotency.
         * @param newLeaseTimeMs   The new lease time in milliseconds.
         * @param lockOwnerId      Identifier of the client attempting to extend the
         *                         lock.
         * @return A CompletableFuture that completes with a Long representing the new
         *         PTTL of the lock if successful, or null/error if not.
         */
        CompletableFuture<Long> extendStateLock(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String newLeaseTimeMs,
                        String lockOwnerId);

        /**
         * Gets the current state of a state lock without attempting to acquire it.
         *
         * @param lockKey The main key for the lock in Redis.
         * @return A CompletableFuture that completes with the current state of the lock
         *         as a String, or null if the lock does not exist.
         */
        CompletableFuture<String> getStateLockState(String lockKey);

        // ReadWriteLock specific operations

        /**
         * Attempts to acquire a read lock for a read-write lock.
         *
         * @param lockKey                 The base key for the read-write lock.
         * @param responseCacheKey        Key for caching responses.
         * @param requestUuid             Unique request ID.
         * @param leaseTimeMs             Lease time for the lock.
         * @param readerId                Identifier for the reader.
         * @param responseCacheTtlSeconds TTL for the response cache.
         * @return CompletableFuture with Long representing PTTL if successful, null
         *         otherwise.
         */
        CompletableFuture<Long> tryReadLock(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String leaseTimeMs,
                        String readerId,
                        String responseCacheTtlSeconds);

        CompletableFuture<Boolean> hset(String key, String field, String value);

        /**
         * Releases a read lock for a read-write lock.
         *
         * @param lockKey     The base key for the read-write lock.
         * @param readerId    Identifier for the reader.
         * @param requestUuid Unique request ID.
         * @return CompletableFuture with result of the unlock operation.
         */
        CompletableFuture<String> unlockReadLock(
                        String lockKey,
                        String readerId,
                        String requestUuid);

        /**
         * Attempts to acquire a write lock for a read-write lock.
         *
         * @param lockKey                 The base key for the read-write lock.
         * @param responseCacheKey        Key for caching responses.
         * @param requestUuid             Unique request ID.
         * @param leaseTimeMs             Lease time for the lock.
         * @param writerId                Identifier for the writer.
         * @param responseCacheTtlSeconds TTL for the response cache.
         * @param mode                    Locking mode (e.g., exclusive).
         * @return CompletableFuture with Long representing PTTL if successful, null
         *         otherwise.
         */
        CompletableFuture<Long> tryWriteLock(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String leaseTimeMs,
                        String writerId,
                        String responseCacheTtlSeconds,
                        String mode);

        /**
         * Releases a write lock for a read-write lock.
         *
         * @param lockKey     The base key for the read-write lock.
         * @param writerId    Identifier for the writer.
         * @param requestUuid Unique request ID.
         * @param command     Command for unlock (e.g., "UNLOCK", "FORCE_UNLOCK").
         * @return CompletableFuture with result of the unlock operation.
         */
        CompletableFuture<String> unlockWriteLock(
                        String lockKey,
                        String writerId,
                        String requestUuid,
                        String command);

        /**
         * Publishes a message to the unlock channel for a given lock key.
         *
         * @param lockKey The lock key for which the unlock message is published.
         * @param message The message to publish (typically indicating unlock).
         * @return A CompletableFuture that completes with the number of clients that
         *         received the message.
         */
        CompletableFuture<Long> publishUnlockMessage(String lockKey, String message);

        /**
         * Subscribes to the unlock channel for a given lock key.
         *
         * @param lockKey  The lock key for which to subscribe to unlock messages.
         * @param callback The callback to execute when a message is received.
         * @return A CompletableFuture that completes when the subscription is active.
         */
        CompletableFuture<Void> subscribeToUnlockChannel(String lockKey, java.util.function.Consumer<String> callback);

        /**
         * Unsubscribes from the unlock channel for a given lock key.
         *
         * @param lockKey The lock key to unsubscribe from.
         * @return A CompletableFuture that completes when unsubscription is done.
         */
        CompletableFuture<Void> unsubscribeFromUnlockChannel(String lockKey);

        /**
         * Gets a string value from Redis.
         *
         * @param key The key to get the value for.
         * @return A CompletableFuture that completes with the string value, or null if
         *         the key doesn't exist.
         */
        CompletableFuture<String> getString(String key);

        /**
         * Updates the state of a lock.
         *
         * @param lockKey                 The main key for the lock in Redis.
         * @param responseCacheKey        The fully constructed key for the response
         *                                cache.
         * @param requestUuid             Unique ID for request idempotency.
         * @param lockOwnerId             Identifier of the client attempting to update
         *                                the state.
         * @param newState                The new state to set.
         * @param stateKeySuffix          The suffix to append to the lock key to form
         *                                the state key.
         * @param stateExpirationMs       The expiration time for the state key in
         *                                milliseconds (can be "nil").
         * @param responseCacheTtlSeconds TTL for the response cache entry.
         * @return A CompletableFuture that completes with a code indicating the result:
         *         1 = success, 0 = not owner.
         */
        CompletableFuture<Integer> updateState(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String lockOwnerId,
                        String newState,
                        String stateKeySuffix,
                        String stateExpirationMs,
                        String responseCacheTtlSeconds);

        /**
         * Updates the state of a lock only if the current state matches the expected
         * state.
         * This operation is atomic and ensures that the state is only updated if it
         * matches
         * the expected value.
         *
         * @param lockKey                 The main key for the lock in Redis.
         * @param responseCacheKey        The fully constructed key for the response
         *                                cache.
         * @param requestUuid             Unique ID for request idempotency.
         * @param lockOwnerId             Identifier of the client attempting to update
         *                                the state.
         * @param expectedState           The state value that must match the current
         *                                state.
         * @param newState                The new state to set if the current state
         *                                matches the expected state.
         * @param stateKeySuffix          The suffix to append to the lock key to form
         *                                the state key.
         * @param stateExpirationMs       The expiration time for the state key in
         *                                milliseconds (can be "nil").
         * @param responseCacheTtlSeconds TTL for the response cache entry.
         * @return A CompletableFuture that completes with a code indicating the result:
         *         1 = success, 0 = not owner, 2 = state mismatch.
         */
        CompletableFuture<Integer> updateStateIfEquals(
                        String lockKey,
                        String responseCacheKey,
                        String requestUuid,
                        String lockOwnerId,
                        String expectedState,
                        String newState,
                        String stateKeySuffix,
                        String stateExpirationMs,
                        String responseCacheTtlSeconds);

        /**
         * Unlocks a state lock and optionally sets a new state.
         *
         * @param lockKey                 The main key for the lock in Redis.
         * @param unlockChannelPrefix     The prefix for the unlock channel.
         * @param responseCacheKey        The fully constructed key for the response
         *                                cache.
         * @param requestUuid             Unique ID for request idempotency.
         * @param lockOwnerId             Identifier of the client attempting to release
         *                                the lock.
         * @param newState                The new state to set upon successful release
         *                                (can be null).
         * @param stateKeySuffix          The suffix to append to the lock key to form
         *                                the state key.
         * @param stateExpirationMs       The expiration time for the state key in
         *                                milliseconds (can be "nil").
         * @param responseCacheTtlSeconds TTL for the response cache entry.
         * @return A CompletableFuture that completes when the unlock operation is
         *         attempted.
         */
        CompletableFuture<Void> unlockStateLock(
                        String lockKey,
                        String unlockChannelPrefix,
                        String responseCacheKey,
                        String requestUuid,
                        String lockOwnerId,
                        String newState,
                        String stateKeySuffix,
                        String stateExpirationMs,
                        String responseCacheTtlSeconds);
}