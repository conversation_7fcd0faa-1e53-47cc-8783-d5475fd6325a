{"groups": [{"name": "destilink.fw.locking.redis", "type": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "description": "Configuration properties for Redis-based distributed locking."}, {"name": "destilink.fw.locking.redis.watchdog", "type": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "sourceType": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "description": "Configuration properties for the Redis lock watchdog mechanism."}], "properties": [{"name": "destilink.fw.locking.redis.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether Redis locking functionality is enabled.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": true}, {"name": "destilink.fw.locking.redis.lease-time", "type": "java.time.Duration", "description": "Default time-to-live for locks.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "30s"}, {"name": "destilink.fw.locking.redis.retry-interval", "type": "java.time.Duration", "description": "Default retry interval between lock acquisition attempts.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "100ms"}, {"name": "destilink.fw.locking.redis.max-retries", "type": "java.lang.Integer", "description": "Maximum number of retry attempts for lock acquisition. 0 means no retries.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": 30}, {"name": "destilink.fw.locking.redis.state-key-expiration", "type": "java.time.Duration", "description": "Expiration time for the state key used in StateLock implementations.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "24h"}, {"name": "destilink.fw.locking.redis.pub-sub-wait-timeout", "type": "java.time.Duration", "description": "Timeout for waiting on pub/sub operations.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "5s"}, {"name": "destilink.fw.locking.redis.acquire-timeout", "type": "java.time.Duration", "description": "Timeout for acquiring a lock.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "10s"}, {"name": "destilink.fw.locking.redis.response-cache-ttl", "type": "java.time.Duration", "description": "Time-to-live for the response cache used in lock operations.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "30s"}, {"name": "destilink.fw.locking.redis.lock-owner-id-validation-regex", "type": "java.lang.String", "description": "Regular expression pattern for validating lock owner IDs.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"}, {"name": "destilink.fw.locking.redis.max-lock-name-length", "type": "java.lang.Integer", "description": "Maximum allowed length for a lock name.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": 128}, {"name": "destilink.fw.locking.redis.max-bucket-name-length", "type": "java.lang.Integer", "description": "Maximum allowed length for a bucket name.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": 64}, {"name": "destilink.fw.locking.redis.max-scope-length", "type": "java.lang.Integer", "description": "Maximum allowed length for a scope.", "sourceType": "com.tui.destilink.framework.locking.redis.config.RedisLockProperties", "defaultValue": 64}, {"name": "destilink.fw.locking.redis.watchdog.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether the lock watchdog mechanism is enabled.", "sourceType": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "defaultValue": true}, {"name": "destilink.fw.locking.redis.watchdog.interval", "type": "java.time.Duration", "description": "Interval between watchdog renewal attempts.", "sourceType": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "defaultValue": "10s"}, {"name": "destilink.fw.locking.redis.watchdog.max-retries", "type": "java.lang.Integer", "description": "Maximum number of retry attempts for watchdog renewal.", "sourceType": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "defaultValue": 3}, {"name": "destilink.fw.locking.redis.watchdog.timeout", "type": "java.time.Duration", "description": "Timeout for watchdog operations.", "sourceType": "com.tui.destilink.framework.locking.redis.lock.config.WatchdogProperties", "defaultValue": "5s"}], "hints": []}