package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock is not found in Redis.
 * <p>
 * This exception is thrown when an operation is attempted on a lock
 * that does not exist in Redis.
 * </p>
 */
public class LockNotFoundException extends AbstractRedisLockException {

    /**
     * Constructs a new LockNotFoundException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     */
    public LockNotFoundException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
    }

    /**
     * Constructs a new LockNotFoundException with the specified details and cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public LockNotFoundException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // No additional specific markers for this exception type
        // Base class already adds the common lock markers
    }
}