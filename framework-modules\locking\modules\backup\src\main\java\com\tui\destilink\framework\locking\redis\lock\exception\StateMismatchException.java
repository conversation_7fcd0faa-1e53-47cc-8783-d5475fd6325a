package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when a state value does not match the expected value.
 * <p>
 * This exception is specific to StateLock operations and indicates that
 * a state value in Redis did not match the expected value during a conditional
 * operation.
 * </p>
 */
public class StateMismatchException extends AbstractRedisLockException {

    private final String stateKey;
    private final String expectedState;
    private final String actualState;

    /**
     * Constructs a new StateMismatchException with the specified details.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be
     *                      null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be
     *                      null)
     * @param stateKey      The key identifying the state
     * @param expectedState The expected state value
     * @param actualState   The actual state value found
     * @param message       Descriptive error message
     */
    public StateMismatchException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String expectedState, String actualState, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.stateKey = stateKey;
        this.expectedState = expectedState;
        this.actualState = actualState;
    }

    /**
     * Constructs a new StateMismatchException with the specified details and cause.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be
     *                      null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be
     *                      null)
     * @param stateKey      The key identifying the state
     * @param expectedState The expected state value
     * @param actualState   The actual state value found
     * @param message       Descriptive error message
     * @param cause         The underlying cause of this exception
     */
    public StateMismatchException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String expectedState, String actualState, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.stateKey = stateKey;
        this.expectedState = expectedState;
        this.actualState = actualState;
    }

    /**
     * Gets the key identifying the state.
     *
     * @return The state key
     */
    public String getStateKey() {
        return stateKey;
    }

    /**
     * Gets the expected state value.
     *
     * @return The expected state
     */
    public String getExpectedState() {
        return expectedState;
    }

    /**
     * Gets the actual state value found.
     *
     * @return The actual state
     */
    public String getActualState() {
        return actualState;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (stateKey != null) {
            contextMap.put("lock.state.key", stateKey);
        }

        if (expectedState != null) {
            contextMap.put("lock.state.expected", expectedState);
        }

        if (actualState != null) {
            contextMap.put("lock.state.actual", actualState);
        }
    }
}