package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when a lock lease extension operation fails.
 * <p>
 * This exception is thrown when an attempt to extend the lease time of a lock
 * fails,
 * typically because the lock is no longer owned by the caller or has expired.
 * </p>
 */
public class LeaseExtensionException extends AbstractRedisLockException {

    private final long leaseTimeMillis;
    private final Object extensionResult;

    /**
     * Constructs a new LeaseExtensionException with the specified details.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be
     *                        null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param leaseTimeMillis The lease time in milliseconds that was attempted to
     *                        be extended
     * @param extensionResult The result of the extension operation (can be null)
     * @param message         Descriptive error message
     */
    public LeaseExtensionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long leaseTimeMillis, Object extensionResult, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.leaseTimeMillis = leaseTimeMillis;
        this.extensionResult = extensionResult;
    }

    /**
     * Constructs a new LeaseExtensionException with the specified details and
     * cause.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be
     *                        null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param leaseTimeMillis The lease time in milliseconds that was attempted to
     *                        be extended
     * @param extensionResult The result of the extension operation (can be null)
     * @param message         Descriptive error message
     * @param cause           The underlying cause of this exception
     */
    public LeaseExtensionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long leaseTimeMillis, Object extensionResult, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.leaseTimeMillis = leaseTimeMillis;
        this.extensionResult = extensionResult;
    }

    /**
     * Gets the lease time in milliseconds that was attempted to be extended.
     *
     * @return The lease time in milliseconds
     */
    public long getLeaseTimeMillis() {
        return leaseTimeMillis;
    }

    /**
     * Gets the result of the extension operation.
     *
     * @return The extension result, or null if not available
     */
    public Object getExtensionResult() {
        return extensionResult;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.leaseTimeMillis", leaseTimeMillis);
        if (extensionResult != null) {
            contextMap.put("lock.extensionResult", extensionResult.toString());
        }
    }
}