<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tui.destilink.framework.locking</groupId>
        <artifactId>locking</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>locking-redis-lock</artifactId>
    <name>Destilink Framework - Locking - Redis Lock</name>
    <description>Redis-based distributed locking mechanism for the Destilink Framework.</description>

    <properties>
        <!-- Define versions for dependencies not managed by the parent BOM or
        framework-dependencies-parent -->
        <!-- For example, if a specific version of a library only used here is needed -->
    </properties>

    <dependencies>
        <!-- Destilink Framework Dependencies -->
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>core</artifactId>
            <!-- Version managed by parent -->
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>redis-core</artifactId>
            <!-- Version managed by parent -->
        </dependency>

        <!-- Micrometer for metrics -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <!-- Required for testing with Micrometer, if not covered by spring-boot-starter-test -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-observation-test</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Jakarta Validation API (already brought in by spring-boot-starter-validation, but good
        to be explicit if directly used) -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>redis-test-support</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency> <!-- Added for CompletableFuture testing -->
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Explicit Mockito for tests if not sufficiently managed by spring-boot-starter-test -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <!-- Configuration inherited from parent -->
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <!-- Configuration inherited from parent -->
            </plugin>
        </plugins>
    </build>
</project>