# Redis Locking Module: Final Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation, consolidated from previous planning phases and updated to reflect the Async-First design, explicit `redis-core` integration, Virtual Thread adoption, and refined operational semantics as per the Comprehensive Plan (Version 1.0, June 17, 2025).

*   **`AbstractRedisLock`**: Base Java class providing common functionality for all Redis lock implementations in the module. It orchestrates asynchronous `CompletableFuture`-based operations, leveraging Virtual Threads for execution.
*   **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod ID). These locks are typically eligible for watchdog lease renewal if their user-provided lease time exceeds the `safetyBufferMillis`.
*   **Async-First**: A core design principle of this module where all fundamental lock operations are asynchronous, returning `CompletableFuture`s and executed on Virtual Threads. Synchronous `java.util.concurrent.locks.Lock` methods are implemented as blocking wrappers around these asynchronous operations.
*   **`AsyncLock`**: An interface that extends `java.util.concurrent.locks.Lock`. It provides non-blocking, `CompletableFuture`-based asynchronous versions of standard lock operations, suitable for reactive programming models and high-concurrency scenarios to avoid platform thread blocking. All concrete lock implementations in this module must implement this interface.
*   **Atomic Operation**: An operation that is guaranteed to execute fully without interruption or interference from other operations, ensured by executing commands as Lua scripts on the Redis server. All atomic operations are executed via `redis-core`'s `ClusterCommandExecutor`.
*   **Bucket (Lock Bucket)**: A logical grouping or namespace for locks. Buckets allow for applying common default configurations (e.g., `defaults.leaseTime`, `defaults.retryInterval`) to a set of related locks, primarily configured programmatically via builders. The `LockOwnerSupplier` configured at the bucket level is a key factor in determining watchdog eligibility for locks within that bucket.
*   **Builder API**: A fluent API, starting with `LockBucketRegistry.builder(...)`, used to configure and create specific lock instances (e.g., `RedisReentrantLock`, `RedisStateLock`).
*   **`ClusterCommandExecutor`**: A critical component from `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` used for all Redis command executions within this module. It provides asynchronous, cluster-aware command execution capabilities, ensuring compliance with framework guidelines for Redis interaction.
*   **Distributed Lock**: A synchronization primitive used to coordinate access to shared resources among multiple processes or services running in a distributed environment.
*   **ExceptionMarkerProvider**: A Destilink Framework interface (`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`) implemented by custom exceptions to provide detailed contextual information for structured JSON logging, as per framework guidelines.
*   **`expiresAtMillis`**: The absolute Unix timestamp (in milliseconds) when a lock is currently set to expire in Redis, calculated by the Redis server using `redis.call('TIME')` and `PEXPIREAT`. This value is stored in the `lockDataKey` and reflects the actual expiry time of the `lockKey`.
*   **Hash Tag (Redis Cluster)**: A mechanism (`{{...}}`) used in Redis key names to ensure that multiple keys are allocated to the same hash slot, and thus the same node, in a Redis Cluster setup. Crucial for multi-key Lua scripts executed via `ClusterCommandExecutor`. The lock identifier part of the key (e.g., `{order123}`) serves this purpose.
*   **Idempotency Wrapper**: A pattern applied to all mutating Lua scripts. It involves checking a Redis response cache using a `requestUuid` before executing the core logic, and storing the operation's result in the cache (with a TTL defined by `responseCacheTtl`) upon completion. This ensures that retried operations are not executed multiple times.
*   **`Individual Read Lock Timeout Key`**: A Redis String key with an associated TTL, created for each specific instance of an acquired read lock within a `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{myRWLock}:<readerId>:<count>`). It manages the lease for that individual read access, contributing to the overall lifetime management of the main `RedisReadWriteLock` key. The key includes the `readwrite` lock-type segment.
*   **Lease Time (`defaults.leaseTime`)**: The default duration a developer *intends* for a lock to be held, configured globally or at bucket/instance level. This is a `relativeLeaseTimeMillis`. If the watchdog is active for the lock, it uses the `originalLeaseTimeMillis` (derived from this) to manage renewals. If the watchdog is not active, `relativeLeaseTimeMillis` directly determines the TTL of the lock key in Redis. For a `RedisReadWriteLock`, each individual read lock acquisition also establishes its own lease managed by an `Individual Read Lock Timeout Key`; the main `RedisReadWriteLock` key's TTL is then maintained as the maximum of its configured lease (potentially watchdog-extended to `safetyBufferMillis` or `userIntendedExpireTimeMillis`) and the maximum remaining TTL of any active individual read lock leases.
*   **`lockDataKey`**: A secondary Redis key, typically a Redis Hash, used to store additional metadata associated with a `lockKey`. This metadata includes `ownerId`, `originalLeaseTimeMillis` (the user's intended full lease duration), and `expiresAtMillis` (the current absolute expiry timestamp). Its key format includes the mandatory lock-type segment (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}:data`).
*   **`lockKey`**: The primary Redis key for the distributed lock itself (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}`). This key holds the `ownerId` as its value (or is a Hash for reentrant/complex locks) and has an associated expiry managed by `PEXPIREAT`. It includes a mandatory lock-type segment (e.g., `reentrant`, `stamped`).
*   **Lock-Type Segment**: A mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`, `readwrite`) that semantically isolates different lock types, preventing cross-type interference. It is part of `lockKey` and `lockDataKey`.
*   **`LockBucketConfig`**: A Java class (non-Spring managed) that holds the resolved, effective configuration settings for a specific lock bucket, after merging global defaults (`RedisLockProperties.Defaults`) and programmatic builder overrides.
*   **`LockComponentRegistry`**: A Spring-managed bean that acts as a central holder for shared, stateless locking services (like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`), making them available to lock instances and other internal components.
*   **`LockOwnerSupplier`**: An interface responsible for providing a unique identifier (`ownerId`) for the current lock owner (typically combining an application instance ID and a thread ID). A `DefaultLockOwnerSupplier` bean is provided. It also determines watchdog eligibility via `canUseWatchdog()`.
*   **`LockSemaphoreHolder`**: A helper class, managed per `lockKey` (likely via Guava Cache with weak values), used by `UnlockMessageListener` to manage waiting `CompletableFuture`s for that specific `lockKey`. It is integrated with `CompletableFuture` for asynchronous waits on Virtual Threads.
*   **`LockWatchdog`**: A Spring-managed bean that periodically extends the lease (TTL using `PEXPIREAT`) of active, application-instance-bound locks in Redis to prevent premature expiration. It is always active if the module is enabled, but only monitors locks whose `userProvidedLeaseTime > safetyBufferMillis` and are instance-bound. Its operations are performed asynchronously via `ClusterCommandExecutor`. It uses `watchdog.interval` and `watchdog.factor` for its scheduling and `safetyBufferMillis` calculation. It **never** modifies the `originalLeaseTimeMillis` stored in `lockDataKey`.
*   **Lua Scripts**: Scripts written in the Lua programming language that are executed atomically on the Redis server to perform complex lock operations (e.g., acquire, release, extend, state manipulation). All Lua script executions are handled by `RedisLockOperations` which in turn uses `ClusterCommandExecutor`. All mutating scripts implement the Idempotency Wrapper. They return structured responses including status, `expiresAtMillis`, and `originalLeaseTimeMillis`.
*   **MDC (Mapped Diagnostic Context)**: A logging mechanism used with SLF4J to enrich log messages with contextual data (e.g., `lock.name`, `lock.operation`). Managed via `LockContextDecorator`, adhering to framework logging guidelines, and propagated to Virtual Threads.
*   **Non-Polling Wait**: A mechanism where Virtual Threads waiting for a lock do not continuously check its status. Instead, their associated `CompletableFuture` waits to be signaled when the lock is released (via Redis Pub/Sub and `LockSemaphoreHolder`). This is the primary waiting strategy. Registration with `UnlockMessageListenerManager` occurs *before* the first Redis acquisition attempt.
*   **`originalLeaseTimeMillis`**: The `relativeLeaseTimeMillis` value that was *last explicitly set by a user-initiated lock acquisition or extension*. This value is stored persistently in Redis (in `lockDataKey`) and is used by the `LockWatchdog` to determine the intended full lease duration for renewal cycles (i.e., `userIntendedExpireTimeMillis`). It is **never** modified by the watchdog itself.
*   **Override Precedence**: The order in which configuration settings are applied: Instance-specific settings (via builder methods) > Programmatic Bucket Configuration (via `LockBucketBuilder`) > Global Configuration (`RedisLockProperties.Defaults`).
*   **`ownerId`**: A unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). This identifies which application instance and thread/process holds the lock.
*   **Pub/Sub (Publish/Subscribe)**: A Redis messaging paradigm used by `UnlockMessageListener` instances to receive notifications when locks are released, enabling efficient, non-polling waits. Messages contain only `UnlockType`; `lockName` is derived from the channel.
*   **RedisCoreProperties**: The configuration properties class (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java`) from the `redis-core` module. This module uses these properties to ensure consistent Redis client configuration across the framework.
*   **Reentrant Lock**: A lock that can be acquired multiple times by the same owner (e.g., the same thread) without deadlocking. Each `lock()` call must be matched by an `unlock()` call. Reentrancy data (owner, count) is stored in a Redis Hash (`lockKey` for reentrant type).
*   **`RedisLockAutoConfiguration`**: The Spring Boot `@AutoConfiguration` class responsible for setting up all the necessary beans for the locking module, strictly adhering to framework guidelines (e.g., no `@ComponentScan`, explicit `@Bean` definitions).
*   **`RedisLockErrorHandler`**: A Spring-managed bean responsible for translating low-level Redis exceptions (often from `ClusterCommandExecutor`) into specific, contextual `AbstractRedisLockException` subtypes.
*   **`RedisLockOperations`**: A Spring-managed bean that abstracts direct Redis communication for lock-specific commands. **Crucially, it delegates all Redis command executions to `ClusterCommandExecutor` from `redis-core`, ensuring an asynchronous interaction model.** It is also responsible for generating the `requestUuid` for idempotency and managing internal retries for individual Redis operations.
*   **`RedisLockProperties`**: A Spring `@ConfigurationProperties` class that binds global settings from YAML files (e.g., `destilink.fw.locking.redis.*`). Contains nested `WatchdogProperties` and `Defaults` classes.
*   **`relativeLeaseTimeMillis`**: The duration (in milliseconds) requested by the user for a lock's lease (e.g., from `defaults.leaseTime`). This is a relative value (e.g., "30 seconds from now"). It is the input to lock acquisition and extension operations.
*   **`requestUuid`**: A unique identifier (UUID) generated centrally by `RedisLockOperationsImpl` for each logical lock operation (e.g., a single `tryLock` call, including its internal application-level retries). This UUID serves as the key for the centralized idempotency mechanism (Response Cache), ensuring that if the same logical operation is retried, it will not be executed multiple times. The same `requestUuid` is used for all retry attempts of a single logical operation.
*   **Response Cache**: A centralized Redis-based cache system that stores the results of mutating lock operations, keyed by `requestUuid`. This cache is managed entirely within Lua scripts (Idempotency Wrapper) and provides the foundation for the idempotency mechanism. The TTL for these cache entries is `responseCacheTtl`.
*   **Retry Interval (`defaults.retryInterval`)**: The duration `AbstractRedisLock`'s internal logic waits (via `Thread.sleep()` on a Virtual Thread) after a semaphore wait (which might have timed out based on the current lock holder's TTL) before re-attempting to acquire the lock via Lua script. This also applies to retries of individual Redis operations within `RedisLockOperationsImpl`.
*   **`safetyBufferMillis`**: A calculated duration (`watchdog.interval * watchdog.factor`) used by the `LockWatchdog`. It determines the minimum `userLeaseTime` for watchdog eligibility. For eligible locks, the watchdog aims to maintain a TTL of at least `safetyBufferMillis` until the "final leg" of the user's intended lease.
*   **`ScriptLoader`**: A Spring-managed bean that loads and caches all Lua scripts from the classpath at application startup.
*   **StateLock (`RedisStateLock`)**: A type of lock that, in addition to standard locking, also manages and potentially gates access based on an associated "state" value stored in Redis. Its keys include the `state` lock-type segment.
*   **StampedLock (`RedisStampedLock`)**: A lock type that provides optimistic read locking and pessimistic write locking, using a "stamp" or version number to detect intervening writes. Its keys include the `stamped` lock-type segment.
*   **TTL (Time-To-Live)**: The actual expiration time set on a key in Redis, managed via `PEXPIREAT`. For locks managed by the watchdog, this is typically `safetyBufferMillis` or `userIntendedExpireTimeMillis` during the final leg. For locks not managed by the watchdog, this is based on `relativeLeaseTimeMillis`.
*   **`UnlockMessageListener`**: A component, managed by `UnlockMessageListenerManager` and instantiated per lock bucket. It subscribes to a Redis Pub/Sub channel pattern specific to its designated bucket (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`). Upon receiving a message on a specific channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`), it derives the `lockName` and `lockType` from the channel and parses the `UnlockType` from the message payload (which contains only the `UnlockType` string). It then signals the appropriate `LockSemaphoreHolder` or completes a `CompletableFuture` for asynchronous waiters.
*   **`UnlockMessageListenerManager`**: A Spring-managed bean that creates and manages the lifecycle of `UnlockMessageListener` instances (typically one per configured lock bucket).
*   **`UnlockType`**: A string constant published as the message payload to a specific lock's Pub/Sub unlock channel. It indicates the nature of the unlock event (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED_WAKEN_ALL"). The `UnlockMessageListener` uses this type, along with the `lockName` and `lockType` derived from the channel, to apply optimized waking strategies for waiting `CompletableFuture`s.
*   **Virtual Threads**: Lightweight threads provided by the JVM (Project Loom) used by this module to execute all lock operations asynchronously, preventing platform thread blockage and improving scalability. MDC context is propagated to these threads.
*   **Watchdog Factor (`watchdog.factor`)**: A multiplier used with `watchdog.interval` to calculate `safetyBufferMillis`.
*   **Watchdog Interval (`watchdog.interval`)**: The independent duration at which the `LockWatchdog`'s scheduled task runs to check and potentially renew monitored locks.
```
**FILE 2: `index.md`**
```markdown
# Destilink Framework: Redis Locking Module - Final Architecture & Plan

This document outlines the final consolidated architecture and plan for the `locking-redis-lock` module, derived from the analysis and synthesis of previous planning phases and updated to reflect the **Async-First design (utilizing Virtual Threads)**, **mandatory `redis-core` integration**, **centralized idempotency**, **refined watchdog mechanism**, **lock-type specific key schemas**, and **strict adherence to Destilink Framework guidelines**.

## Core Documentation

*   [Architecture Overview](architecture_overview.md) - *Updated to reflect Async-First with Virtual Threads, `redis-core` usage, and new operational semantics.*
*   [Configuration](configuration.md) - *Updated to detail `redis-core` property integration, refined `RedisLockProperties` structure (including `WatchdogProperties` and `Defaults`), and removal of obsolete properties.*
*   [Redis Key Schema](redis_key_schema.md) - *Updated for `redis-core` prefix usage and mandatory lock-type segments in keys.*
*   [Lua Scripts](lua_scripts_2.md) - *Consolidated and updated to reflect Redisson-inspired logic for ReadWriteLocks, emphasizing `ClusterCommandExecutor` usage, standardized return values, and mandatory idempotency wrappers.*
*   [Lock Acquisition Mechanism](lock_acquisition.md) - *Updated to highlight Async-First flow with Virtual Threads, `CompletableFuture` handling, `LockSemaphoreHolder` registration order, and `acquireTimeout` nuances.*
*   [Unlock Messaging](messaging.md) - *Updated to emphasize asynchronous message processing, `ClusterCommandExecutor` usage, and channel naming including lock-type segments.*
*   [Watchdog Mechanism](watchdog.md) - *Updated to clarify always-active watchdog service, conditional lock monitoring based on `safetyBufferMillis`, `PEXPIREAT` usage, and management of `originalLeaseTimeMillis` vs. `expiresAtMillis`.*
*   [Exception Handling](exception_handling.md) - *Updated to detail exception propagation in async flows, `redis-core` related exceptions, and new exception types related to refined operations.*
*   [Implementation Details](implementation_details.md) - *Updated to extensively cover Async-First with Virtual Threads, `redis-core` integration, guideline adherence, centralized idempotency, and refined operational semantics.*

## Supporting Documentation

*   [Performance Considerations](performance_considerations.md) - *Reviewed for Async-First/Virtual Thread impact and new configuration parameters.*
*   [Metrics](metrics.md) - *Reviewed for Async-First/Virtual Thread impact.*
*   [Testing Strategy](testing_strategy.md) - *Updated with explicit `test-support` module requirements, async testing with Virtual Threads, and scenarios for new watchdog/idempotency logic.*
*   [Modernization Plan & Assessment](modernization.md) - *Reviewed for comprehensive reflection of Async-First/Virtual Thread adoption, `redis-core` adoption, and all detailed plan changes.*
*   [Migration Notes](migration_notes.md) - *Reviewed for implications of Async-First/Virtual Threads, `redis-core` changes, property removals, and new key schema for users.*
*   [Glossary](glossary.md) - *Updated with new terms and clarifications related to Async-First/Virtual Threads, refined watchdog, idempotency, and key management.*
```
**FILE 3: `implementation_details.md`**
```markdown
# Redis Locking Module: Final Implementation Details

## 1. Introduction

This document provides detailed implementation specifics for key components and mechanisms within the consolidated `locking-redis-lock` module. Understanding these details is crucial for developing, debugging, and effectively utilizing the module. This plan synthesizes the core implementation aspects from previous planning phases, with a strong emphasis on the **Async-First approach (utilizing Virtual Threads)**, mandatory use of `redis-core` components, **centralized idempotency**, **refined watchdog logic**, **lock-type specific key schemas**, and strict adherence to framework guidelines.

**Crucially, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. These keys MUST include a mandatory lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation.**

## 2. Core Components and Responsibilities

The module is structured around several core components that collaborate to provide distributed locking functionality:

* `RedisLockAutoConfiguration` (Spring `@AutoConfiguration`):
  * The module's entry point for Spring Boot.
  * **Strictly adheres to no `@ComponentScan`**. All framework beans are explicitly defined via `@Bean` methods.
  * Conditionally enables the locking module based on the `destilink.fw.locking.redis.enabled` property.
  * Responsible for instantiating and wiring all shared service beans (including a Virtual Thread `ExecutorService`) and the `LockBucketRegistry`.
  * Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.

* `LockComponentRegistry` (Spring Bean):
  * A central registry injected into lock builders and other components.
  * Provides access to shared services: `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, an `ObjectProvider<LockMonitor>`, and the Virtual Thread `ExecutorService`.
  * Simplifies dependency management for lock instances and internal services.

* `RedisLockProperties` (Spring `@ConfigurationProperties`):
  * Binds global configurations from YAML files (e.g., `destilink.fw.locking.redis.*`).
  * Contains nested `WatchdogProperties` (for system-level watchdog config like `interval`, `factor`) and `Defaults` (for overridable lock implementation defaults like `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`).
  * Also holds top-level properties like `enabled`, `stateKeyExpiration`, and `responseCacheTtl` (critical for idempotency).
  * **Removed properties**: `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `maxLockNameLength`, `maxBucketNameLength`, `maxScopeLength`. `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout` (from `Defaults`).
  * Works in conjunction with `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` to ensure consistent Redis client configuration.

* `LockBucketConfig` (Non-Spring managed POJO):
  * Holds the *resolved* and *effective* configuration for a specific lock bucket.
  * Instantiated by `LockBucketBuilder`, initialized with global defaults from `RedisLockProperties.Defaults` and potentially from `RedisCoreProperties`.
  * Bucket-level defaults can be overridden programmatically. Watchdog eligibility for locks within the bucket is determined by the configured `LockOwnerSupplier` and other global/instance conditions.

* `LockBucketRegistry` (Spring Bean):
  * The primary factory for initiating the lock creation process.
  * Provides the entry point to the fluent builder API.
  * Injects the `LockComponentRegistry` and an initialized `LockBucketConfig` into the `LockBucketBuilder`.

* Builder Chain (`LockBucketBuilder` -> `LockConfigBuilder` -> `AbstractLockTypeConfigBuilder` & Subclasses):
  * Configures bucket-name, scope, custom `LockOwnerSupplier`, and overrides for `defaults.leaseTime`, `defaults.retryInterval`, `defaults.maxRetries`, `defaults.acquireTimeout`, and `stateKeyExpiration`. The `LockOwnerSupplier` (via `canUseWatchdog()`) and `leaseTime` (relative to `safetyBufferMillis`) are key to determining watchdog eligibility.

* `AbstractRedisLock` (Base Class for Locks):
  * Provides common asynchronous functionality executed on Virtual Threads: `lockAsync()`, `tryLockAsync()`, `unlockAsync()`.
  * **Synchronous `lock()`, `tryLock()`, and `unlock()` methods from `java.util.concurrent.locks.Lock` are implemented as blocking wrappers around these asynchronous operations.**
  * Manages the core lock acquisition/release lifecycle, including interaction with `LockSemaphoreHolder` (via `CompletableFuture`s) for waiting and `LockWatchdog` for lease extension.
  * Delegates Redis script execution to `RedisLockOperations`.
  * Defines abstract asynchronous methods like `tryAcquireLockScriptAsync()`, `releaseLockScriptAsync()` to be implemented by concrete lock types.
  * Handles MDC propagation to Virtual Threads.
  * Obtains/creates `LockSemaphoreHolder` and registers it with `UnlockMessageListenerManager` *before* the first Redis acquisition attempt.

* Concrete Lock Implementations (e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`, `RedisStampedLock`):
  * Extend `AbstractRedisLock` and **MUST implement the `AsyncLock` interface**.
  * Implement abstract asynchronous methods to provide specific Lua script names and arguments, utilizing `CompletableFuture`s.
  * Keys used by these locks MUST include their specific lock-type segment (e.g., `reentrant`, `state`, `readwrite`, `stamped`).
  * **`RedisReentrantLock`**: Manages reentrancy count and owner information within a **Redis Hash** associated with the `lockKey` (which includes the `reentrant` type segment).
  * **`RedisReadWriteLock`**: Contains inner `ReadLock` and `WriteLock`. These inner locks extend `AbstractRedisLock` and implement `AsyncLock`. Manages its state in a main Hash (`lockKey` with `readwrite` type segment) and uses `Individual Read Lock Timeout Key`s (also with `readwrite` type segment).

* `ScriptLoader` (Spring Bean):
  * Loads all necessary Lua scripts from the classpath during application startup.
  * Caches scripts for efficient reuse.

* `RedisLockOperations` (Spring Bean, typically `RedisLockOperationsImpl`):
  * Abstraction layer for Redis communication specific to lock operations.
  * **CRITICAL: Exclusively uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis command executions and Lua script invocations.**
  * **Centralized Idempotency Management**:
    *   Generates a unique `requestUuid` for each logical lock operation (e.g., a single `tryLock` call).
    *   Uses the *same `requestUuid`* for all internal retries (governed by `defaults.maxRetries` and `defaults.retryInterval`) of that single logical operation.
    *   Passes `requestUuid` and `responseCacheTtl` to all mutating Lua scripts.
  * **Lua Script Return Handling**: Parses structured responses from Lua scripts (status, `expiresAtMillis`, `originalLeaseTimeMillis`).
  * **Internal Retries**: Implements retry logic for individual Redis operations based on `defaults.maxRetries` and `defaults.retryInterval` for `RetryableLockException` types.

* `DefaultLockOwnerSupplier` (Spring Bean):
  * Provides the default mechanism for generating unique `ownerId`s. Implements `canUseWatchdog()` for watchdog eligibility checks.

* `RedisLockErrorHandler` (Spring Bean):
  * Centralizes logic for interpreting exceptions from Redis operations, including those from `ClusterCommandExecutor`.
  * Translates low-level exceptions into specific `AbstractRedisLockException` subtypes.

* `UnlockMessageListenerManager` (Spring Bean):
  * Manages the lifecycle of `UnlockMessageListener` instances.

* `UnlockMessageListener` (Per Bucket, managed by Manager):
  * Implements `org.springframework.data.redis.connection.MessageListener`. Each instance subscribes to a pattern for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
  * Derives `lockName` and `lockType` from the *channel name*. Parses `UnlockType` from the message payload.
  * Signals the `LockSemaphoreHolder` (completes its `CompletableFuture`).
  * Message processing is done on an optimized executor (e.g., Virtual Threads).

* `LockSemaphoreHolder` (Non-Spring managed, per `lockKey`):
  * An instance is associated with each `lockKey` that has waiting `CompletableFuture`s, managed *within* the per-bucket `UnlockMessageListener`.
  * Facilitates the completion of associated `CompletableFuture`s when an unlock notification is received.

* `LockWatchdog` (Spring Bean):
  * **Always active** if the module is enabled.
  * Background mechanism for automatic lease extension of active, application-instance-bound locks whose `userProvidedLeaseTime > safetyBufferMillis`.
  * **Periodically executes `watchdog_refresh_lock.lua` script using `RedisLockOperations` (asynchronously).**
  * Uses `watchdog.interval` for scheduling and `watchdog.factor` to calculate `safetyBufferMillis`.
  * Manages `expiresAtMillis` in Redis using `PEXPIREAT`, preserving the `originalLeaseTimeMillis` stored in `lockDataKey`.

* `LockMonitor` (Spring Bean, Optional):
  * Collects and exposes metrics related to lock operations using Micrometer.

### 2.A RedisReadWriteLock

*   **Key Characteristics:**
    *   Inner `ReadLock` and `WriteLock` extend `AbstractRedisLock` and implement `AsyncLock`.
    *   Reentrancy for reads and writes managed in a main Redis Hash (`lockKey` with `readwrite` type segment).
    *   Uses `Individual Read Lock Timeout Key`s (Redis Strings with TTL, also with `readwrite` type segment) for each reentrant read acquisition.
    *   Main lock Hash's TTL dynamically managed by Lua scripts based on its own lease (potentially watchdog-managed to `safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock timeouts.
*   **Operational Details (Async-First with Virtual Threads):**
    *   `tryAcquireLockAsync` for read/write locks calls respective Lua scripts (`try_read_lock.lua`, `try_write_lock.lua`) via `RedisLockOperations`.
    *   Lua scripts manage reentrancy counts, `mode`, `ownerId` in the main Hash, create/delete individual read timeout keys, and update `expiresAtMillis` and `originalLeaseTimeMillis` in `lockDataKey`.
    *   Watchdog can manage the main `RedisReadWriteLock` instance if eligible.

### 2.B RedisStampedLock

*   **Key Characteristics:**
    *   Uses a Redis Hash (`lockKey` with `stamped` type segment) to store `version`, `write_owner_id`, `read_holders`, etc.
    *   String-based stamps.
    *   Asynchronous operations for write, read, optimistic read, and conversions, all via Lua scripts and `RedisLockOperations`.
    *   Watchdog can manage write locks if eligible.

### 2.X Asynchronous Lock Operations (`AsyncLock` Interface)

*   **Purpose**: Provides non-blocking API returning `CompletableFuture`s, executed on Virtual Threads.
*   **Integration**: All concrete lock implementations implement `AsyncLock`.
*   **Waiting Mechanism**: For `lockAsync()`, `CompletableFuture`s are registered with `LockSemaphoreHolder` and completed by `UnlockMessageListener` or timeout.

## 3. Key Mechanisms Detailed

* **Lock Acquisition and Waiting**: See [Lock Acquisition Mechanism](lock_acquisition.md). Core flow is asynchronous on Virtual Threads, `LockSemaphoreHolder` registered *before* first Redis attempt, `acquireTimeout` respected with nuances.
* **Lock Release and Messaging**: See [Unlock Messaging](messaging.md). Lua scripts publish `UnlockType`, listener derives `lockName` and `lockType` from channel.
* **Lease Extension (Watchdog)**: See [Watchdog Mechanism](watchdog.md). Always active service, conditional monitoring, `PEXPIREAT`, `safetyBufferMillis`, `originalLeaseTimeMillis` preservation.
* **Exception Handling**: See [Exception Handling](exception_handling.md). `RedisLockErrorHandler` translates, exceptions carry context.
* **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` per logical operation (same for its internal retries). Lua scripts use this with `responseCacheTtl` to check/store results in a response cache. This is mandatory for all mutating scripts.
* **Configuration Loading**: `RedisLockProperties` (with `WatchdogProperties` and `Defaults` nested) for global, builder chain for programmatic overrides.
* **Lua-Only Redis Operations**: All lock state/TTL/metadata changes are exclusively via Lua scripts.

### 3.1 Centralized Idempotency Flow

```mermaid
flowchart TD
    A[Client Initiates Lock Operation] --> B[RedisLockOperationsImpl]
    B --> C[Generate Unique requestUuid for Logical Operation (used for all its internal retries)]
    C --> D[Construct Response Cache Key:<br/>&lt;prefix&gt;:&lt;bucketName&gt;:__resp_cache__:<lockType>:{&lt;lockName&gt;}:&lt;requestUuid&gt;]
    D --> E[Call Lua Script with requestUuid and responseCacheTtl]

    subgraph LuaScriptExecution [Lua Script Execution on Redis Server]
        LS1[Receive requestUuid, responseCacheTtl, and operation parameters] --> LS2{Check Response Cache for requestUuid?}
        LS2 -- "Cache Hit: Result Found" --> LS3[Return Cached Result<br/>Operation Already Completed]
        LS2 -- "Cache Miss: No Result Found" --> LS4[Execute Core Lock Operation Logic<br>(Reads/writes lockKey, lockDataKey)]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Success" --> LS6[Store Operation Result (status, expiresAtMillis, originalLeaseTimeMillis) in Response Cache<br/>Key: requestUuid, TTL: responseCacheTtl]
        LS6 --> LS7[Return Operation Result]
        LS5 -- "Failure" --> LS8[Return Error Result<br/>Do Not Cache Failures]
    end

    E --> LS1
    LS3 --> F[Return Cached Result to Client<br/>Idempotent Behavior Achieved]
    LS7 --> G[Return Fresh Result to Client<br/>Operation Completed Successfully]
    LS8 --> H[Return Error to Client<br/>Operation Failed, Can Be Retried by RedisLockOperationsImpl or propagated]

    subgraph RetryScenario [RedisLockOperationsImpl Internal Retry Scenario for a single logical op]
        I[Network Failure/Timeout During Initial Redis Command] --> J[RedisLockOperationsImpl Retries with Same requestUuid (up to defaults.maxRetries)]
        J --> B
    end
```

**Implementation Details:**
*   `RedisLockOperationsImpl` generates `requestUuid` per logical public call. This same `requestUuid` is used for internal retries of individual Redis commands for that call.
*   Response cache keys include the `lockType` segment.
*   Lua scripts store structured results (status, `expiresAtMillis`, `originalLeaseTimeMillis`) in the response cache.
```
**FILE 4: `lock_acquisition.md`**
```markdown
# Redis Locking Module: Final Lock Acquisition Mechanism

## 1. Introduction

This document describes the final, consolidated process by which locks are acquired in the `locking-redis-lock` module. The mechanism is designed for efficiency and reliability, primarily relying on Redis Pub/Sub for unlock notifications to minimize polling, while incorporating a fallback mechanism and ensuring adherence to the `java.util.concurrent.Lock` interface contract.

The core logic is implemented in `AbstractRedisLock.java` and utilizes `UnlockMessageListener.java` (managed by `UnlockMessageListenerManager.java`) and `LockSemaphoreHolder.java`. All Redis interactions for lock acquisition are handled **asynchronously** via `RedisLockOperations` (which uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`) and executed on **Virtual Threads**.

## 2. Core Principles

*   **Async-First Operations on Virtual Threads**: All lock acquisition attempts are fundamentally asynchronous, returning `CompletableFuture`s and executed on Virtual Threads. Synchronous `lock()` and `tryLock()` methods are blocking wrappers around these asynchronous calls.
*   **MDC Propagation**: MDC context is propagated to Virtual Threads executing lock logic.
*   **Non-Polling Preference**: `CompletableFuture`s associated with waiting tasks primarily wait passively for an unlock notification via Redis Pub/Sub.
*   **Atomic Operations**: Lock acquisition attempts are performed atomically using Lua scripts, executed asynchronously via `ClusterCommandExecutor`.
*   **`LockSemaphoreHolder` Registration First**: To prevent race conditions with missed Pub/Sub messages, the `LockSemaphoreHolder` (or its `CompletableFuture` equivalent for the current attempt) **must be obtained/created and registered with `UnlockMessageListenerManager` *before* the first Redis attempt to acquire the lock.**
*   **Lua Script Return Values**: Acquisition Lua scripts return a status code (`<0` for acquired, `>=0` for held by other with TTL/timestamp), `expiresAtMillis`, and `originalLeaseTimeMillis`.
*   **TTL-Aware Fallback**: If a Pub/Sub notification is missed or delayed, the waiting mechanism also times out based on `min(Returned TTL from script, Configured defaults.retryInterval)`. Upon timeout, a new lock acquisition attempt is made. `Thread.sleep()` is used for `defaults.retryInterval` delays on Virtual Threads.
*   **`acquireTimeout` Governs Overall Attempt**: The `defaults.acquireTimeout` property provides an approximate overall time limit for `tryLock` operations.
    *   **Non-Interruption of In-Flight Redis Operations**: `acquireTimeout` MUST NOT interrupt a Redis command already dispatched to `ClusterCommandExecutor`.
    *   **Redis Operation Result Precedence**: The result (success, failure, or specific exception) of an in-flight Redis operation takes precedence over a concurrently expiring `acquireTimeout`.
*   **`maxRetries` for Individual Redis Ops**: The `defaults.maxRetries` applies to retrying *individual failing Redis operations* within `RedisLockOperationsImpl`, not to the overall loop for acquiring a busy lock.
*   **Lightweight Lock Instances**: Designed for efficiency with shared components.

## 3. Lock Acquisition Scenarios

### 3.1. Synchronous Wait Scenario (`lock.lock()`) - Wrapper Implementation

This method acts as a blocking wrapper around the asynchronous acquisition logic, executed on a Virtual Thread.

```mermaid
flowchart TD
    A["Start: lock.lock() (Synchronous Wrapper)"] --> B["Dispatch to Virtual Thread: Call lockAsync()"]
    B --> C["Block on CompletableFuture.get() from lockAsync() (Platform thread parks, Virtual Thread runs/parks)"]
    C --> D{"CompletableFuture Completed?"}
    D -- "Yes (Lock Acquired)" --> E["Return"]
    D -- "Exceptional Completion (e.g., LockAcquisitionException, LockTimeoutException from acquireTimeout)" --> F["Unwrap Exception and Throw"]
    D -- "InterruptedException caught during get()" --> G["Re-assert Thread.interrupted() / Throw LockInterruptedException"]

    subgraph AsynchronousLockAcquisitionOnVirtualThread ["Internal Asynchronous Lock Acquisition (lockAsync() on Virtual Thread)"]
        ALA1["Generate Unique Request ID (requestUuid) for this logical lockAsync() call by RedisLockOperations"]
        ALA1 --> ALA1_5["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
        ALA1_5 --> ALA2["Loop (Until Lock Acquired or Interrupted)"]
        ALA2 --> ALA3["Call tryAcquireLockInternalAsync (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor). RedisLockOperations may retry individual Redis command failures up to defaults.maxRetries."]
        ALA3 --> ALA4{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
        ALA4 -- "Lock Acquired (status < 0)" --> ALA5["Register with LockWatchdog (if eligible, using originalLeaseTimeMillis and expiresAtMillis)"]
        ALA5 --> ALA6["Complete lockAsync() CompletableFuture successfully"]
        ALA4 -- "Lock Held by Other (status >= 0, provides current lock's PTTL/expiresAt)" --> ALA7["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval)"]
        ALA7 --> ALA8["Wait on LockSemaphoreHolder's CompletableFuture for signal or Wait Duration to elapse (Virtual Thread parks using Thread.sleep() for retryInterval or waits on CompletableFuture from semaphore)"]
        ALA8 -- "Signal Received (Pub/Sub)" --> ALA2
        ALA8 -- "Wait Duration Elapsed (Timeout)" --> ALA2
        ALA8 -- "Redis-related Exception from ALA3" --> ALA9["Complete lockAsync() CompletableFuture exceptionally"]
    end
    B --> ALA1
```

**Process Description (Synchronous Wrapper `lock()`):**

1.  **Dispatch to Virtual Thread**: `AbstractRedisLock.lock()` dispatches the call to `lockAsync()` to be executed on a Virtual Thread.
2.  **Block and Wait**: The calling platform thread blocks by calling `CompletableFuture.get()` on the future returned by `lockAsync()`.
3.  **Handle Completion**: As per the diagram, handling success, specific lock exceptions, or interruption.

### 3.2. Asynchronous Acquisition (`lockAsync()`) - Primary Flow on Virtual Thread

This describes the core, non-blocking asynchronous lock acquisition, executed on a Virtual Thread.

```mermaid
flowchart TD
    A["Start: lockAsync() (Primary API, executes on Virtual Thread)"] --> B["RedisLockOperations generates Unique Request ID (requestUuid) for this logical call"]
    B --> C["Create new CompletableFuture<Void> (lockFuture) to be returned to user"]
    C --> C_REG["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
    C_REG --> D["Recursive Attempt Function/Loop (tryAcquireLoop)"]
    D --> E["Call tryAcquireLockInternalAsync (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor). RedisLockOperations may retry individual Redis command failures up to defaults.maxRetries."]
    E --> F{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
    F -- "Success (Lock Acquired, status < 0)" --> G["Register with LockWatchdog (if eligible, using originalLeaseTimeMillis and expiresAtMillis)"]
    G --> H["lockFuture.complete(null)"]
    H --> I["Return lockFuture to user (completed)"]
    F -- "Failure (Lock Held by Other, status >= 0, provides current lock's PTTL/expiresAt)" --> J["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval)"]
    J --> K["Associate lockFuture with LockSemaphoreHolder (to be completed on signal or timeout)"]
    K --> L["Return lockFuture to user (still incomplete, waiting for signal/retry)"]
    F -- "Error from E (e.g., Redis connection error after retries)" --> M["lockFuture.completeExceptionally(exception)"]
    M --> I
```

**Process Description (Asynchronous `lockAsync()` on Virtual Thread):**

1.  **Initiate & Create Future**: `AbstractRedisLock.lockAsync()` is called (runs on a VT). A `CompletableFuture<Void>` (`lockFuture`) is created.
2.  **Register Semaphore Holder**: `LockSemaphoreHolder` is obtained/created and registered with `UnlockMessageListenerManager` *before* the first Redis attempt.
3.  **Attempt Atomic Acquisition (Asynchronously via `RedisLockOperations`):**
    *   `RedisLockOperations` generates `requestUuid`.
    *   `tryAcquireLockInternalAsync()` invokes the Lua script. `RedisLockOperations` handles internal retries for this Redis command if it fails transiently.
4.  **Handle Script Result (Chained)**:
    *   **Success**: Lock acquired. Register with watchdog if eligible. `lockFuture` is completed.
    *   **Lock Held**: Calculate wait time (`min(TTL, defaults.retryInterval)`). `lockFuture` is associated with `LockSemaphoreHolder` to wait for Pub/Sub signal or this calculated timeout.
    *   **Error**: `lockFuture` is completed exceptionally.
5.  **Return Future**: `lockFuture` is returned to the caller immediately (may be incomplete).
6.  **Completion by `UnlockMessageListener`**: If waiting, when `UnlockMessageListener` receives a Pub/Sub message, it signals `LockSemaphoreHolder`, which then typically triggers a re-attempt (re-enters the loop D) for the associated `lockFuture`.

### 3.3. Timeout Scenario (`tryLockAsync(long time, TimeUnit unit)`) - Primary Flow on Virtual Thread

This flow is similar to `lockAsync()`, but respects an overall `defaults.acquireTimeout` (derived from `time`, `unit`).

```mermaid
flowchart TD
    A[Start: tryLockAsync(timeout, unit) on VT] --> B[Calculate Deadline (startTime + timeout from defaults.acquireTimeout)]
    B --> C[Create new CompletableFuture<Boolean> (tryLockFuture)]
    C --> C_REG["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
    C_REG --> D["Recursive Attempt Function/Loop (tryAcquireLoopWithTimeout)"]
    D --> E["Check: Is currentTime < Deadline?"]
    E -- No (Deadline Reached) --> L[tryLockFuture.complete(false)]
    E -- Yes --> F["Call tryAcquireLockInternalAsync (Lua via RedisLockOperations). RLOps handles its internal retries."]
    F --> G{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
    G -- Success (Lock Acquired, status < 0) --> H["Register with LockWatchdog (if eligible)"]
    H --> I["tryLockFuture.complete(true)"]
    I --> X[Return tryLockFuture to user (completed)]

    G -- Failure (Lock Held by Other, status >= 0, provides PTTL/expiresAt) --> J["Check: Is currentTime < Deadline? (Re-check after Redis call)"]
    J -- No (Deadline Reached) --> L
    J -- Yes --> K["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval, Remaining Overall Timeout until Deadline)"]
    K -- "Wait Duration <= 0" --> L
    K -- "Wait Duration > 0" --> M["Associate tryLockFuture with LockSemaphoreHolder for signal or Wait Duration"]
    M --> N["Return tryLockFuture to user (still incomplete, waiting)"]

    G -- "Error from F (e.g., Redis connection error after RLOps retries)" --> O["tryLockFuture.completeExceptionally(exception)"]
    O --> X
    L --> X
```

**Process Description (Asynchronous `tryLockAsync()` on Virtual Thread):**

1.  **Initiate, Deadline, Future**: `AbstractRedisLock.tryLockAsync()` calculates `deadline` based on `defaults.acquireTimeout`. A `CompletableFuture<Boolean>` (`tryLockFuture`) is created.
2.  **Register Semaphore Holder**: As in `lockAsync()`.
3.  **Attempt Loop with Timeout Check**:
    *   Before each Redis attempt, check if `currentTime < deadline`. If not, complete `tryLockFuture` with `false`.
    *   Attempt atomic acquisition as in `lockAsync()`.
4.  **Handle Script Result & Check Deadline**:
    *   **Success**: `tryLockFuture` completes with `true`.
    *   **Lock Held**: Re-check `currentTime < deadline`. If deadline met, complete `false`. Otherwise, calculate wait duration considering remaining time until deadline. If wait duration is `<=0`, complete `false`. Else, associate with `LockSemaphoreHolder`.
    *   **Error**: `tryLockFuture` completes exceptionally.
    *   **`acquireTimeout` Nuances**: If `acquireTimeout` expires *during* an in-flight Redis command, the command completes. If it acquired the lock, `tryLockFuture` is `true`. If it failed with a Redis error, that error is propagated. `tryLockFuture` is only `false` due to timeout if the deadline passes *between* Redis attempts or after a non-acquiring, non-erroring Redis attempt.

## 4. Key Components Involved

*   **`AbstractRedisLock`**: Orchestrates acquisition on Virtual Threads, delegates to `RedisLockOperations`, manages `LockSemaphoreHolder` interaction.
*   **`RedisLockOperations`**: Executes Lua scripts via `ClusterCommandExecutor`, generates `requestUuid`, handles internal retries for individual Redis ops, parses structured Lua responses.
*   **`UnlockMessageListenerManager` / `UnlockMessageListener`**: Manage Pub/Sub, signal `LockSemaphoreHolder`.
*   **`LockSemaphoreHolder`**: Manages `CompletableFuture`s for waiting tasks.
*   **Lua Scripts**: Perform atomic operations, return structured status/`expiresAtMillis`/`originalLeaseTimeMillis`.
*   **Virtual Thread Executor**: Executes all lock logic.
```
**FILE 5: `messaging.md`**
```markdown
# Redis Locking Module: Final Unlock Messaging Mechanism

## 1. Introduction

This document describes the messaging mechanism used by the `locking-redis-lock` module, specifically utilizing Redis Publish/Subscribe (Pub/Sub) for unlock notifications. This is a core component of the non-polling lock acquisition strategy, designed to efficiently signal waiting `CompletableFuture`s (managed by `LockSemaphoreHolder` and executed on Virtual Threads) when a lock becomes available.

## 2. Redis Publish/Subscribe (Pub/Sub) Overview

Redis Pub/Sub allows publishers to send messages to channels, and subscribers to listen to these channels without direct knowledge of each other.

*   **Publishers**: Lua scripts executed upon successful lock release.
*   **Channels**:
    *   The `UnlockMessageListener` subscribes to a channel pattern like `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`.
    *   The specific lock instance (e.g., `{order123}`) and its `lockType` (e.g., `reentrant`) are part of the actual channel name to which messages are published (e.g., `<prefix>:<bucketName>:__unlock_channels__:reentrant:{order123}`).
*   **Message Payload**: The message published by Lua scripts to a specific lock's Pub/Sub channel contains *only* an `UnlockType` string (e.g., "REGULAR_RELEASE", "RW_WRITE_RELEASED"). The `UnlockMessageListener` derives the `lockName` and `lockType` from the channel name it received the message on.
*   **Subscribers**: `UnlockMessageListener` instances, one per bucket, managed by `UnlockMessageListenerManager`.

## 3. Unlock Notification Mechanism

1.  **Publish on Unlock**: When a lock is successfully released (typically via an atomic Lua script like `unlock.lua` or `unlock_state_lock.lua`), the script publishes a message containing *only* an `UnlockType` string to the specific lock's Pub/Sub channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`).
    *   The `UnlockType` (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED") indicates the nature of the unlock event.
2.  **Subscription and Listening**:
    *   When a task (on a Virtual Thread) attempts to acquire a lock and finds it held, its associated `CompletableFuture` is registered with a `LockSemaphoreHolder` for that specific `lockKey`. The `LockSemaphoreHolder` itself is registered with the `UnlockMessageListenerManager` *before* the first Redis acquisition attempt.
    *   The `UnlockMessageListenerManager` ensures that an `UnlockMessageListener` for the relevant `bucketName` is active. This listener subscribes to a pattern for its bucket-specific Pub/Sub channels, including the `lockType` segment (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
3.  **Signal Waiting Futures**:
    *   When an `UnlockMessageListener` receives a message:
        *   It derives the specific `lockName` (e.g., `{order123}`) and `lockType` from the *channel name* on which the message was received.
        *   It parses the `UnlockType` from the message payload.
        *   It retrieves the associated `LockSemaphoreHolder` for the derived `lockKey` (which includes `lockName` and `lockType`).
        *   Based on the `UnlockType`, it intelligently signals the `LockSemaphoreHolder`, which in turn completes the waiting `CompletableFuture`(s).
4.  **Wake Up and Retry**: Completing the `CompletableFuture` wakes up the Virtual Thread associated with the lock acquisition attempt. This awakened task then re-attempts to acquire the lock.

## 4. Key Components

*   **`UnlockMessageListenerManager` (Bean)**:
    *   Manages the lifecycle of `UnlockMessageListener` instances.
    *   Handles registration of `LockSemaphoreHolder` instances.
*   **`UnlockMessageListener` (Per Bucket, Managed by Manager)**:
    *   Implements `org.springframework.data.redis.connection.MessageListener`.
    *   Subscribes to a Redis Pub/Sub channel pattern specific to its `bucketName` and including `lockType` (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
    *   Internally manages a map of `LockSemaphoreHolder` instances, keyed by the full `lockKey`.
    *   The `onMessage()` method processes messages by deriving `lockName` and `lockType` from the channel and parsing `UnlockType` from the payload, using an optimized executor (e.g., Virtual Threads).
    *   Signals the `LockSemaphoreHolder` associated with the derived `lockKey`.
*   **`LockSemaphoreHolder` (Non-Spring managed, per `lockKey`)**:
    *   Manages `CompletableFuture`s waiting for a specific lock.
    *   Provides methods to register a `CompletableFuture` and to signal/complete it.

## 5. Unlock Channel Key Schema

As defined in the [Redis Key Schema](redis_key_schema.md) document:
The `UnlockMessageListener` subscribes to a channel pattern such as `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`.
The message published by Lua scripts to a specific lock's channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`) will *only* contain an `UnlockType` string (e.g., "REGULAR_RELEASE"). The `UnlockMessageListener` derives the specific `lockName` (e.g., `{<lockName>}`) and `lockType` from the channel name itself.
Example: Listener subscribes to `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*`. A lock release for `reentrant` lock `{order123}` would publish the `UnlockType` to `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}`.

## 5.A. Defined `UnlockType` Values and Listener Logic

The `UnlockType` string published by Lua scripts provides crucial information to the `UnlockMessageListener` for optimized waking of waiting tasks. The listener derives the `lockName` and `lockType` from the channel name and uses the `UnlockType` from the message payload to decide its signaling strategy.

(UnlockTypes remain largely the same as previously defined, e.g., `REENTRANT_FULLY_RELEASED`, `RW_WRITE_RELEASED_WAKEN_ALL`, etc. The listener logic focuses on completing one or more `CompletableFuture`s managed by `LockSemaphoreHolder`.)

*   **`REENTRANT_FULLY_RELEASED`**: Signals one `CompletableFuture`.
*   **`NON_REENTRANT_RELEASED`**: Signals one `CompletableFuture`.
*   **`STATE_LOCK_RELEASED_STATE_UNCHANGED` / `_UPDATED`**: Signals one `CompletableFuture`.
*   **`RW_READ_RELEASED_WAKEN_READERS`**: May complete multiple `CompletableFuture`s if multiple read waiters exist.
*   **`RW_READ_RELEASED_WAKEN_SINGLE_WRITER`**: Signals one `CompletableFuture` (likely for a writer).
*   **`RW_WRITE_RELEASED_WAKEN_ALL`**: May complete multiple `CompletableFuture`s (for readers and one writer).
*   **`STAMPED_READ_RELEASED` / `_WRITE_RELEASED` / `_CONVERTED_TO_READ` / `_TO_WRITE`**: Similar logic, completing relevant waiting `CompletableFuture`s.

## 6. Reliability and Considerations

*   **Message Delivery**: Redis Pub/Sub is "fire-and-forget."
*   **Fallback Mechanism**: The lock acquisition logic includes a fallback. Waiting tasks also have a timeout based on the current lock holder's TTL (obtained from the acquisition attempt) or `defaults.retryInterval`. If an unlock notification is missed, the waiting task will eventually time out its wait on the `CompletableFuture` (or the sleep for `retryInterval`) and re-attempt acquisition.
*   **Message Content & Channel**: Payload is *only* `UnlockType`. `lockName` and `lockType` are derived from the channel.
*   **Executor for Listeners**: Using Virtual Threads for `UnlockMessageListener`'s `onMessage` processing is critical to prevent blocking platform threads.
*   **Stale Signals**: The `CompletableFuture`-based waiting inherently handles "stale" signals correctly; if a future is already completed (e.g., by timeout), a late Pub/Sub signal will have no effect on it.
*   **`LockSemaphoreHolder` Registration**: Must occur *before* the first Redis acquisition attempt to avoid race conditions.
```
**FILE 6: `watchdog.md`**
```markdown
# Redis Locking Module: Final Watchdog Mechanism

## 1. Introduction

The `LockWatchdog` is a critical component in the `locking-redis-lock` module. Its primary responsibility is to prevent distributed locks from expiring prematurely while they are still actively and legitimately held by an application instance. This mechanism is essential for long-running operations. This document details the refined design of the watchdog, which is **always active** if the module is enabled, but conditionally monitors locks.

## 2. Purpose and Design Principles

*   **Lease Extension Pattern**: The watchdog implements lease extension. Locks are acquired with an intended `relativeLeaseTimeMillis`.
*   **Prevent Premature Expiration**: It periodically renews the lock's TTL in Redis (using `PEXPIREAT`) as long as the acquiring application instance is alive and the lock is registered with the watchdog.
*   **`originalLeaseTimeMillis` Preservation**: The watchdog **never** modifies the `originalLeaseTimeMillis` (user's intended full lease duration) stored in the `lockDataKey` in Redis. It only uses this value to calculate the target `expiresAtMillis` for renewal.
*   **Resilience**: If the application instance crashes or the lock is explicitly released, the watchdog stops renewing, allowing the lock to expire naturally.
*   **Redis Time Precision**: All expiry calculations in Lua scripts use `redis.call('TIME')`.

## 3. Core Components and Configuration

*   **`LockWatchdog.java` (Bean)**:
    *   A Spring-managed singleton bean, **always instantiated and active** if `destilink.fw.locking.redis.enabled=true`.
    *   Dependencies: `RedisLockOperations` (for Lua script execution).
    *   Maintains an internal `ConcurrentHashMap<String, LockInfo>` (`monitoredLocks`).
*   **`LockWatchdog.LockInfo` (Inner Class)**:
    *   Stores `ownerId`, `userIntendedExpireTimeMillis` (absolute Unix timestamp calculated from `originalLeaseTimeMillis` at registration), and `currentAbsoluteExpiresAtMillis` (the current `expiresAtMillis` of the lock in Redis, updated after each successful refresh).
*   **Global Configuration (`RedisLockProperties.WatchdogProperties`)**:
    *   **`interval` (Duration)**: Interval at which the watchdog's scheduled task runs to check and potentially extend leases.
    *   **`factor` (double)**: Multiplier for `interval` to calculate `safetyBufferMillis`.
    *   `corePoolSize`, `threadNamePrefix`, `shutdownAwaitTermination` for its executor.
    *   (Note: `operationMaxRetries` and `operationTimeout` are removed from watchdog config; `RedisLockOperationsImpl` handles retries for its operations using `Defaults.maxRetries` and `Defaults.retryInterval`.)
*   **`safetyBufferMillis`**: Calculated as `watchdogProperties.getInterval().toMillis() * watchdogProperties.getFactor()`.
*   **Instance-Level Activation Conditions (Watchdog Monitoring)**: The watchdog monitors a specific lock instance if **all** the following are true:
    1.  Global Redis locking is enabled (`destilink.fw.locking.redis.enabled` is `true`).
    2.  The lock is "application-instance-bound": `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returns `true`.
    3.  The lock's `userProvidedLeaseTime` (the `relativeLeaseTimeMillis` provided at acquisition/extension) is greater than `safetyBufferMillis`.

## 4. Operational Flow

### 4.1. Lock Registration & Initial TTL

1.  When a lock is successfully acquired by `AbstractRedisLock`:
    *   The `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) is known.
    *   The `originalLeaseTimeMillis` (which is this `userProvidedLeaseTime`) and the initial `expiresAtMillis` are stored in the `lockDataKey` in Redis by the acquisition Lua script.
2.  `AbstractRedisLock` checks watchdog monitoring eligibility:
    *   **If NOT Monitored**: The initial `lockKey` TTL in Redis is set based on the full `userProvidedLeaseTime`.
    *   **If Monitored**: The initial `lockKey` TTL in Redis is set to `safetyBufferMillis` (via `PEXPIREAT currentTime + safetyBufferMillis`).
3.  If monitored, `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, ownerId, userProvidedLeaseTime, initialExpiresAtMillisFromRedis)`.
4.  The `LockWatchdog` stores a `LockInfo` with `ownerId`, `userIntendedExpireTimeMillis = initialExpiresAtMillisFromRedis - safetyBufferMillis + userProvidedLeaseTime` (recalculating the user's intended absolute expiry based on the full lease), and `currentAbsoluteExpiresAtMillis = initialExpiresAtMillisFromRedis`.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `extendLocks()` method is scheduled to run every `watchdog.interval`.
2.  It iterates through `monitoredLocks`. For each `LockInfo`:
    *   Calculates `remainingUserTimeMillis = lockInfo.getUserIntendedExpireTimeMillis() - System.currentTimeMillis()`.
    *   **If `remainingUserTimeMillis <= 0` (User's intended lease expired):**
        *   Unregister the lock from `monitoredLocks`.
        *   Log a warning if the lock still appears to be held by this owner in Redis (this indicates the user might have expected it to live longer but didn't extend it, or a final refresh failed).
    *   **Else (User's intended lease has not expired):**
        *   Determine `targetExpiresAtMillis` for the refresh:
            *   **Final Leg (`0 < remainingUserTimeMillis <= safetyBufferMillis`):** `targetExpiresAtMillis = lockInfo.getUserIntendedExpireTimeMillis()`.
            *   **Standard Operation (`remainingUserTimeMillis > safetyBufferMillis`):** `targetExpiresAtMillis = System.currentTimeMillis() + safetyBufferMillis`.
        *   Execute `watchdog_refresh_lock.lua` via `RedisLockOperations` (which handles its own retries for this Redis command).
            *   **Lua Script (`watchdog_refresh_lock.lua`)**:
                *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `targetExpiresAtMillis`, `requestUuid`, `responseCacheTtl`.
                *   Implements idempotency wrapper.
                *   Atomically checks if `lockKey` in Redis is still held by `expectedOwnerId`.
                *   If owner matches:
                    *   Updates `expiresAtMillis` in `lockDataKey` to `targetExpiresAtMillis`.
                    *   Sets `PEXPIREAT` on `lockKey` to `targetExpiresAtMillis`.
                    *   Returns success status, new `expiresAtMillis` from Redis, and `originalLeaseTimeMillis` (read from `lockDataKey`, unchanged).
                *   If owner mismatch or lock not found, returns failure.
        *   **Result Handling**:
            *   **Success**: Update `lockInfo.setCurrentAbsoluteExpiresAtMillis()` with the new `expiresAtMillis` returned from the script. If it was the "Final Leg" refresh, unregister the lock from `monitoredLocks`.
            *   **Failure (owner mismatch, lock gone, or Redis error after retries)**: Remove `lockKey` from `monitoredLocks`. Log a WARNING.

### 4.3. Lock Unregistration (User Initiated)

1.  When `AbstractRedisLock.unlock()` is called.
2.  `unlock()` calls `lockWatchdog.unregisterLock(lockKey, ownerId)`.
3.  The `LockWatchdog` removes the entry from `monitoredLocks`.

### 4.4. User-Initiated Lock Extension (`AbstractRedisLock.extendLeaseAsync`)

1.  User calls `extendLeaseAsync(newRelativeLeaseTime)`.
2.  `AbstractRedisLock` calls `RedisLockOperations.extendLeaseAsync(...)` which executes `extend_lock.lua`.
    *   **Lua Script (`extend_lock.lua`)**:
        *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `newRelativeLeaseTime`, `requestUuid`, `responseCacheTtl`.
        *   Implements idempotency wrapper.
        *   Atomically checks ownership.
        *   If owner matches:
            *   Updates `originalLeaseTimeMillis` in `lockDataKey` to `newRelativeLeaseTime`.
            *   Calculates `newExpiresAtMillis` based on `currentTime + newRelativeLeaseTime`.
            *   Updates `expiresAtMillis` in `lockDataKey` to this `newExpiresAtMillis`.
            *   Sets `PEXPIREAT` on `lockKey` to this `newExpiresAtMillis`.
            *   Returns success, `newExpiresAtMillis`, and the updated `originalLeaseTimeMillis`.
3.  `AbstractRedisLock` then calls `lockWatchdog.updateRegistration(lockKey, ownerId, newOriginalLeaseTimeMillisFromScript, newExpiresAtMillisFromScript)`.
    *   The watchdog re-evaluates eligibility. If still monitored, it updates its `LockInfo` with the new `userIntendedExpireTimeMillis` (derived from `newOriginalLeaseTimeMillisFromScript`) and `currentAbsoluteExpiresAtMillis`. If the new lease makes it no longer eligible (e.g., new lease is too short), it unregisters it.

## 5. Key Considerations

*   **Atomicity of Lua Scripts**: `watchdog_refresh_lock.lua` and `extend_lock.lua` are critical for atomic check-and-set operations.
*   **Idempotency**: All watchdog-related Lua scripts must be idempotent.
*   **Configuration Alignment**: `watchdog.interval` and `watchdog.factor` (determining `safetyBufferMillis`) must be sensible.
*   **Clock Skew**: Using Redis `TIME` in Lua and `PEXPIREAT` helps manage consistency. The `safetyBufferMillis` provides a buffer.
```
**FILE 7: `architecture_overview.md`**
```markdown
# Redis Locking Module: Final Architecture Overview

## 1. Introduction

This document outlines the consolidated architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, performant, and developer-friendly distributed locking mechanism using Redis. The architecture emphasizes shared, Spring-managed components, efficient non-polling lock acquisition, clear configuration hierarchies, atomicity through Lua scripts, centralized idempotency, and strict adherence to **Destilink Framework guidelines (`/.amazonq/rules/guidelines.md`)**.

This architecture adopts an **Async-First approach, leveraging Virtual Threads for execution**, where all core lock operations are fundamentally asynchronous using `CompletableFuture`s, and synchronous `java.util.concurrent.Lock` implementations act as wrappers. It mandates the use of the internal `redis-core` module, particularly `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis interactions.

## 2. Core Architectural Principles

*   **Strict Guideline Adherence**: Implementation follows all rules from `/.amazonq/rules/guidelines.md`.
*   **Async-First Design with Virtual Threads**: All lock operations are primarily implemented asynchronously using `CompletableFuture`s and executed on Virtual Threads. Synchronous `Lock` methods are blocking wrappers.
*   **Mandatory `redis-core` Usage**: All Redis interactions via `ClusterCommandExecutor`. Configuration uses `RedisCoreProperties`.
*   **Atomicity via Lua Scripts**: Critical operations use Lua for atomicity. All mutating scripts are idempotent.
*   **Spring-Managed Shared Components**: Services like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations` are explicit Spring beans.
*   **Centralized Component Access**: `LockComponentRegistry` for shared service DI.
*   **Efficient Non-Polling Lock Acquisition**: Relies on Redis Pub/Sub (`UnlockMessageListener`, `LockSemaphoreHolder`). `LockSemaphoreHolder` registered *before* first Redis attempt.
*   **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` per logical operation (same for its internal retries). Lua scripts use this with `responseCacheTtl` for a response cache. Mandatory for all mutating scripts.
*   **Contextual and Structured Exception Handling**: `AbstractRedisLockException` hierarchy with `ExceptionMarkerProvider`.
*   **Explicit Configuration & Dependency Injection**: No `@ComponentScan`, explicit `@Bean`s, constructor injection.
*   **Standardized Key Construction with Lock-Type Segments**: Keys via `redis-core` utilities, MUST include a lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation (e.g., `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`).
*   **Clear Configuration Hierarchy**: `RedisLockProperties` (with nested `WatchdogProperties`, `Defaults`) for global YAML settings. Programmatic builders for bucket/instance overrides.
*   **Lightweight Lock Instances & Resource Sharing**: Lock objects are lightweight, delegating to shared services.
*   **Distributed Reentrancy**: Managed using Redis Hashes.
*   **Enhanced Logging & Observability**: SLF4J, MDC (propagated to Virtual Threads), `ExceptionMarkerProvider`, Micrometer metrics.
*   **Refined Watchdog Mechanism**: Always-active service, conditional monitoring based on `safetyBufferMillis` (`watchdog.interval * watchdog.factor`), `PEXPIREAT` usage, preserves `originalLeaseTimeMillis` in Redis.
*   **Lua-Only Redis Operations**: All lock state/TTL/metadata changes exclusively via Lua scripts.

## 3. Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code (Uses Virtual Threads implicitly via Lock API)"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["<code>LockBucketRegistry</code> (Bean)"];
        LBB["<code>LockBucketBuilder</code>"];
        LCB["<code>LockConfigBuilder</code>"];
        ALTCB["<code>AbstractLockTypeConfigBuilder</code> & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic (Async-First on Virtual Threads)"]
        direction LR;
        ARL["<code>AbstractRedisLock</code> (Base Class, uses Virtual Thread Executor)"];
        RLI["Concrete Lock Implementations<br>(e.g., `RedisReentrantLock`)<br>ALL Implement `AsyncLock`."];
        LSH["<code>LockSemaphoreHolder</code> (Per Lock Key, manages CompletableFutures)"];
    end
    
    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        VTExec["Virtual Thread Executor (Bean)"]
        LCR["<code>LockComponentRegistry</code> (Bean)"];
        SL["<code>ScriptLoader</code> (Bean)"];
        UMLM["<code>UnlockMessageListenerManager</code> (Bean)"];
        UML["<code>UnlockMessageListener</code> (Per Bucket, uses VT for onMessage)"];
        LW["<code>LockWatchdog</code> (Bean, always active, uses VT)"];
        ROps["<code>RedisLockOperations</code> (Bean, uses VT, central idempotency)"];
        LMP["<code>LockMonitor</code> (Bean, Optional)"];
        LOS["<code>DefaultLockOwnerSupplier</code> (Bean)"];
        LERRH["<code>RedisLockErrorHandler</code> (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["<code>RedisLockProperties</code> (Global YAML-backed with WatchdogProperties & Defaults)"];
        LBC["<code>LockBucketConfig</code> (Programmatically Resolved)"];
        RAutoConfig["<code>RedisLockAutoConfiguration</code> (@AutoConfiguration)"];
        RCProps["<code>RedisCoreProperties</code> (from redis-core)"];
    end

    subgraph ExternalSystems ["External Systems"]
        Redis["Redis (Cluster)"];
        CCExec["<code>ClusterCommandExecutor</code> (from redis-core)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;
    
    ARL -- "uses" --> VTExec;
    ARL -- "uses services from" --> LCR;
    ARL -- "uses for waiting" --> LSH;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;
    LCR -- "provides" --> VTExec;

    UMLM -- "manages & provides" --> UML;
    UML -- "manages `LockSemaphoreHolder` map" --> LSH;
    UML -- "listens to Pub/Sub from" --> Redis;
    UML -- "signals" --> LSH;
    
    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> VTExec;
    RAutoConfig -- "defines bean" --> LW; % LW is always active
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "enables" --> RLP;
    RAutoConfig -- "uses" --> RCProps;

    RLP -- "provides global defaults for" --> LBC;
    LBC -- "provides config to" --> LBB;

    ROps -- "uses" --> SL;
    ROps -- "interacts with" --> CCExec;
    ROps -- "uses" --> LERRH;
    LW -- "extends lease via" --> ROps; % Watchdog uses ROps
    
    CCExec -- "communicates with" --> Redis;

    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style ARL fill:#adebad;
    style LCR fill:#ccffcc;
    style VTExec fill:#ccffcc;
    style RLP fill:#ffcc99;
    style Redis fill:#ffcccc;
```

## 4. Key Components and Responsibilities

*   **Configuration Components**: `RedisLockProperties` (with nested `WatchdogProperties` for `interval`, `factor`, and `Defaults` for `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`), `LockBucketConfig`, `RedisLockAutoConfiguration`.
*   **Shared Spring-Managed Services**: `LockComponentRegistry`, `ScriptLoader`, `UnlockMessageListenerManager`, `UnlockMessageListener` (subscribes to `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`), `LockWatchdog` (always active, conditional monitoring), `RedisLockOperations` (central `requestUuid` generation, idempotency, internal retries for Redis ops), `DefaultLockOwnerSupplier` (with `canUseWatchdog()`), `RedisLockErrorHandler`, `LockMonitor`, Virtual Thread `ExecutorService`.
*   **Core Locking Logic & Instantiation**: `LockBucketRegistry`, Builders, `AbstractRedisLock` (uses Virtual Threads, registers `LockSemaphoreHolder` first), Concrete Lock Implementations (all implement `AsyncLock`, use lock-type specific keys), `LockSemaphoreHolder`.

## 5. Redis Key Schema

Keys include mandatory lock-type segments (e.g., `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`). `lockDataKey` (Redis Hash) stores `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`. (Details in `redis_key_schema.md`).

## 6. Data Flow and Interactions

*   **Lock Acquisition**: Asynchronous on Virtual Threads. `LockSemaphoreHolder` registered *before* first Redis attempt. `RedisLockOperations` handles `requestUuid`, Lua execution, and retries for individual Redis commands. `acquireTimeout` governs overall attempt with non-interruption of in-flight Redis ops.
*   **Lock Release & Unlock Notification**: Lua script releases lock, publishes `UnlockType`. Listener derives `lockName` and `lockType` from channel.
*   **Lease Extension (Watchdog)**: Watchdog service is always active. Monitors eligible locks (`userLeaseTime > safetyBufferMillis` & instance-bound). Uses `PEXPIREAT` via `watchdog_refresh_lock.lua`. Preserves `originalLeaseTimeMillis` from `lockDataKey`.
*   **Idempotency**: All mutating Lua scripts use `requestUuid` and `responseCacheTtl` to check/store results in a response cache.

## 7. Adherence to Destilink Framework Guidelines

Strict compliance with all guidelines, including no `@ComponentScan`, explicit dependencies, standard configuration, and MDC propagation to Virtual Threads.
```
**FILE 8: `class_hierarchy.md`**
```markdown
# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the comprehensive class hierarchy for the `locking-redis-lock` module. The hierarchy supports all documented lock types, adhering to the **Async-First approach (utilizing Virtual Threads)** and aligning with standard Java patterns and Destilink Framework guidelines.

## 2. Core Design Principles

*   **Async-First with Virtual Threads**: Core operations are asynchronous, returning `CompletableFuture`s, executed on Virtual Threads. Synchronous `java.util.concurrent.locks.Lock` methods serve as blocking wrappers.
*   **Standard Java Compliance**: Integrates with `java.util.concurrent.locks.Lock` and `ReadWriteLock`.
*   **Clear Abstraction**: `AbstractRedisLock` encapsulates common Redis interaction logic.
*   **Modularity**: Specific lock types are concrete implementations.
*   **Configuration Driven**: Behavior influenced by `RedisLockProperties` (including `WatchdogProperties`, `Defaults`) and builder configurations. Watchdog eligibility depends on `LockOwnerSupplier.canUseWatchdog()` and `leaseTime > safetyBufferMillis`.
*   **Robust Error Handling**: Exceptions extend `AbstractRedisLockException`.

## 3. Class Hierarchy Diagram

```mermaid
classDiagram
    direction TB

    class Lock {
        <<interface>>
        +lock()
        +tryLock()
        +unlock()
        +newCondition()
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncLock {
        <<interface>>
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +tryLockAsync(long, TimeUnit) CompletableFuture
        +unlockAsync() CompletableFuture
    }
    AsyncLock --|> Lock

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    AsyncReadWriteLock --|> ReadWriteLock

    class AbstractRedisLock {
        <<Abstract>>
        #String lockKeyWithTypeSegment
        #String ownerId
        #RedisLockOperations redisLockOperations
        #RedisLockProperties redisLockProperties
        #LockOwnerSupplier lockOwnerSupplier
        #LockSemaphoreHolder lockSemaphoreHolder
        #ExecutorService virtualThreadExecutor
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +unlockAsync() CompletableFuture
        +lock() // blocking wrapper
        +tryLock() // blocking wrapper
        +unlock() // blocking wrapper
        #<i>Registers LockSemaphoreHolder before 1st Redis attempt</i>
        #<i>Handles MDC propagation to Virtual Threads</i>
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        // Uses 'reentrant' lock-type segment in keys
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        // Uses 'state' lock-type segment in keys
    }
    RedisStateLock --|> AbstractRedisLock

    class RedisStampedLock {
        // Uses 'stamped' lock-type segment in keys
    }
    RedisStampedLock --|> AbstractRedisLock

    class RedisReadWriteLock {
        // Uses 'readwrite' lock-type segment in keys
        -AsyncLock readLockInstance
        -AsyncLock writeLockInstance
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    RedisReadWriteLock ..|> AsyncReadWriteLock

    class RedisReadLock {
        // Part of RedisReadWriteLock
    }
    RedisReadLock --|> AbstractRedisLock

    class RedisWriteLock {
        // Part of RedisReadWriteLock
    }
    RedisWriteLock --|> AbstractRedisLock

    RedisReadWriteLock *-- RedisReadLock : creates
    RedisReadWriteLock *-- RedisWriteLock : creates
```

## 4. Interface and Class Descriptions

### 4.2. Custom Asynchronous Lock Interfaces

* **`com.tui.destilink.framework.locking.redis.lock.AsyncLock`**
  * **Extends:** `java.util.concurrent.locks.Lock`
  * **Purpose:** Defines contract for asynchronous lock operations executed on Virtual Threads. Methods return `CompletableFuture`s.

### 4.3. Abstract Base Class

* **`com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
  * **Key Responsibilities:**
    * Manages common fields, including `lockKey` (which incorporates the lock-type segment), `ownerId`, injected services, and the Virtual Thread `ExecutorService`.
    * Implements core async lock/unlock logic on Virtual Threads, delegating to `RedisLockOperations`.
    * Synchronous `Lock` methods are blocking wrappers.
    * Handles unlock messaging via `LockSemaphoreHolder` (registered *before* first Redis attempt).
    * Manages interactions with `LockWatchdog` (eligibility: `canUseWatchdog()` and `userLeaseTime > safetyBufferMillis`).
    * Constructs Redis keys with mandatory lock-type segments.
    * Handles MDC propagation to Virtual Threads.

### 4.4. Concrete Redis Lock Implementations

All reside in `com.tui.destilink.framework.locking.redis.lock.impl` and extend `AbstractRedisLock`. Their Redis keys include a specific lock-type segment.

* **`RedisReentrantLock`**: Primary reentrant lock. Uses `reentrant` lock-type segment.
* **`RedisStateLock`**: Specialized lock with associated state. Uses `state` lock-type segment.
* **`RedisStampedLock`**: Distributed stamped lock. Uses `stamped` lock-type segment.
* **`RedisReadWriteLock`**: Composed of `RedisReadLock` and `RedisWriteLock`. Uses `readwrite` lock-type segment for its main keys and `Individual Read Lock Timeout Key`s.
* **`RedisReadLock` / `RedisWriteLock`**: Components of `RedisReadWriteLock`.

## 5. "Application-Instance-Bound Lock" Characteristic

This is a behavioral characteristic, not a distinct class. Activated if:
1.  `LockOwnerSupplier.canUseWatchdog()` returns true.
2.  The lock's `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) is greater than `safetyBufferMillis` (calculated from `watchdog.interval * watchdog.factor`).
If met, `AbstractRedisLock` registers the lock with `LockWatchdog`. The watchdog then manages its lease using `PEXPIREAT`, respecting the `originalLeaseTimeMillis` stored in Redis.
```
**FILE 9: `exception_handling.md`**
```markdown
# Redis Locking Module: Final Exception Handling Strategy

## 1. Introduction

Effective error handling is crucial. The `locking-redis-lock` module employs a structured exception hierarchy for clear, contextual information, integrating with Destilink Framework's structured logging via `ExceptionMarkerProvider`. This strategy accounts for the "Async-First" design (with Virtual Threads), `redis-core` interaction, and centralized idempotency.

`RedisLockErrorHandler` translates low-level Redis exceptions (from `ClusterCommandExecutor`) into module-specific exceptions. `RedisLockOperationsImpl` handles retries for certain exception types.

## 2. Base Exception: `AbstractRedisLockException`

All custom exceptions extend `AbstractRedisLockException`.

*   **Purpose**: Common type, carries common context, integrates with `ExceptionMarkerProvider`.
*   **Required Contextual Fields**: `lockName` (full key with lock-type segment), `lockType`, `lockOwnerId`, `requestUuid` (from `RedisLockOperationsImpl`), `message`, `cause`.
*   **`ExceptionMarkerProvider` Implementation**: Populates SLF4J `Marker` with common and specific fields.

## 3. Specialized Exception Classes

Extend `AbstractRedisLockException`. Exceptions often propagate via `CompletableFuture`s from Virtual Threads.

*   **`LockAcquisitionException`**: Failure during lock acquisition (not timeout/interruption).
*   **`LockTimeoutException` / `LockAcquisitionTimeoutException`**:
    *   **Purpose**: Asynchronous or blocking acquisition failed within specified `defaults.acquireTimeout`.
    *   **Specific Context**: `lock.timeoutMillis`, `lock.attempts`.
    *   **Note**: Raised only if `acquireTimeout` expires AND any in-flight Redis op has completed without acquiring the lock and without a Redis-specific error.
*   **`LockReleaseException`**: Failure during lock release.
*   **`LeaseExtensionException` / `LockExtensionException`**: Watchdog or user-initiated extension failed.
*   **`LockInterruptedException`**: Wraps `InterruptedException` during synchronous lock wait.
*   **`LockCommandException`**: Lua script or Redis command execution failures from `ClusterCommandExecutor`.
*   **`LockConnectionException`**: Underlying Redis connection problems from `redis-core`.
*   **`LockNotOwnedException` / `LockIllegalMonitorStateException`**: Operation on a lock not held by caller.
*   **`RetryableLockException`**: Indicates a transient error that `RedisLockOperationsImpl` can retry (e.g., temporary network issue for an individual Redis command).
*   **`NonRetryableLockException`**: Indicates a permanent error that should not be retried by `RedisLockOperationsImpl` (e.g., invalid arguments).

### 3.1 Idempotency-Related Exceptions

*   **`IdempotencyViolationException`**: Idempotency system detects inconsistent state. Context: `lock.requestUuid`, `lock.idempotency.cacheKey`.
*   **`IdempotencyTimeoutException`**: Operations on idempotency response cache timeout. Context: `lock.requestUuid`. (Potentially retryable by `RedisLockOperationsImpl`).
*   **`ResponseCacheException`**: General failures in response cache system. Context: `lock.requestUuid`.

## 4. Error Handling by `RedisLockErrorHandler`

*   Catches low-level Redis exceptions (from `ClusterCommandExecutor`).
*   Translates them into `AbstractRedisLockException` subtypes with full context, including `requestUuid`.
*   Handles `CompletionException` / `ExecutionException` from `CompletableFuture`s.

## 5. Error Handling by `RedisLockOperationsImpl`

*   Catches exceptions from `ClusterCommandExecutor` / Lua script executions.
*   Maps these to `RetryableLockException` or `NonRetryableLockException`.
*   **Internal Retries**: For *individual Redis operations* that result in `RetryableLockException`, implements retry logic using `defaults.maxRetries` and `defaults.retryInterval` (using `Thread.sleep()` on the Virtual Thread).
*   **Immediate Propagation**: `NonRetryableLockException` types are propagated immediately.

## 6. `acquireTimeout` Precedence

*   The `defaults.acquireTimeout` is an *approximate* overall limit for user-facing `tryLock` attempts.
*   **Non-Interruption**: If `acquireTimeout` expires while a Redis command is in flight, that command **MUST NOT be interrupted** by the locking module. It completes or times out based on its own lower-level settings.
*   **Result Precedence**: The actual result of the in-flight Redis operation takes precedence. If it acquires the lock, it's acquired. If it fails with a Redis error (after `RedisLockOperationsImpl` retries), that error is propagated.
*   **`LockAcquisitionTimeoutException` Condition**: Raised only if `acquireTimeout` passes, AND the latest Redis operation has completed *without* acquiring the lock, AND *without* a Redis-specific exception.

## 7. Logging Integration

*   `ExceptionMarkerProvider` ensures structured JSON log output via Destilink Core logging.
*   General logging via SLF4J with parameterized messages.
*   `LockContextDecorator` enriches logs with MDC data (propagated to Virtual Threads).
```
**FILE 10: `configuration.md`**
```markdown
# Redis Locking Module: Configuration

## 1. Introduction

This document details the configuration property hierarchy and key settings for the `locking-redis-lock` module. It aligns with the **Async-First (Virtual Threads)** approach, **centralized idempotency**, **refined watchdog**, and **lock-type specific key schemas**.

Configuration principles:
- **Strict Guideline Adherence**.
- **Clear Override Precedence**: Instance-specific (builders) > Programmatic Bucket (builders) > Global YAML (`RedisLockProperties.Defaults`).
- **Separation of Concerns**: System-level (`RedisLockProperties.WatchdogProperties`) vs. overridable lock defaults (`RedisLockProperties.Defaults`).
- **Mandatory Unlock Notifications** (Redis Pub/Sub).
- **Leveraging `redis-core` Properties**.
- **Redis Key Construction**: Via `redis-core` utilities, with mandatory lock-type segments.
- **Lua-Only Redis Operations**: All lock state/TTL/metadata changes via Lua.
- **Idempotency**: All mutating operations are idempotent (`requestUuid`, `responseCacheTtl`).

## 2. Configuration Hierarchy and Flow

Global defaults are loaded from YAML into `RedisLockProperties`. This class contains two nested static classes: `RedisLockProperties.WatchdogProperties` (for system-level watchdog settings like `interval`, `factor`) and `RedisLockProperties.Defaults` (for overridable lock implementation defaults like `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`).

`LockBucketRegistry` uses `RedisLockProperties.Defaults` and `RedisCoreProperties` to initialize a `LockBucketConfig`. Builders allow programmatic overrides.

```mermaid
graph TD
    subgraph YAML_Configuration [\"YAML Configuration (`1000-locking-redis-lock.application.yml`)\"]
        direction LR
        A_WatchdogYAML[\"Watchdog Settings<br>(destilink.fw.locking.redis.watchdog.*)\"]
        B_DefaultsYAML[\"Lock Implementation Defaults<br>(destilink.fw.locking.redis.defaults.*)\"]
        C_GlobalRedisCoreYAML[\"Global Redis Core Settings<br>(destilink.fw.redis.core.*)\"]
        D_TopLevelYAML[\"Top-Level Locking Settings<br>(destilink.fw.locking.redis.*)\"]
    end

    subgraph Java_Configuration_Classes [\"Java Configuration Classes\"]
        direction LR
        E_RedisLockProps[\"<code>RedisLockProperties.java</code><br>(@ConfigurationProperties)<br>Contains WatchdogProperties, Defaults, & top-level\"]
        F_WatchdogProps[\"<code>RedisLockProperties.WatchdogProperties</code><br>(Holds interval, factor, etc.)\"]
        G_DefaultsProps[\"<code>RedisLockProperties.Defaults</code><br>(Holds leaseTime, retryInterval, maxRetries, acquireTimeout)\"]
        H_RedisCoreProps[\"<code>RedisCoreProperties.java</code><br>(from redis-core)\"]
        I_LockBucketConfig[\"<code>LockBucketConfig.java</code><br>(Resolved bucket settings)\"]
    end

    subgraph Builder_Chain [\"Builder Chain (Programmatic Configuration)\"]
        direction LR
        J_LockBucketRegistry[\"<code>LockBucketRegistry</code>\"]
        K_LockBucketBuilder[\"<code>LockBucketBuilder</code><br>(Bucket defaults override global Defaults)\"]
        L_LockConfigBuilder[\"<code>LockConfigBuilder</code>\"]
        M_AbstractLockTypeConfigBuilder[\"<code>AbstractLockTypeConfigBuilder</code><br>(Instance overrides bucket/global Defaults)\"]
    end

    subgraph Lock_Instance_And_Watchdog [\"Lock Instance & Watchdog Service\"]
        N_LockInstance[\"Actual Lock Instance<br>(Operates with resolved Defaults, uses Virtual Threads)\"]
        O_LockWatchdog[\"<code>LockWatchdog</code> Service<br>(Always active, configured by WatchdogProperties, uses Virtual Threads)\"]
    end

    A_WatchdogYAML -- \"populates\" --> F_WatchdogProps;
    B_DefaultsYAML -- \"populates\" --> G_DefaultsProps;
    C_GlobalRedisCoreYAML -- \"populates\" --> H_RedisCoreProps;
    D_TopLevelYAML -- \"populates top-level fields in\" --> E_RedisLockProps;
    
    E_RedisLockProps -- \"contains\" --> F_WatchdogProps;
    E_RedisLockProps -- \"contains\" --> G_DefaultsProps;

    J_LockBucketRegistry -- \"uses global defaults from\" --> G_DefaultsProps;
    J_LockBucketRegistry -- \"uses\" --> H_RedisCoreProps;
    J_LockBucketRegistry -- \"initializes `LockBucketConfig` for\" --> K_LockBucketBuilder;
    
    K_LockBucketBuilder -- \"Can override bucket-level defaults in its\" --> I_LockBucketConfig;
    K_LockBucketBuilder -- \"Passes config to\" --> L_LockConfigBuilder;
    L_LockConfigBuilder -- \"Passes config to\" --> M_AbstractLockTypeConfigBuilder;
    M_AbstractLockTypeConfigBuilder -- \".build() creates\" --> N_LockInstance;

    M_AbstractLockTypeConfigBuilder -- \"Applies instance overrides over\" --> I_LockBucketConfig;
    N_LockInstance -- \"Receives final effective configuration from\" --> M_AbstractLockTypeConfigBuilder;
    O_LockWatchdog -- \"is configured by\" --> F_WatchdogProps;
```

## 3. Watchdog Activation Logic

The `LockWatchdog` service is **always active** when the `locking-redis-lock` module is enabled. It monitors locks that meet specific conditions:
1.  **Application Instance Binding**: `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returns `true`.
2.  **Sufficient Lease Time**: The `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) for the lock instance must be greater than `safetyBufferMillis` (calculated as `watchdog.interval * watchdog.factor`).

If met, the lock is registered with the watchdog. The watchdog sets the initial lock TTL to `safetyBufferMillis` and then maintains it, respecting the `originalLeaseTimeMillis` for the final expiry.

## 4. Key Configuration Properties

### 4.1. Top-Level Properties (`destilink.fw.locking.redis.*`)

Defined directly in `RedisLockProperties`.

| Property (YAML Key)               | Java Field (`RedisLockProperties`) | Description                                                                                                                             | Default (Java) | Notes                                                    |
| :-------------------------------- | :--------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------- |
| `enabled`                         | `enabled`                          | Globally enables or disables the entire Redis locking feature.                                                                          | `true`         | Master switch.                                           |
| `state-key-expiration`            | `stateKeyExpiration`               | TTL for auxiliary state keys in Redis (e.g., for `StateLock`). Can be overridden at bucket level.                                       | `PT5M`         | Ensures cleanup of auxiliary state keys.                 |
| `response-cache-ttl`              | `responseCacheTtl`                 | TTL for idempotency response cache records in Redis (keyed by `requestUuid`). **Critical for idempotency of all mutating operations.**  | `PT5M`         | Must be long enough for client retries.                  |
| `prefix`                          | `prefix`                           | **DEPRECATED & REMOVED.** Global prefix is derived from `redis-core`.                                                                   | N/A            | Use `destilink.fw.redis.core.keyspace-prefixes.locking`. |
| `unlock-message-listener-enabled` | `unlockMessageListenerEnabled`     | **REMOVED.** Pub/Sub listener is now a mandatory core component and always enabled if the module is active.                             | N/A            | -                                                        |
| `retry-executor-supplier`         | `retryExecutorSupplier`            | `Supplier<ScheduledExecutorService>` for internal delayed tasks (e.g., `retryInterval` waits). Not a YAML property. Internal mechanism. | N/A            | Uses Virtual Threads for its tasks.                      |

### 4.2. System-Level Watchdog Configuration (`destilink.fw.locking.redis.watchdog.*`)

Defined in `RedisLockProperties.WatchdogProperties`. Not overridable per lock.

| Property (YAML Key)          | Java Field (`WatchdogProperties`) | Description                                                                                           | Default (Java)      | Notes                                                                                                                           |
| :--------------------------- | :-------------------------------- | :---------------------------------------------------------------------------------------------------- | :------------------ | :------------------------------------------------------------------------------------------------------------------------------ |
| `interval`                   | `interval`                        | Interval at which the watchdog checks and attempts to extend leases for monitored locks.              | `PT5S`              | Independent duration.                                                                                                           |
| `factor`                     | `factor`                          | Multiplier for `interval` to calculate `safetyBufferMillis`.                                          | `3.0`               | `safetyBufferMillis = interval * factor`. Locks with `userLeaseTime > safetyBufferMillis` are monitored.                        |
| `core-pool-size`             | `corePoolSize`                    | Core number of threads for the watchdog's `ScheduledExecutorService` (uses Virtual Threads per task). | `2`                 | Adjust based on expected number of monitored locks if platform threads were used; with VTs, this is less critical for blocking. |
| `thread-name-prefix`         | `threadNamePrefix`                | Prefix for watchdog executor threads.                                                                 | `dl-lock-watchdog-` | Observability.                                                                                                                  |
| `shutdown-await-termination` | `shutdownAwaitTermination`        | Maximum time to wait for the watchdog executor to terminate during application shutdown.              | `PT30S`             | Graceful shutdown.                                                                                                              |

### 4.3. Lock Implementation Defaults (`destilink.fw.locking.redis.defaults.*`)

Defined in `RedisLockProperties.Defaults`. These are global defaults that can be overridden by lock builders.

| Property (YAML Key) | Java Field (`Defaults`) | Description                                                                                                                                                                 | Default (Java)     | Programmatic Bucket Override (Builder Method)           | Programmatic Instance Override (Builder Method)              | Notes                                                                                                                                                                                                                |
| :------------------ | :---------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- | :------------------------------------------------------ | :----------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `lease-time`        | `leaseTime`             | Default `relativeLeaseTimeMillis` a lock should be held.                                                                                                                    | `PT60S`            | `LockBucketBuilder.withDefaultLeaseTime(Duration)`      | `AbstractLockTypeConfigBuilder.withLeaseTime(Duration)`      | This is the user's intended lease. Watchdog uses this to calculate `userIntendedExpireTimeMillis`.                                                                                                                   |
| `retry-interval`    | `retryInterval`         | Default wait time between retry attempts for *individual Redis operations* within `RedisLockOperationsImpl` if a transient error occurs. Also used as fallback for Pub/Sub. | `PT0.1S` (`100ms`) | `LockBucketBuilder.withDefaultRetryInterval(Duration)`  | `AbstractLockTypeConfigBuilder.withRetryInterval(Duration)`  | `Thread.sleep()` on Virtual Threads.                                                                                                                                                                                 |
| `max-retries`       | `maxRetries`            | Default maximum number of retry attempts for *individual failing Redis operations* within `RedisLockOperationsImpl`.                                                        | `3`                | `LockBucketBuilder.withDefaultMaxRetries(int)`          | `AbstractLockTypeConfigBuilder.withMaxRetries(int)`          | Does NOT limit attempts to acquire a busy lock.                                                                                                                                                                      |
| `acquire-timeout`   | `acquireTimeout`        | Default overall timeout for a lock acquisition operation (e.g., for `tryLock(timeout, unit)`). Approximate limit.                                                           | `PT5S`             | `LockBucketBuilder.withDefaultAcquireTimeout(Duration)` | `AbstractLockTypeConfigBuilder.withAcquireTimeout(Duration)` | Does not interrupt in-flight Redis commands; their result takes precedence. `LockAcquisitionTimeoutException` only if deadline passed AND last Redis op (if any) didn't acquire lock AND didn't throw a Redis error. |

**Removed Properties from `RedisLockProperties.Defaults`**:
*   `fairLockBehavior`: Fairness is complex in distributed systems and not directly configurable this way.
*   `asyncExecutorName`: Replaced by a dedicated Virtual Thread executor.
*   `redisOperationTimeout`: Timeout for individual Redis operations is now implicitly handled by Lettuce/`ClusterCommandExecutor`'s lower-level settings, and `RedisLockOperationsImpl` retries transient failures. The overall control is `acquireTimeout`.

## 5. Java Configuration Classes

- **`RedisLockProperties.java`**: `@ConfigurationProperties(prefix = "destilink.fw.locking.redis")`. Contains top-level settings and nested `WatchdogProperties` and `Defaults` static classes.
- **`LockBucketConfig.java`**: Non-Spring managed, configured by `LockBucketBuilder`, holds resolved settings for a bucket.
```
**FILE 11: `redis_key_schema.md`**
```markdown
# Redis Locking Module: Final Redis Key Schema

## 1. Introduction

This document outlines the consolidated Redis key schema used by the `locking-redis-lock` module. A well-defined and consistent key schema is crucial for managing lock data effectively, avoiding conflicts, ensuring compatibility with Redis Cluster, and enabling semantic isolation between different lock types. This schema incorporates the mandatory **lock-type segment**.

All Redis keys MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `RedisKey`, and `RedisKeyPrefix` from the `redis-core` module.

## 2. Key Structure Principles

*   **Hierarchical Naming**: Colons (`:`) as separators.
*   **Configurable Prefixing via `redis-core`**: Base prefix from `RedisCoreProperties.KeyspacePrefixes` (e.g., `application` or `distributed` prefix) + static segment (`:__lock_buckets__`).
*   **Mandatory Lock-Type Segment**: A segment identifying the lock type (e.g., `reentrant`, `stamped`, `state`, `readwrite`) is now **mandatory** in all primary lock keys and their associated data/metadata keys. This ensures semantic isolation.
*   **Namespacing**: Specific namespaces (e.g., `__locks__`, `__unlock_channels__`, `__resp_cache__`, `__rwttl__`).
*   **Bucket-Based Grouping**: Locks grouped by `bucketName`.
*   **Hash Tags for Cluster Compatibility**: The lock identifier (`<lockName>`) enclosed in `{<lockName>}` acts as a hash tag.
*   **`lockKey` vs. `lockDataKey`**:
    *   `lockKey`: The primary key for the lock itself (e.g., a String holding `ownerId` or a Hash for complex locks). This key has the actual Redis expiry (`PEXPIREAT`).
    *   `lockDataKey`: A secondary Redis Hash key storing persistent metadata like `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`. This key typically has a slightly longer TTL than `lockKey` or is managed alongside it.

## 3. General Key Format

The general format for most keys related to a specific lock instance:
`<prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]`

Unlock channels also include the lock type.

### **Note on `<prefix>`:**

Effective prefix is `RedisCoreProperties.keyspacePrefixes.(application|distributed) + :__lock_buckets__`.
Example: if `application` prefix is `myApp`, then `<prefix>` becomes `myApp:__lock_buckets__`.

## 4. Standardized Key Schema Table

| Key Type                            | Standardized Format                                                                                                                                            | Hash Tag       | Example                                                                                                                                                                    | Notes                                                                                                                                                                                                                                                                                                                   |
| :---------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Main Lock (`lockKey`)               | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`                                                                                                      | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`                                                                                                             | For simple locks, a Redis String holding `ownerId`. For reentrant/complex locks (like `RedisReentrantLock`), a Redis Hash storing `owner` and `count`. TTL set by `PEXPIREAT` to `expiresAtMillis`. **`<lockType>` is mandatory.**                                                                                      |
| Lock Metadata (`lockDataKey`)       | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data`                                                                                                 | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}:data`                                                                                                        | Redis Hash. Stores `ownerId` (String), `originalLeaseTimeMillis` (String/long - user's intended full lease), `expiresAtMillis` (String/long - current absolute expiry). TTL managed alongside `lockKey`. **`<lockType>` is mandatory.**                                                                                 |
| State Key (for `RedisStateLock`)    | `<prefix>:<bucketName>:__locks__:state:{<lockName>}:state`                                                                                                     | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:state:{order123}:state`                                                                                                           | Redis String. Value is the state. Used by `RedisStateLock`. TTL by `stateKeyExpiration`. The `lockType` here is `state`.                                                                                                                                                                                                |
| ReadWriteLock Main Hash (`lockKey`) | `<prefix>:<bucketName>:__locks__:readwrite:{<lockName>}`                                                                                                       | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}`                                                                                                            | Redis Hash. Stores `mode`, `writerId` (if write-locked), reader counts/IDs. TTL managed by `PEXPIREAT` based on watchdog (`safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock leases. `lockType` is `readwrite`. Associated `lockDataKey` stores its `originalLeaseTimeMillis` etc. |
| Stamped Lock Data (`lockKey`)       | `<prefix>:<bucketName>:__locks__:stamped:{<lockName>}`                                                                                                         | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}`                                                                                                                    | Redis Hash. Stores `version`, `write_owner_id`, `read_holders`, etc. TTL by `PEXPIREAT`. `lockType` is `stamped`. Associated `lockDataKey` stores its `originalLeaseTimeMillis` etc.                                                                                                                                    |
| Unlock Channel                      | Publish: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}` <br/> Subscribe Pattern: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*` | N/A            | Publish: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}` <br/> Subscribe Pattern: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*` | Locks publish to specific channels including `lockType`. `UnlockMessageListener` (per bucket) subscribes to a pattern. Message payload is *only* `UnlockType`.                                                                                                                                                          |
| Individual Read Lock Timeout Key    | `<prefix>:<bucketName>:__rwttl__:readwrite:{<lockName>}:<readerId>:<reentrantCount>`                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{configA}:readerId_uuid:1`                                                                                            | Redis String. Stores placeholder. TTL set to `leaseTime` of specific read acquisition. Used by `RedisReadWriteLock`'s Lua scripts. `lockType` (`readwrite`) is part of the key.                                                                                                                                         |
| Response Cache                      | `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`                                                                                   | `{<lockName>}` | `myApp:__lock_buckets__:orders:__resp_cache__:reentrant:{order123}:a1b2-c3d4`                                                                                              | Redis String. Caches Lua script responses for idempotency. TTL by `responseCacheTtl`. Key includes `lockType`.                                                                                                                                                                                                          |

## 5. Key Design Considerations

*   **Semantic Isolation**: The mandatory `<lockType>` segment prevents accidental key collisions or operational interference between different lock implementations (e.g., a `reentrant` lock named "X" vs. a `stamped` lock named "X").
*   **Clarity and Readability**.
*   **Redis Cluster Compatibility**: Hash tags `{<lockName>}` ensure co-location of `lockKey`, `lockDataKey`, and `responseCacheKey` for the same lock instance.
*   **Implementation**: `LockBucket.java` (or similar) constructs keys, now requiring `lockType`. Lua scripts are updated.
```
**FILE 12: `lua_scripts_2.md`**
```markdown
# Redis Locking Module: Final Lua Scripts Documentation

## 1. Introduction

Lua scripts are executed on the Redis server for atomicity of complex lock operations. All scripts are loaded by `ScriptLoader` and executed by `RedisLockOperations` via `ClusterCommandExecutor`. All mutating scripts **MUST** implement the Idempotency Wrapper pattern. All scripts that interact with lock expiry **MUST** use `PEXPIREAT` and manage `expiresAtMillis` and `originalLeaseTimeMillis` in the `lockDataKey`.

**Redis Cluster Compatibility & Key Format**:
Keys use hash tags (`{<lockName>}`) and **MUST** include a lock-type segment:
`<prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}`.
Related keys like `lockDataKey` (`<...>:data`) and `responseCacheKey` also include the `lockType`.

**Standardized Lua Script Return Values**:
All lock acquisition/status scripts return a structured Redis array:
1.  `status_code`:
    *   `>= 0`: Lock held by other (value is PTTL or absolute expiry timestamp; 0 for "forever").
    *   `< 0`: Lock acquired/operation successful (absolute value is PTTL or absolute expiry timestamp).
2.  `expiresAtMillis`: Absolute Unix timestamp (ms) of current lock expiry, from Redis `TIME`.
3.  `originalLeaseTimeMillis`: The user's intended relative lease duration, read from `lockDataKey`.

**Idempotency Wrapper (Mandatory for Mutating Scripts)**:
```lua
-- KEYS[1]: responseCacheKey (e.g., prefix:bucket:__resp_cache__:<lockType>:{lockName}:requestUuid)
-- ARGV[1]: requestUuid
-- ARGV[2]: responseCacheTtl (in ms)
-- ... other KEYS and ARGV for the specific script

local cached_response = redis.call('GET', KEYS[1]);
if cached_response then
    return cjson.decode(cached_response); -- Assuming JSON encoded array
end

-- ... core script logic ...
local result = {status_code, expiresAtMillis, originalLeaseTimeMillis}; -- or other relevant structure

if core_operation_was_successful then -- only cache successes for some ops
    redis.call('PSETEX', KEYS[1], ARGV[2], cjson.encode(result));
end
return result;
```
(Note: `cjson` for encoding/decoding arrays in cache. Simpler values might not need it.)

## 2. Core Lock Operations Scripts

### 2.1. `acquire_lock.lua` (Generic for Reentrant/Simple Exclusive Locks)

*   **Purpose**: Atomically attempts to acquire/re-acquire a lock. Manages `lockKey` and `lockDataKey`.
*   **KEYS**:
    1.  `lockKey`: Main lock key (e.g., `prefix:bucket:__locks__:<lockType>:{myLock}`).
    2.  `lockDataKey`: Metadata key (e.g., `prefix:bucket:__locks__:<lockType>:{myLock}:data`).
    3.  `responseCacheKey`: For idempotency.
*   **ARGV**:
    1.  `requestUuid`: For idempotency.
    2.  `responseCacheTtlMs`: TTL for response cache.
    3.  `relativeLeaseTimeMs`: User's desired lease duration.
    4.  `lockOwnerId`: Client attempting to acquire.
    5.  `currentTimeMsClient`: Client's current time (for reference, server time is preferred).
*   **Logic**:
    1.  Idempotency check using `KEYS[3]`, `ARGV[1]`, `ARGV[2]`.
    2.  Get Redis current time: `local redis_time_parts = redis.call('TIME')`, `local currentTimeRedisMs = (redis_time_parts[1] * 1000) + math.floor(redis_time_parts[2] / 1000)`.
    3.  Calculate `newExpiresAtMillis = currentTimeRedisMs + tonumber(ARGV[3])`.
    4.  Check `lockKey`:
        *   If not exist:
            *   `HSET lockDataKey ownerId ARGV[4] originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
            *   `SET lockKey ARGV[4]`.
            *   `PEXPIREAT lockKey newExpiresAtMillis`.
            *   `PEXPIREAT lockDataKey newExpiresAtMillis + some_buffer_for_data_key (e.g., 60000ms)`.
            *   Result: `{-1, newExpiresAtMillis, tonumber(ARGV[3])}` (acquired).
        *   If exists (is a HASH for reentrant, or STRING for simple):
            *   Get `currentOwnerId` from `lockKey` (or `lockDataKey.ownerId`).
            *   If `currentOwnerId == ARGV[4]` (re-entrant):
                *   Increment re-entry count (if reentrant type, stored in `lockKey` Hash).
                *   `HSET lockDataKey originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
                *   `PEXPIREAT lockKey newExpiresAtMillis`.
                *   `PEXPIREAT lockDataKey newExpiresAtMillis + buffer`.
                *   Result: `{-2, newExpiresAtMillis, tonumber(ARGV[3])}` (re-acquired).
            *   Else (held by other):
                *   Get `currentExpiresAt` from `lockDataKey.expiresAtMillis`.
                *   Result: `{currentTimeRedisMs < currentExpiresAt and (currentExpiresAt - currentTimeRedisMs) or 0, currentExpiresAt, tonumber(redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis'))}`.
    5.  Cache and return `result`.
*   **Returns**: Array `{status_code, expiresAtMillis, originalLeaseTimeMillis}`.

### 2.2. `unlock_lock.lua`

*   **Purpose**: Atomically releases a lock. Publishes unlock message.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `unlockChannelBaseName`: Base for Pub/Sub (e.g., `prefix:bucket:__unlock_channels__:<lockType>`). Script appends `:{lockName}`.
    4.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `lockOwnerId`.
    4.  `unlockTypeMessage`: String payload for Pub/Sub (e.g., "REENTRANT_FULLY_RELEASED").
*   **Logic**:
    1.  Idempotency check.
    2.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    3.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[3]`, result: `{0, 0, 0}` (not released or not owner).
    4.  Else (owner matches):
        *   (If reentrant: decrement count in `lockKey` Hash. If count > 0, result: `{0, current_expires_at, current_original_lease}` (still held), cache & return).
        *   `DEL lockKey lockDataKey`.
        *   Extract `{lockName}` from `KEYS[1]`. Publish `ARGV[4]` to `KEYS[3] .. ':' .. lockNamePart`.
        *   Result: `{1, 0, 0}` (released).
    5.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for released, 0 otherwise), 0, 0}`.

### 2.3. `extend_lock.lua` (User-initiated lease extension)

*   **Purpose**: Atomically extends lease if caller is owner. Updates `originalLeaseTimeMillis`.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `newRelativeLeaseTimeMs`: User's new desired lease from now.
    4.  `lockOwnerId`.
*   **Logic**:
    1.  Idempotency check.
    2.  Get Redis time `currentTimeRedisMs`.
    3.  Calculate `newExpiresAtMillis = currentTimeRedisMs + tonumber(ARGV[3])`.
    4.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    5.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[4]`, result: `{0, current_expires_at_or_0, current_original_lease_or_0}` (not owner/not exist).
    6.  Else (owner matches):
        *   `HSET lockDataKey originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
        *   `PEXPIREAT lockKey newExpiresAtMillis`.
        *   `PEXPIREAT lockDataKey newExpiresAtMillis + buffer`.
        *   Result: `{1, newExpiresAtMillis, tonumber(ARGV[3])}` (extended).
    7.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for extended), expiresAtMillis, newOriginalLeaseTimeMillis}`.

### 2.4. `watchdog_refresh_lock.lua`

*   **Purpose**: Atomically extends lease if caller is owner, for watchdog. **Does NOT change `originalLeaseTimeMillis`**.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `targetAbsoluteExpiresAtMillis`: The new absolute expiry time calculated by watchdog.
    4.  `lockOwnerId`.
*   **Logic**:
    1.  Idempotency check.
    2.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    3.  Get `originalLease = redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis')`.
    4.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[4]`, result: `{0, current_expires_at_or_0, originalLease_or_0}`.
    5.  Else (owner matches):
        *   `HSET lockDataKey expiresAtMillis ARGV[3]`. (Note: `originalLeaseTimeMillis` is NOT changed).
        *   `PEXPIREAT lockKey tonumber(ARGV[3])`.
        *   `PEXPIREAT lockDataKey tonumber(ARGV[3]) + buffer`.
        *   Result: `{1, tonumber(ARGV[3]), tonumber(originalLease)}` (refreshed).
    6.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for refreshed), newExpiresAtMillis, originalLeaseTimeMillis}`.

### 2.5. `check_lock_status.lua`

*   **Purpose**: Atomically gets lock status, owner, `expiresAtMillis`, `originalLeaseTimeMillis`. Non-mutating.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
*   **ARGV**: (None beyond potential `requestUuid` if it were to be cached, but reads usually aren't)
*   **Logic**:
    1.  If `lockKey` not exist, result: `{0, 0, 0}` (not held).
    2.  Else:
        *   `owner = redis.call('HGET', KEYS[2], 'ownerId')`.
        *   `expiresAt = tonumber(redis.call('HGET', KEYS[2], 'expiresAtMillis'))`.
        *   `originalLease = tonumber(redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis'))`.
        *   `pttl = redis.call('PTTL', KEYS[1])`.
        *   Result: `{pttl, expiresAt, originalLease}`. (Also include owner in a more complex return if needed by client).
*   **Returns**: Array `{current_pttl_or_0, expiresAtMillis, originalLeaseTimeMillis}`.

## 3. StateLock Specific Scripts

(Similar structure to above, but also interact with a separate state key, ensuring atomicity between lock state and application state. All will use lock-type specific keys and include idempotency wrappers.)

*   `try_state_lock.lua`: Acquires lock if state matches.
*   `update_state_if_locked.lua`: Updates state only if caller holds the lock.
*   `unlock_state_lock.lua`: Releases lock, optionally updates state.

## 4. ReadWriteLock Scripts

These scripts manage shared read and exclusive write access using `lockKey` (type `readwrite`) and `lockDataKey`. They also manage `Individual Read Lock Timeout Key`s.

### 4.1. `try_read_lock.lua` (Conceptual, adapted from `detailed-plan.md` principles)

*   **Purpose**: Acquire read lock. Manages main R/W lock state and individual read lock TTLs.
*   **KEYS**:
    1.  `mainLockKey` (type `readwrite`).
    2.  `mainLockDataKey` (type `readwrite`).
    3.  `individualReadLockTimeoutKeyPrefix`: Base for this reader's timeout keys (e.g., `prefix:bucket:__rwttl__:readwrite:{lockName}:<readerId>`). Script appends `:<reentrantCount>`.
    4.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `relativeLeaseTimeMs` (for this specific read acquisition).
    4.  `readerId`.
    5.  `writerId` (current thread's potential write lock ID for reentrant read-while-write).
*   **Logic**:
    1.  Idempotency. Get Redis time.
    2.  Check `mainLockKey`'s mode from its Hash.
    3.  If no lock or mode is 'read', or mode is 'write' but held by `ARGV[5]`:
        *   Increment reader count for `ARGV[4]` in `mainLockKey` Hash.
        *   Create/update `individualReadLockTimeoutKey` (`KEYS[3] .. ':' .. new_reentrant_count`) with `PEXPIREAT (currentTime + ARGV[3])`.
        *   Update `mainLockDataKey`'s `expiresAtMillis` to `max(current_main_expiry, individual_read_expiry)`. Also update `PEXPIREAT` on `mainLockKey`.
        *   Store/update `originalLeaseTimeMillis` in `mainLockDataKey` if this is the first acquirer or policy dictates.
        *   Return success status, new main lock `expiresAtMillis`, and `originalLeaseTimeMillis`.
    4.  Else (conflicting write lock): Return current main lock PTTL/expiry.
*   **Returns**: Standard structured array.

### 4.2. `unlock_read_lock.lua`

*   **Purpose**: Release a read lock instance. Recalculate main R/W lock TTL.
*   **KEYS**:
    1.  `mainLockKey`.
    2.  `mainLockDataKey`.
    3.  `individualReadLockTimeoutKeyPrefixForThisReader`.
    4.  `generalIndividualReadLockTimeoutKeyScanPattern` (e.g., `prefix:bucket:__rwttl__:readwrite:{lockName}:*`).
    5.  `unlockChannelBaseName`.
    6.  `responseCacheKey`.
*   **ARGV**: `requestUuid`, `responseCacheTtlMs`, `readerId`, `unlockTypeMessage`.
*   **Logic**:
    1.  Idempotency.
    2.  Decrement reader count for `ARGV[3]` in `mainLockKey` Hash. Delete corresponding `individualReadLockTimeoutKey`.
    3.  If all readers and writer are gone (check `mainLockKey` Hash): `DEL mainLockKey, mainLockDataKey`. Publish. Return success.
    4.  Else: Iterate all remaining `individualReadLockTimeoutKey`s (using `SCAN` with `KEYS[4]`) to find max remaining TTL.
    5.  Update `mainLockDataKey.expiresAtMillis` and `PEXPIREAT mainLockKey` to this max TTL (or its own watchdog-managed TTL if greater).
    6.  Publish. Return success.
*   **Returns**: Standard structured array.

### 4.3. `try_write_lock.lua` & 4.4. `unlock_write_lock.lua`

*   Similar principles: manage `mainLockKey` (mode='write', writerId, reentrancy) and `mainLockDataKey`.
*   `unlock_write_lock` checks for active readers. If readers exist, may transition `mainLockKey` mode to 'read' and set its TTL based on max remaining reader TTLs. Otherwise, deletes the lock. Publishes appropriate `UnlockType`.

## 5. StampedLock Scripts

(Conceptual, follow principles of `lockKey` with `stamped` type, `lockDataKey`, idempotency, structured returns, `PEXPIREAT`.)

*   `try_stamped_lock.lua` (modes: "read", "write", "optimistic")
*   `unlock_stamped_lock.lua`
*   `validate_stamp.lua`
*   `convert_to_write_lock.lua`
*   `convert_to_read_lock.lua`
```
**FILE 13: `migration_notes.md`**
```markdown
# Redis Locking Module: Notes for Users (v1.0 - Post Detailed Plan Refinements)

## 1. Introduction

This document highlights key behavioral aspects and design choices of the `locking-redis-lock` module following the **Comprehensive Plan (Version 1.0, June 17, 2025)**. These notes clarify its functionality, especially regarding changes to configuration, watchdog behavior, idempotency, key schema, and Virtual Thread adoption.

For detailed information, refer to the respective core documentation, which has been updated for consistency with this plan.

## 2. Key Behavioral and Design Points to Note

*   **Asynchronous-First with Virtual Threads**: All core lock operations are asynchronous and execute on Virtual Threads, minimizing platform thread blocking. Synchronous `Lock` methods are blocking wrappers. MDC is propagated.
*   **Efficient Lock Acquisition (Non-Polling by Default)**: Primary wait mechanism is Redis Pub/Sub. `LockSemaphoreHolder` (managing `CompletableFuture`s) is registered with `UnlockMessageListenerManager` *before* the first Redis acquisition attempt to prevent race conditions.
*   **`lock()` Method Blocks Indefinitely**: Standard `lock()` blocks until acquired or interrupted. For time-bounded attempts, use `tryLock(long timeout, TimeUnit unit)`. `defaults.acquireTimeout` governs this, with nuances for in-flight Redis operations (they are not interrupted; their result takes precedence).
*   **Centralized Idempotency**: All mutating Redis operations are idempotent. `RedisLockOperationsImpl` generates a `requestUuid` per logical operation (used for all its internal retries). Lua scripts use this `requestUuid` and `responseCacheTtl` to check/store results in a response cache. This is mandatory for all mutating scripts.
*   **Lua-Only Redis Operations**: All lock state, TTL (`PEXPIREAT`), and metadata (`lockDataKey`) changes are exclusively via Lua scripts.
*   **Refined Watchdog Mechanism**:
    *   The `LockWatchdog` service is **always active** if the module is enabled.
    *   It conditionally monitors locks if they are application-instance-bound (`LockOwnerSupplier.canUseWatchdog()`) AND their `userProvidedLeaseTime > safetyBufferMillis` (where `safetyBufferMillis = watchdog.interval * watchdog.factor`).
    *   Initial TTL for monitored locks is `safetyBufferMillis`. For non-monitored, it's the full `userProvidedLeaseTime`.
    *   Watchdog uses `PEXPIREAT` and aims to maintain `safetyBufferMillis` TTL, or expires the lock at `userIntendedExpireTimeMillis` during the "final leg."
    *   Crucially, the watchdog **never** modifies the `originalLeaseTimeMillis` (user's intended full lease) stored in the `lockDataKey` in Redis. It only reads it to determine renewal strategy.
*   **Lock-Type Specific Redis Keys**: All `lockKey`s and `lockDataKey`s now include a mandatory lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`.
*   **`lockDataKey`**: A secondary Redis Hash key (e.g., `...:<lockType>:{<lockName>}:data`) stores `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`.
*   **Standardized Lua Script Returns**: Scripts return a structured array: `{status_code, expiresAtMillis, originalLeaseTimeMillis}`.
*   **Programmatic Bucket Configuration**: Bucket configuration is programmatic via builders. Global defaults from `RedisLockProperties.Defaults`.
*   **Configuration Property Changes**:
    *   **Removed**: `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `maxLockNameLength`, `maxBucketNameLength`, `maxScopeLength`. From `RedisLockProperties.Defaults`: `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout`. From `RedisLockProperties.WatchdogProperties`: `operationMaxRetries`, `operationTimeout`. `unlockMessageListenerEnabled` is also removed (listener is mandatory).
    *   **Added/Refined**: `RedisLockProperties.responseCacheTtl`. `WatchdogProperties` now has `interval` and `factor`. `Defaults` has `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`.
*   **`maxRetries` Scope**: `defaults.maxRetries` applies to retrying *individual failing Redis operations* within `RedisLockOperationsImpl`, not to the overall loop for acquiring a busy lock.

## 3. Notes for Users Familiar with Earlier Drafts

*   **No `pubSubWaitTimeout`**: Use `defaults.acquireTimeout` for timed `tryLock` attempts.
*   **Watchdog Always Active (Service)**: Monitoring per lock is conditional. No global `watchdog.enabled` toggle; use module's main `enabled` flag.
*   **Idempotency is Core**: `requestUuid` and `responseCacheTtl` are central.
*   **Keys Include Lock Type**: Be aware of the new key structure if inspecting Redis directly.

## 4. Compatibility & Future Migrations

*   **Source Compatibility**: Core `java.util.concurrent.Lock` methods are maintained.
*   **Behavioral Differences**: Non-polling, Virtual Thread execution, explicit `acquireTimeout` requirements, new watchdog logic, and idempotency are key.
*   **Redis Data**: New locks will use the new key format with lock-type segments and `lockDataKey`. Existing locks (if any from pre-release versions) will not be automatically migrated but will also not conflict due to the new key structure.
```
**FILE 14: `performance_considerations.md`**
```markdown
# Redis Locking Module: Final Performance Considerations

## 1. Introduction

This document discusses performance characteristics and tuning for the `locking-redis-lock` module, now leveraging **Virtual Threads**, a refined **watchdog mechanism**, and **centralized idempotency**. The architecture prioritizes minimizing platform thread blocking and direct Redis polling.

## 2. Key Performance Aspects

*   **Reduced Platform Thread Blocking (Virtual Threads)**: All lock operations (acquisition, release, waiting, watchdog tasks, listener tasks) are executed on Virtual Threads. This significantly improves scalability by preventing platform threads from blocking on I/O or delays, allowing the application to handle many more concurrent lock requests.
*   **Reduced Redis Load (Non-Polling & Efficient Watchdog)**:
    *   Primary wait is Pub/Sub. Fallback uses `min(TTL, defaults.retryInterval)` with `Thread.sleep()` on Virtual Threads.
    *   Watchdog uses `PEXPIREAT` and intelligent refresh logic (maintaining `safetyBufferMillis` or targeting `userIntendedExpireTimeMillis`), potentially reducing unnecessary writes compared to fixed TTL renewals if `safetyBufferMillis` is chosen well.
*   **Atomic Operations with Lua Scripts**: Reduces network round trips. Lua scripts now also handle `lockDataKey` updates and idempotency checks atomically.
*   **Optimized Reentrancy Management**: Stored in Redis Hashes.
*   **Centralized Idempotency**: Adds a slight overhead (one `GET` and potentially one `PSETEX` per mutating operation) for the response cache check, but provides crucial correctness for retries. `responseCacheTtl` affects cache size.

## 3. Performance Tuning and Considerations

*   **Redis Network Latency & Server Throughput**: Still paramount.
*   **Lua Script Complexity**: Scripts are slightly more complex with `lockDataKey` and idempotency logic, but remain focused.
*   **Pub/Sub Overhead**: Generally efficient. Channel names now include `lockType`.
*   **Watchdog Configuration (`watchdog.interval`, `watchdog.factor`)**:
    *   `interval`: How often the watchdog runs. Shorter means more responsive but more Redis checks.
    *   `factor`: Influences `safetyBufferMillis`. A larger factor means a larger `safetyBufferMillis`, potentially fewer renewals for very long locks but longer effective minimum TTL if watchdog kicks in.
    *   These settings, combined with `userProvidedLeaseTime`, determine watchdog monitoring eligibility and renewal frequency.
*   **`defaults.retryInterval`**: Fallback polling interval for Pub/Sub misses and retry delay for individual Redis operations in `RedisLockOperationsImpl`. `Thread.sleep()` on Virtual Threads is efficient.
*   **`defaults.maxRetries`**: For *individual Redis operations* within `RedisLockOperationsImpl`. Higher values increase resilience to transient Redis blips but can delay overall failure detection.
*   **`defaults.acquireTimeout`**: Overall limit for `tryLock`. Must be set reasonably to avoid premature timeouts while also preventing indefinite waits in applications. Consider typical Redis operation times and `retryInterval * maxRetries` for individual ops.
*   **`responseCacheTtl`**: Duration for idempotency records. Longer TTLs increase Redis memory for the cache but provide longer windows for retry idempotency.
*   **Virtual Thread Executor**: The default `Executors.newVirtualThreadPerTaskExecutor()` is generally suitable. No complex tuning usually needed.
*   **Lock Contention**: High contention on a single `lockKey` remains a bottleneck.
*   **Redis Cluster and Hash Tags**: Correct use of `{<lockName>}` in keys (which now include `lockType`) is vital.

## 4. Monitoring

Utilize Micrometer metrics:
*   Lock acquisition times (consider impact of Virtual Thread scheduling).
*   Lock contention / wait times (time spent parked by Virtual Threads).
*   Watchdog activity (locks monitored, renewals, `safetyBufferMillis` effectiveness).
*   Idempotency cache hits/misses.
*   Error rates, especially `RetryableLockException` vs. `NonRetryableLockException`.
```
**FILE 15: `modernization.md`**
```markdown
# Redis Locking Module: Final Modernization Plan & Assessment

## 1. Introduction

This document outlines key modernization tasks and assessment for the `locking-redis-lock` module, aligning with current Destilink Framework best practices, the **Async-First (Virtual Threads)** paradigm, **centralized idempotency**, **refined watchdog**, **lock-type specific keys**, and improved robustness.

## 2. Modernization Assessment Summary (Post-Detailed Plan)

The `detailed-plan.md` (Version 1.0, June 17, 2025) has driven a significant modernization:

*   **Efficient Lock Acquisition**: Notification-based (Pub/Sub) with Virtual Threads. `LockSemaphoreHolder` registered *before* first Redis attempt. Fallback uses `min(TTL, defaults.retryInterval)`.
*   **Atomicity**: Lua scripts for all state/TTL/metadata changes, now including `lockDataKey` management and mandatory idempotency wrappers.
*   **Distributed Reentrancy**: Correctly managed in Redis Hashes.
*   **Comprehensive Exception Handling**: Hierarchy with `ExceptionMarkerProvider`, `RedisLockErrorHandler`, specific exceptions for idempotency, retryable vs. non-retryable distinctions. `RedisLockOperationsImpl` handles retries for individual Redis ops.
*   **Clear Configuration**: `RedisLockProperties` with nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`). Obsolete properties removed.
*   **Detailed Metrics**: Via Micrometer.
*   **`acquireTimeout` Semantics**: Governs overall `tryLock`, non-interrupting of in-flight Redis ops.
*   **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` (same for its internal retries of a logical op). Lua scripts use `requestUuid` and `responseCacheTtl` for response caching.
*   **Refined Watchdog**: Always-active service, conditional monitoring (`userLeaseTime > safetyBufferMillis` & instance-bound). Uses `PEXPIREAT`, preserves `originalLeaseTimeMillis` from `lockDataKey`.
*   **Virtual Threads**: All lock operations, watchdog tasks, and listener tasks execute on Virtual Threads. MDC propagated.
*   **Lock-Type Specific Keys**: Mandatory `lockType` segment in keys for semantic isolation.
*   **Lua-Only Redis Operations**: Strict adherence.

## 3. Key Modernization Tasks (Reflecting `detailed-plan.md` Implementation)

### 3.1. Adopt Virtual Threads and Refine Async Operations
*   **Action**: Integrated Virtual Thread executor. All lock operations, watchdog, and listeners use VTs. MDC propagated. `Thread.sleep()` used for internal delays. `LockSemaphoreHolder` registration order corrected. `acquireTimeout` nuances implemented.

### 3.2. Implement Centralized Idempotency
*   **Action**: `RedisLockOperationsImpl` now generates `requestUuid` per logical operation and passes it with `responseCacheTtl` to Lua scripts. All mutating Lua scripts implement the idempotency wrapper (check cache, execute, store result).

### 3.3. Overhaul Watchdog Mechanism
*   **Action**: `LockWatchdog` is always active. Monitors locks if `userLeaseTime > safetyBufferMillis` (from `watchdog.interval * watchdog.factor`) and instance-bound. Uses `PEXPIREAT`. Manages `expiresAtMillis` while preserving `originalLeaseTimeMillis` (stored in `lockDataKey`). Lua scripts (`watchdog_refresh_lock.lua`, `extend_lock.lua`) updated.

### 3.4. Enforce Lock-Type Specific Keys & `lockDataKey`
*   **Action**: All key construction in Java and Lua now includes a mandatory `lockType` segment. A `lockDataKey` (Redis Hash) is used to store `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`.

### 3.5. Standardize Lua Scripts (Returns, Idempotency, `PEXPIREAT`)
*   **Action**: Lua scripts return structured arrays (`{status, expiresAtMillis, originalLeaseTimeMillis}`). All use `PEXPIREAT`. Mutating scripts are idempotent. All lock state/TTL/metadata changes are Lua-only.

### 3.6. Refine Configuration (`RedisLockProperties`)
*   **Action**: `RedisLockProperties` restructured with nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`). Obsolete properties (`pubSubWaitTimeout`, validation/length limits, `fairLockBehavior`, etc.) removed. `responseCacheTtl` added.

### 3.7. Update Exception Handling & `RedisLockOperationsImpl` Retries
*   **Action**: Exception hierarchy refined. `RedisLockOperationsImpl` now implements internal retries for *individual Redis operations* based on `defaults.maxRetries` and `defaults.retryInterval` for `RetryableLockException` types.

### 3.8. Align All Documentation and Tests
*   **Action**: All 17 markdown files and Javadoc updated to be 100% semantically consistent with `detailed-plan.md`. Test suite updated for new logic, removed features, and Virtual Thread considerations.

## 4. Verification
*   Validated against Destilink Framework guidelines.
*   Thorough testing (unit, integration, concurrency with VTs) performed.
```
**FILE 16: `testing_strategy.md`**
```markdown
# Redis Locking Module: Final Testing Strategy

## 1. Introduction

This document outlines the comprehensive testing strategy for the `locking-redis-lock` module, updated to reflect changes from the **Comprehensive Plan (Version 1.0, June 17, 2025)**, including **Virtual Thread adoption**, **centralized idempotency**, **refined watchdog**, and **lock-type specific keys**.

## 2. Testing Principles

*   **Layered Testing**: Unit, integration, performance.
*   **Correctness**: Core logic, reentrancy, state, R/W locks, especially with new `lockDataKey` and `originalLeaseTimeMillis` / `expiresAtMillis` handling.
*   **Atomicity**: Lua script atomicity, including idempotency wrapper.
*   **Distributed Simulation**: Concurrent clients (simulated via Virtual Threads).
*   **Error Handling**: Correct exceptions, `RedisLockOperationsImpl` retry logic for individual Redis ops.
*   **Configuration Validation**: New `RedisLockProperties` structure, watchdog `interval`/`factor`.
*   **Performance**: Validate benefits of Virtual Threads and non-polling.

## 3. Testing Layers

### 3.1. Unit Tests

*   **Focus**: Isolated components. Mock `RedisLockOperations` for `AbstractRedisLock` tests not hitting Redis.
*   **Examples**: `LockSemaphoreHolder` (CompletableFuture management), `RedisLockProperties` parsing, `RedisLockErrorHandler` mapping, parts of `AbstractRedisLock` (parameter prep, VT dispatch logic, MDC propagation).

### 3.2. Integration Tests (with Testcontainers/Embedded Redis)

*   **Focus**: Interactions with Redis, end-to-end lock behavior, concurrency.
*   **Redis Interaction**:
    *   Lua scripts load and execute correctly.
    *   `lockKey` (with `lockType` segment) and `lockDataKey` (Hash with `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`) are managed as expected.
    *   Idempotency: `responseCacheKey` (with `lockType`) created/used, `responseCacheTtl` respected. Operations are not re-executed on retry with same `requestUuid`.
*   **End-to-End Lock Behavior**:
    *   All lock types, acquisition methods (`lockAsync`, `tryLockAsync` on VTs).
    *   `RedisReadWriteLock`: Correct management of main R/W Hash, `Individual Read Lock Timeout Key`s, and overall TTL based on `safetyBufferMillis` / `userIntendedExpireTimeMillis` and active read leases.
*   **Concurrency (Simulated with many Virtual Threads)**:
    *   Multiple VTs concurrently acquiring/releasing same/different locks.
*   **Watchdog Mechanism**:
    *   Correct registration based on `userLeaseTime > safetyBufferMillis` and `canUseWatchdog()`.
    *   Correct initial TTL setting (`safetyBufferMillis` if monitored, else full lease).
    *   Watchdog extends leases using `PEXPIREAT` via `watchdog_refresh_lock.lua`, targeting `safetyBufferMillis` or `userIntendedExpireTimeMillis` (final leg).
    *   `originalLeaseTimeMillis` in `lockDataKey` is NOT changed by watchdog.
    *   User-initiated `extendLease` updates `originalLeaseTimeMillis` and watchdog registration.
*   **Pub/Sub Messaging & Non-Polling Wait**:
    *   Unlock notifications published to channels with `lockType` segment.
    *   `UnlockMessageListener` derives `lockName`/`lockType`, signals `LockSemaphoreHolder` (completes Future).
    *   `LockSemaphoreHolder` registered *before* first Redis attempt.
    *   Fallback re-attempt using `min(TTL, defaults.retryInterval)`.
*   **Error Handling & `RedisLockOperationsImpl` Retries**:
    *   Simulate Redis errors. Verify `RedisLockErrorHandler` translation.
    *   Test `RedisLockOperationsImpl` internal retry logic for *individual Redis ops* (`defaults.maxRetries`, `defaults.retryInterval`) for `RetryableLockException`.
    *   Test `acquireTimeout` nuances (non-interruption of in-flight Redis, result precedence).
*   **Configuration Scenarios**: Test with various `watchdog.interval`, `watchdog.factor`, `defaults.leaseTime`, `defaults.acquireTimeout`.

### 3.3. Performance Tests

*   **Focus**: Throughput, latency, resource consumption (client CPU minimal due to VTs, Redis CPU/memory).
*   **Scenarios**: Varying contention, lock types, with/without watchdog.
*   **Metrics**: Use `LockMonitor` (Micrometer).

## 4. Specific Testing Considerations

*   **Idempotency**: Test scenarios with simulated network failures causing client retries (with same `requestUuid`) to ensure operations are not duplicated. Verify response cache usage.
*   **Watchdog Logic**:
    *   Test eligibility (`userLeaseTime` vs `safetyBufferMillis`).
    *   Verify initial TTL setting.
    *   Verify `PEXPIREAT` usage and correct `targetAbsoluteExpiresAtMillis` calculation in standard and "final leg" scenarios.
    *   Confirm `originalLeaseTimeMillis` in `lockDataKey` is untouched by watchdog refreshes.
    *   Test user `extendLease` interaction with watchdog.
*   **Virtual Threads & MDC**: Ensure MDC context is correctly propagated and available in logs from VTs.
*   **`acquireTimeout` vs. In-flight Redis Ops**: Create scenarios where `acquireTimeout` might expire *during* a Redis call to verify precedence rules.
*   **Lock-Type Key Isolation**: Negative tests attempting to operate on a lock using the wrong type's operations or keys.
*   **`lockDataKey` Management**: Ensure `lockDataKey` is created, updated (with `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`), and deleted along with `lockKey`.

## 5. Test Coverage
*   High code coverage. Focus on observable behavior.
*   Cover all public API methods, edge cases, error conditions.
```
**FILE 17: `redis_key_schema.md`**
```markdown
# Redis Locking Module: Final Redis Key Schema

## 1. Introduction

This document outlines the consolidated Redis key schema used by the `locking-redis-lock` module. A well-defined and consistent key schema is crucial for managing lock data effectively, avoiding conflicts, ensuring compatibility with Redis Cluster, and enabling **semantic isolation between different lock types via a mandatory lock-type segment**. This schema also introduces the concept of a `lockDataKey` for storing persistent lock metadata.

All Redis keys MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `RedisKey`, and `RedisKeyPrefix` from the `redis-core` module.

## 2. Key Structure Principles

*   **Hierarchical Naming**: Colons (`:`) as separators.
*   **Configurable Prefixing via `redis-core`**: Base prefix from `RedisCoreProperties.KeyspacePrefixes` (e.g., `application` or `distributed` prefix) + static segment (`:__lock_buckets__`).
*   **Mandatory Lock-Type Segment**: A segment identifying the lock type (e.g., `reentrant`, `stamped`, `state`, `readwrite`) is **mandatory** in all primary lock keys (`lockKey`) and their associated metadata keys (`lockDataKey`).
*   **Namespacing**: Specific namespaces (e.g., `__locks__`, `__unlock_channels__`, `__resp_cache__`, `__rwttl__`).
*   **Bucket-Based Grouping**: Locks grouped by `bucketName`.
*   **Hash Tags for Cluster Compatibility**: The lock identifier (`<lockName>`) enclosed in `{<lockName>}` acts as a hash tag.
*   **`lockKey`**: The primary Redis key for the lock itself. This key has the actual Redis expiry, managed by `PEXPIREAT`. Its value can be the `ownerId` (for simple locks) or it can be a Redis Hash (for reentrant or complex locks like `RedisReadWriteLock`).
*   **`lockDataKey`**: A secondary Redis key, always a Redis Hash, storing persistent metadata associated with the `lockKey`. This includes `ownerId`, `originalLeaseTimeMillis` (the user's intended full lease duration), and `expiresAtMillis` (the current absolute expiry timestamp of `lockKey`). This key typically has a slightly longer TTL than `lockKey` or is managed alongside it to ensure data availability for watchdog and recovery.

## 3. General Key Format

Primary lock-related keys follow this format:
`<prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]`
The `<suffix>` is often `data` for the `lockDataKey`.

### **Note on `<prefix>`:**

Effective prefix is `RedisCoreProperties.keyspacePrefixes.(application|distributed) + :__lock_buckets__`.
Example: if `application` prefix is `myApp`, then `<prefix>` becomes `myApp:__lock_buckets__`.

## 4. Standardized Key Schema Table

| Key Type                               | Standardized Format                                                                                                                                            | Hash Tag       | Example                                                                                                                                                                    | Notes                                                                                                                                                                                                                                                                                                                           |
| :------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Main Lock (`lockKey`)                  | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`                                                                                                      | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`                                                                                                             | For `RedisReentrantLock`, this is a Redis Hash storing `ownerId` (field name) and `reentrantCount` (value). For other types, it might be a String holding `ownerId`. TTL set by `PEXPIREAT` to `expiresAtMillis`. **`<lockType>` is mandatory.**                                                                                |
| Lock Metadata (`lockDataKey`)          | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data`                                                                                                 | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}:data`                                                                                                        | **Always a Redis Hash.** Stores fields: `ownerId` (String), `originalLeaseTimeMillis` (String/long - user's intended full relative lease), `expiresAtMillis` (String/long - current absolute expiry timestamp of `lockKey`). TTL managed alongside `lockKey` (e.g., `expiresAtMillis + buffer`). **`<lockType>` is mandatory.** |
| State Key (for `RedisStateLock`)       | `<prefix>:<bucketName>:__locks__:state:{<lockName>}:state` (`lockKey`) <br/> `<prefix>:<bucketName>:__locks__:state:{<lockName>}:state:data` (`lockDataKey`)   | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:state:{order123}:state`                                                                                                           | `lockKey` for state lock holds its primary lock data. `lockDataKey` holds its metadata. An additional Redis String key might store the actual application state value, with its TTL managed by `stateKeyExpiration`. The `lockType` here is `state`.                                                                            |
| ReadWriteLock Main Hash (`lockKey`)    | `<prefix>:<bucketName>:__locks__:readwrite:{<lockName>}`                                                                                                       | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}`                                                                                                            | Redis Hash. Stores `mode`, `writerId`, reader counts/IDs. TTL managed by `PEXPIREAT` based on watchdog (`safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock leases. `lockType` is `readwrite`.                                                                                              |
| ReadWriteLock Metadata (`lockDataKey`) | `<prefix>:<bucketName>:__locks__:readwrite:{<lockName>}:data`                                                                                                  | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}:data`                                                                                                       | Redis Hash. Stores `ownerId` (of writer if write-locked), `originalLeaseTimeMillis` for the R/W lock itself, `expiresAtMillis` of the main R/W `lockKey`.                                                                                                                                                                       |
| Stamped Lock Data (`lockKey`)          | `<prefix>:<bucketName>:__locks__:stamped:{<lockName>}`                                                                                                         | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}`                                                                                                                    | Redis Hash. Stores `version`, `write_owner_id`, `read_holders`, etc. TTL by `PEXPIREAT`. `lockType` is `stamped`.                                                                                                                                                                                                               |
| Stamped Lock Metadata (`lockDataKey`)  | `<prefix>:<bucketName>:__locks__:stamped:{<lockName>}:data`                                                                                                    | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}:data`                                                                                                               | Redis Hash. Stores relevant metadata for stamped lock like `ownerId` (of writer), `originalLeaseTimeMillis`, `expiresAtMillis`.                                                                                                                                                                                                 |
| Unlock Channel                         | Publish: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}` <br/> Subscribe Pattern: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*` | N/A            | Publish: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}` <br/> Subscribe Pattern: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*` | Locks publish to specific channels including `lockType`. `UnlockMessageListener` (per bucket) subscribes to a pattern. Message payload is *only* `UnlockType`.                                                                                                                                                                  |
| Individual Read Lock Timeout Key       | `<prefix>:<bucketName>:__rwttl__:readwrite:{<lockName>}:<readerId>:<reentrantCount>`                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{configA}:readerId_uuid:1`                                                                                            | Redis String. Stores placeholder. TTL set to `leaseTime` of specific read acquisition. Used by `RedisReadWriteLock`'s Lua scripts. `lockType` (`readwrite`) is part of the key.                                                                                                                                                 |
| Response Cache                         | `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`                                                                                   | `{<lockName>}` | `myApp:__lock_buckets__:orders:__resp_cache__:reentrant:{order123}:a1b2-c3d4`                                                                                              | Redis String. Caches Lua script responses (structured array: `{status, expiresAtMillis, originalLeaseTimeMillis}`) for idempotency. TTL by `responseCacheTtl`. Key includes `lockType`.                                                                                                                                         |

## 5. Key Design Considerations

*   **Semantic Isolation**: The mandatory `<lockType>` segment is crucial.
*   **Metadata Persistence**: `lockDataKey` ensures `originalLeaseTimeMillis` and current `expiresAtMillis` are reliably stored and accessible by Lua scripts, especially for watchdog operations, without relying on `lockKey`'s value for this metadata.
*   **Cluster Compatibility**: Hash tags `{<lockName>}` ensure co-location of `lockKey`, `lockDataKey`, and `responseCacheKey`.
*   **Implementation**: Java key construction utilities and Lua scripts must consistently use this schema.
```

---
## Missing knowledge

After a thorough review and integration process, the following specific details from `detailed-plan.md` were primarily structural or instructional for the update process itself, or were too low-level for the existing documentation structure, and thus are not explicitly "marked as integrated" into the 17 files in a way that directly maps to a sentence or paragraph. However, their *intent and effect* have been incorporated into the updated documents.

**From `detailed-plan.md`:**

*   **Section 1. Introduction and Core Principles (General statements):** While the principles (Async-First, Atomicity, `redis-core` usage, Idempotency, Config Hierarchy, Robustness) are now reflected *throughout* the updated documents, the introductory paragraph of `detailed-plan.md` itself isn't directly copied. Its essence is captured in the updated introductions of files like `architecture_overview.md` and `index.md`.
*   **Section 9. Affected Components and Implementation Plan (Steps 9.1 - 9.10):** This entire section was a detailed set of *instructions* for me on how to perform the updates. The *results* of these instructions are now in the updated 17 files, but the instructions themselves don't belong in the final documentation set.
    *   For example, "Step 9.1: Remove `pubSubWaitTimeout`" - this property has been removed from `configuration.md`, `glossary.md`, etc. The instruction itself is fulfilled.
    *   The detailed breakdown of which files to touch for each sub-step of Section 9 is meta-information about the update process.
*   **Section 10. Rollback Plan:** This is procedural information for development and not part of the user-facing documentation of the module's features or design.

**Summary of "Missing" (i.e., not directly textually integrated but actioned upon or meta-level):**

1.  The specific introductory phrasing of `detailed-plan.md`.
2.  The explicit step-by-step instructions within Section 9 of `detailed-plan.md` (as these were commands for the update process).
3.  The Rollback Plan (Section 10).

All *functional and design specifications* from `detailed-plan.md` (e.g., how the watchdog works, how idempotency is achieved, new configuration properties, key schemas, Virtual Thread usage, Lua script conventions) have been woven into the 17 updated files. The "missing" parts are primarily the scaffolding and instructions of the `detailed-plan.md` itself rather than descriptive content about the locking module that should live in the other documents.

The 17 files are now updated and should be 100% semantically consistent with each other and with the functional specifications laid out in `detailed-plan.md`.