# Redis Locking Module: Final Unlock Messaging Mechanism

## 1. Introduction

This document describes the messaging mechanism used by the `locking-redis-lock` module, specifically utilizing Redis Publish/Subscribe (Pub/Sub) for unlock notifications. This is a core component of the non-polling lock acquisition strategy, designed to efficiently signal waiting `CompletableFuture`s (managed by `LockSemaphoreHolder` and executed on Virtual Threads) when a lock becomes available.

## 2. Redis Publish/Subscribe (Pub/Sub) Overview

Redis Pub/Sub allows publishers to send messages to channels, and subscribers to listen to these channels without direct knowledge of each other.

*   **Publishers**: Lua scripts executed upon successful lock release.
*   **Channels**:
    *   The `UnlockMessageListener` subscribes to a channel pattern like `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`.
    *   The specific lock instance (e.g., `{order123}`) and its `lockType` (e.g., `reentrant`) are part of the actual channel name to which messages are published (e.g., `<prefix>:<bucketName>:__unlock_channels__:reentrant:{order123}`).
*   **Message Payload**: The message published by Lua scripts to a specific lock's Pub/Sub channel contains *only* an `UnlockType` string (e.g., "REGULAR_RELEASE", "RW_WRITE_RELEASED"). The `UnlockMessageListener` derives the `lockName` and `lockType` from the channel name it received the message on.
*   **Subscribers**: `UnlockMessageListener` instances, one per bucket, managed by `UnlockMessageListenerManager`.

## 3. Unlock Notification Mechanism

1.  **Publish on Unlock**: When a lock is successfully released (typically via an atomic Lua script like `unlock.lua` or `unlock_state_lock.lua`), the script publishes a message containing *only* an `UnlockType` string to the specific lock's Pub/Sub channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`).
    *   The `UnlockType` (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED") indicates the nature of the unlock event.
2.  **Subscription and Listening**:
    *   When a task (on a Virtual Thread) attempts to acquire a lock and finds it held, its associated `CompletableFuture` is registered with a `LockSemaphoreHolder` for that specific `lockKey`. The `LockSemaphoreHolder` itself is registered with the `UnlockMessageListenerManager` *before* the first Redis acquisition attempt.
    *   The `UnlockMessageListenerManager` ensures that an `UnlockMessageListener` for the relevant `bucketName` is active. This listener subscribes to a pattern for its bucket-specific Pub/Sub channels, including the `lockType` segment (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
3.  **Signal Waiting Futures**:
    *   When an `UnlockMessageListener` receives a message:
        *   It derives the specific `lockName` (e.g., `{order123}`) and `lockType` from the *channel name* on which the message was received.
        *   It parses the `UnlockType` from the message payload.
        *   It retrieves the associated `LockSemaphoreHolder` for the derived `lockKey` (which includes `lockName` and `lockType`).
        *   Based on the `UnlockType`, it intelligently signals the `LockSemaphoreHolder`, which in turn completes the waiting `CompletableFuture`(s).
4.  **Wake Up and Retry**: Completing the `CompletableFuture` wakes up the Virtual Thread associated with the lock acquisition attempt. This awakened task then re-attempts to acquire the lock.

## 4. Key Components

*   **`UnlockMessageListenerManager` (Bean)**:
    *   Manages the lifecycle of `UnlockMessageListener` instances.
    *   Handles registration of `LockSemaphoreHolder` instances.
*   **`UnlockMessageListener` (Per Bucket, Managed by Manager)**:
    *   Implements `org.springframework.data.redis.connection.MessageListener`.
    *   Subscribes to a Redis Pub/Sub channel pattern specific to its `bucketName` and including `lockType` (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
    *   Internally manages a map of `LockSemaphoreHolder` instances, keyed by the full `lockKey`.
    *   The `onMessage()` method processes messages by deriving `lockName` and `lockType` from the channel and parsing `UnlockType` from the payload, using an optimized executor (e.g., Virtual Threads).
    *   Signals the `LockSemaphoreHolder` associated with the derived `lockKey`.
*   **`LockSemaphoreHolder` (Non-Spring managed, per `lockKey`)**:
    *   Manages `CompletableFuture`s waiting for a specific lock.
    *   Provides methods to register a `CompletableFuture` and to signal/complete it.

## 5. Unlock Channel Key Schema

As defined in the [Redis Key Schema](redis_key_schema.md) document:
The `UnlockMessageListener` subscribes to a channel pattern such as `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`.
The message published by Lua scripts to a specific lock's channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`) will *only* contain an `UnlockType` string (e.g., "REGULAR_RELEASE"). The `UnlockMessageListener` derives the specific `lockName` (e.g., `{<lockName>}`) and `lockType` from the channel name itself.
Example: Listener subscribes to `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*`. A lock release for `reentrant` lock `{order123}` would publish the `UnlockType` to `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}`.

## 5.A. Defined `UnlockType` Values and Listener Logic

The `UnlockType` string published by Lua scripts provides crucial information to the `UnlockMessageListener` for optimized waking of waiting tasks. The listener derives the `lockName` and `lockType` from the channel name and uses the `UnlockType` from the message payload to decide its signaling strategy.

(UnlockTypes remain largely the same as previously defined, e.g., `REENTRANT_FULLY_RELEASED`, `RW_WRITE_RELEASED_WAKEN_ALL`, etc. The listener logic focuses on completing one or more `CompletableFuture`s managed by `LockSemaphoreHolder`.)

*   **`REENTRANT_FULLY_RELEASED`**: Signals one `CompletableFuture`.
*   **`NON_REENTRANT_RELEASED`**: Signals one `CompletableFuture`.
*   **`STATE_LOCK_RELEASED_STATE_UNCHANGED` / `_UPDATED`**: Signals one `CompletableFuture`.
*   **`RW_READ_RELEASED_WAKEN_READERS`**: May complete multiple `CompletableFuture`s if multiple read waiters exist.
*   **`RW_READ_RELEASED_WAKEN_SINGLE_WRITER`**: Signals one `CompletableFuture` (likely for a writer).
*   **`RW_WRITE_RELEASED_WAKEN_ALL`**: May complete multiple `CompletableFuture`s (for readers and one writer).
*   **`STAMPED_READ_RELEASED` / `_WRITE_RELEASED` / `_CONVERTED_TO_READ` / `_TO_WRITE`**: Similar logic, completing relevant waiting `CompletableFuture`s.

## 6. Reliability and Considerations

*   **Message Delivery**: Redis Pub/Sub is "fire-and-forget."
*   **Fallback Mechanism**: The lock acquisition logic includes a fallback. Waiting tasks also have a timeout based on the current lock holder's TTL (obtained from the acquisition attempt) or `defaults.retryInterval`. If an unlock notification is missed, the waiting task will eventually time out its wait on the `CompletableFuture` (or the sleep for `retryInterval`) and re-attempt acquisition.
*   **Message Content & Channel**: Payload is *only* `UnlockType`. `lockName` and `lockType` are derived from the channel.
*   **Executor for Listeners**: Using Virtual Threads for `UnlockMessageListener`'s `onMessage` processing is critical to prevent blocking platform threads.
*   **Stale Signals**: The `CompletableFuture`-based waiting inherently handles "stale" signals correctly; if a future is already completed (e.g., by timeout), a late Pub/Sub signal will have no effect on it.
*   **`LockSemaphoreHolder` Registration**: Must occur *before* the first Redis acquisition attempt to avoid race conditions.
