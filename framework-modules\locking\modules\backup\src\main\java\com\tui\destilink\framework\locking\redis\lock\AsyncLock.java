package com.tui.destilink.framework.locking.redis.lock;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * An asynchronous extension of the standard
 * {@link java.util.concurrent.locks.Lock} interface.
 * <p>
 * This interface provides non-blocking, {@link CompletableFuture}-based
 * asynchronous versions
 * of standard lock operations, suitable for reactive programming models and
 * high-concurrency
 * scenarios to avoid thread blocking. All concrete lock implementations in this
 * module must
 * implement this interface.
 * </p>
 * <p>
 * The asynchronous methods return {@link CompletableFuture}s that complete when
 * the corresponding
 * lock operation completes. This allows for improved resource utilization in
 * highly concurrent,
 * reactive applications by offloading waiting to the
 * {@link CompletableFuture}'s completion stages.
 * </p>
 * *
 * <p>
 * Implementations of this interface must also implement the standard
 * {@link java.util.concurrent.locks.Lock} interface,
 * with synchronous methods acting as blocking wrappers around these
 * asynchronous operations.
 * </p>
 *
 * @see java.util.concurrent.locks.Lock
 */
public interface AsyncLock extends Lock {

    /**
     * Acquires the lock asynchronously.
     * <p>
     * If the lock is not available, the returned {@link CompletableFuture} will
     * complete
     * only when the lock has been acquired.
     * </p>
     *
     * @return a {@link CompletableFuture} that completes when the lock is acquired
     */
    CompletableFuture<Void> lockAsync();

    /**
     * Acquires the lock asynchronously unless the current thread is interrupted.
     * <p>
     * If the lock is not available, the returned {@link CompletableFuture} will
     * complete
     * only when the lock has been acquired.
     * </p>
     * <p>
     * If the thread is interrupted while waiting for the lock, the future will
     * complete
     * exceptionally with an {@link InterruptedException}.
     * </p>
     *
     * @return a {@link CompletableFuture} that completes when the lock is acquired
     */
    CompletableFuture<Void> lockInterruptiblyAsync();

    /**
     * Attempts to acquire the lock asynchronously without blocking.
     * <p>
     * The returned {@link CompletableFuture} will complete with {@code true} if the
     * lock was acquired,
     * and {@code false} if the lock was not available.
     * </p>
     *
     * @return a {@link CompletableFuture} that completes with {@code true} if the
     *         lock was acquired,
     *         and {@code false} otherwise
     */
    CompletableFuture<Boolean> tryLockAsync();

    /**
     * Attempts to acquire the lock asynchronously, waiting up to the specified time
     * if necessary.
     * <p>
     * The returned {@link CompletableFuture} will complete with {@code true} if the
     * lock was acquired
     * within the specified time, and {@code false} if the lock could not be
     * acquired before the timeout.
     * </p>
     *
     * @param time the maximum time to wait for the lock
     * @param unit the time unit of the {@code time} argument
     * @return a {@link CompletableFuture} that completes with {@code true} if the
     *         lock was acquired,
     *         and {@code false} if the waiting time elapsed before the lock was
     *         acquired
     */
    CompletableFuture<Boolean> tryLockAsync(long time, TimeUnit unit);

    /**
     * Releases the lock asynchronously.
     * <p>
     * The returned {@link CompletableFuture} will complete when the lock has been
     * released.
     * </p>
     *
     * @return a {@link CompletableFuture} that completes when the lock is released
     */
    CompletableFuture<Void> unlockAsync();

    /**
     * Checks asynchronously if this lock is currently held.
     * <p>
     * The returned {@link CompletableFuture} will complete with {@code true} if the
     * lock is currently held,
     * and {@code false} otherwise.
     * </p>
     * *
     * <p>
     * Note that this method is not part of the standard
     * {@link java.util.concurrent.locks.Lock} interface and
     * is specific to {@link AsyncLock}.
     * </p>
     *
     * @return a {@link CompletableFuture} that completes with {@code true} if the
     *         lock is held,
     *         and {@code false} otherwise
     */
    CompletableFuture<Boolean> isLockedAsync();
}