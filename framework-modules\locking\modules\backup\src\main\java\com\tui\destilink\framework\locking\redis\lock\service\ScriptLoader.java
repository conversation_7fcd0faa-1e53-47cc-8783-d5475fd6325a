package com.tui.destilink.framework.locking.redis.lock.service;

import org.springframework.data.redis.core.script.RedisScript;

/**
 * Service responsible for loading and caching Redis Lua scripts used by the
 * locking mechanism.
 * <p>
 * This interface provides methods to access pre-loaded Lua scripts for various
 * lock operations
 * such as acquiring, releasing, extending, and checking locks. Scripts are
 * loaded once during
 * initialization and cached for subsequent use to improve performance.
 * </p>
 */
public interface ScriptLoader {

    /**
     * Loads a script from the specified path and returns its SHA.
     * <p>
     * This method is used to load scripts that are not pre-loaded during
     * initialization.
     * </p>
     *
     * @param scriptPath The path to the script file
     * @return The SHA of the loaded script
     */
    String loadScript(String scriptPath);

    /**
     * Loads a script from the specified path with the given result type.
     * <p>
     * This method is used to load scripts that are not pre-loaded during
     * initialization and specify the expected result type.
     * </p>
     *
     * @param scriptPath The path to the script file
     * @param resultType The expected result type of the script
     * @param <T>        The type parameter for the result type
     * @return The loaded script
     */
    <T> RedisScript<T> loadScript(String scriptPath, Class<T> resultType);

    /**
     * Gets the script for acquiring a lock.
     * <p>
     * This script atomically checks if a lock can be acquired and sets it if
     * possible.
     * It also handles the case where the lock is already held by the same owner,
     * in which case it extends the lock's TTL.
     * </p>
     *
     * @return The Redis script for acquiring a lock
     */
    RedisScript<Long> getAcquireLockScript();

    /**
     * Gets the script for releasing a lock.
     * <p>
     * This script atomically checks if the lock is held by the specified owner
     * and releases it if so. This prevents accidental release of locks held by
     * other processes.
     * </p>
     *
     * @return The Redis script for releasing a lock
     */
    RedisScript<Long> getReleaseLockScript();

    /**
     * Gets the script for extending a lock's TTL.
     * <p>
     * This script atomically checks if the lock is held by the specified owner
     * and extends its TTL if so. This prevents accidental extension of locks held
     * by
     * other processes.
     * </p>
     *
     * @return The Redis script for extending a lock's TTL
     */
    RedisScript<Long> getExtendLockScript();

    /**
     * Gets the script for checking if a lock exists.
     * <p>
     * This script checks if a lock exists and returns information about its current
     * state.
     * </p>
     *
     * @return The Redis script for checking a lock's existence
     */
    RedisScript<Long> getCheckLockScript();

    /**
     * Gets the script for trying to acquire a lock.
     * <p>
     * Similar to the acquire lock script, but specifically designed for
     * non-blocking
     * try-lock operations.
     * </p>
     *
     * @return The Redis script for trying to acquire a lock
     */
    RedisScript<Long> getTryLockScript();

    /**
     * Gets the script for updating a state lock's state.
     * <p>
     * This script atomically checks if the lock is held by the specified owner
     * and updates the state if so.
     * </p>
     *
     * @return The Redis script for updating a state lock's state
     */
    RedisScript<Long> getUpdateStateScript();

    /**
     * Gets the script for updating a state lock's state only if it matches the
     * expected state.
     * <p>
     * This script atomically checks if the lock is held by the specified owner,
     * verifies the current state matches the expected state, and updates the state
     * if both conditions are met.
     * </p>
     *
     * @return The Redis script for conditionally updating a state lock's state
     */
    RedisScript<Long> getUpdateStateIfEqualsScript();

    /**
     * Gets the script for trying to acquire a state lock.
     * <p>
     * This script atomically checks if a state lock can be acquired based on the
     * current state
     * and sets it if possible.
     * </p>
     *
     * @return The Redis script for trying to acquire a state lock
     */
    RedisScript<Long> getTryStateLockScript();

    /**
     * Gets the script for releasing a state lock.
     * <p>
     * This script atomically checks if the state lock is held by the specified
     * owner
     * and releases it if so, optionally setting a new state.
     * </p>
     *
     * @return The Redis script for releasing a state lock
     */
    RedisScript<Long> getUnlockStateLockScript();
}