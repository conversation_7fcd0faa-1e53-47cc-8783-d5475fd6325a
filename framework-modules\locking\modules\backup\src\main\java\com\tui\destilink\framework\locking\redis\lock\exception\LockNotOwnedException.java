package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when an operation is attempted on a lock that is not owned
 * by the caller.
 * <p>
 * This exception is thrown when a thread attempts to release or modify a lock
 * that is currently owned by a different owner.
 * </p>
 */
public class LockNotOwnedException extends AbstractRedisLockException {

    private final String expectedOwnerId;
    private final String actualOwnerId;

    /**
     * Constructs a new LockNotOwnedException with the specified details.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param expectedOwnerId The owner ID that was expected to own the lock
     * @param actualOwnerId   The actual owner ID of the lock (can be null if not
     *                        discoverable)
     * @param message         Descriptive error message
     */
    public LockNotOwnedException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String expectedOwnerId, String actualOwnerId, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.expectedOwnerId = expectedOwnerId;
        this.actualOwnerId = actualOwnerId;
    }

    /**
     * Constructs a new LockNotOwnedException with the specified details and cause.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param expectedOwnerId The owner ID that was expected to own the lock
     * @param actualOwnerId   The actual owner ID of the lock (can be null if not
     *                        discoverable)
     * @param message         Descriptive error message
     * @param cause           The underlying cause of this exception
     */
    public LockNotOwnedException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String expectedOwnerId, String actualOwnerId, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.expectedOwnerId = expectedOwnerId;
        this.actualOwnerId = actualOwnerId;
    }

    /**
     * Gets the owner ID that was expected to own the lock.
     *
     * @return The expected owner ID
     */
    public String getExpectedOwnerId() {
        return expectedOwnerId;
    }

    /**
     * Gets the actual owner ID of the lock, if discoverable.
     *
     * @return The actual owner ID, or null if not discoverable
     */
    public String getActualOwnerId() {
        return actualOwnerId;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (expectedOwnerId != null) {
            contextMap.put("lock.expectedOwnerId", expectedOwnerId);
        }

        if (actualOwnerId != null) {
            contextMap.put("lock.actualOwnerId", actualOwnerId);
        }
    }
}