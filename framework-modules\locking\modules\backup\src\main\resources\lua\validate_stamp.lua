-- KEYS[1] stampedLockDataKey (e.g., prefix:bucket:__locks__:{myStampedLock}:stamped)
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] expectedVersion (the version string from the optimistic read stamp)
-- ARGV[3] lockOwnerId (for context/logging, not directly used for validation logic)
-- ARGV[4] responseCacheTTLSeconds

local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    return tonumber(cachedResult);
end;

local currentVersion = redis.call('hget', KEYS[1], 'version');
local writeOwnerId = redis.call('hget', KEYS[1], 'write_owner_id');

local isValid = 0;
if (currentVersion ~= false and currentVersion == ARGV[2] and writeOwnerId == false) then
    isValid = 1;
end;

redis.call('set', KEYS[2], isValid, 'px', ARGV[4]);
return isValid;