# Redis Locking Module: Final Watchdog Mechanism

## 1. Introduction

The `LockWatchdog` is a critical component in the `locking-redis-lock` module. Its primary responsibility is to prevent distributed locks from expiring prematurely while they are still actively and legitimately held by an application instance. This mechanism is essential for long-running operations. This document details the refined design of the watchdog, which is **always active** if the module is enabled, but conditionally monitors locks.

## 2. Purpose and Design Principles

*   **Lease Extension Pattern**: The watchdog implements lease extension. Locks are acquired with an intended `relativeLeaseTimeMillis`.
*   **Prevent Premature Expiration**: It periodically renews the lock's TTL in Redis (using `PEXPIREAT`) as long as the acquiring application instance is alive and the lock is registered with the watchdog.
*   **`originalLeaseTimeMillis` Preservation**: The watchdog **never** modifies the `originalLeaseTimeMillis` (user's intended full lease duration) stored in the `lockDataKey` in Redis. It only uses this value to calculate the target `expiresAtMillis` for renewal.
*   **Resilience**: If the application instance crashes or the lock is explicitly released, the watchdog stops renewing, allowing the lock to expire naturally.
*   **Redis Time Precision**: All expiry calculations in Lua scripts use `redis.call('TIME')`.

## 3. Core Components and Configuration

*   **`LockWatchdog.java` (Bean)**:
    *   A Spring-managed singleton bean, **always instantiated and active** if `destilink.fw.locking.redis.enabled=true`.
    *   Dependencies: `RedisLockOperations` (for Lua script execution).
    *   Maintains an internal `ConcurrentHashMap<String, LockInfo>` (`monitoredLocks`).
*   **`LockWatchdog.LockInfo` (Inner Class)**:
    *   Stores `ownerId`, `userIntendedExpireTimeMillis` (absolute Unix timestamp calculated from `originalLeaseTimeMillis` at registration), and `currentAbsoluteExpiresAtMillis` (the current `expiresAtMillis` of the lock in Redis, updated after each successful refresh).
*   **Global Configuration (`RedisLockProperties.WatchdogProperties`)**:
    *   **`interval` (Duration)**: Interval at which the watchdog's scheduled task runs to check and potentially extend leases.
    *   **`factor` (double)**: Multiplier for `interval` to calculate `safetyBufferMillis`.
    *   `corePoolSize`, `threadNamePrefix`, `shutdownAwaitTermination` for its executor.
    *   (Note: `operationMaxRetries` and `operationTimeout` are removed from watchdog config; `RedisLockOperationsImpl` handles retries for its operations using `Defaults.maxRetries` and `Defaults.retryInterval`.)
*   **`safetyBufferMillis`**: Calculated as `watchdogProperties.getInterval().toMillis() * watchdogProperties.getFactor()`.
*   **Instance-Level Activation Conditions (Watchdog Monitoring)**: The watchdog monitors a specific lock instance if **all** the following are true:
    1.  Global Redis locking is enabled (`destilink.fw.locking.redis.enabled` is `true`).
    2.  The lock is "application-instance-bound": `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returns `true`.
    3.  The lock's `userProvidedLeaseTime` (the `relativeLeaseTimeMillis` provided at acquisition/extension) is greater than `safetyBufferMillis`.

## 4. Operational Flow

### 4.1. Lock Registration & Initial TTL

1.  When a lock is successfully acquired by `AbstractRedisLock`:
    *   The `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) is known.
    *   The `originalLeaseTimeMillis` (which is this `userProvidedLeaseTime`) and the initial `expiresAtMillis` are stored in the `lockDataKey` in Redis by the acquisition Lua script.
2.  `AbstractRedisLock` checks watchdog monitoring eligibility:
    *   **If NOT Monitored**: The initial `lockKey` TTL in Redis is set based on the full `userProvidedLeaseTime`.
    *   **If Monitored**: The initial `lockKey` TTL in Redis is set to `safetyBufferMillis` (via `PEXPIREAT currentTime + safetyBufferMillis`).
3.  If monitored, `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, ownerId, userProvidedLeaseTime, initialExpiresAtMillisFromRedis)`.
4.  The `LockWatchdog` stores a `LockInfo` with `ownerId`, `userIntendedExpireTimeMillis = initialExpiresAtMillisFromRedis - safetyBufferMillis + userProvidedLeaseTime` (recalculating the user's intended absolute expiry based on the full lease), and `currentAbsoluteExpiresAtMillis = initialExpiresAtMillisFromRedis`.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `extendLocks()` method is scheduled to run every `watchdog.interval`.
2.  It iterates through `monitoredLocks`. For each `LockInfo`:
    *   Calculates `remainingUserTimeMillis = lockInfo.getUserIntendedExpireTimeMillis() - System.currentTimeMillis()`.
    *   **If `remainingUserTimeMillis <= 0` (User's intended lease expired):**
        *   Unregister the lock from `monitoredLocks`.
        *   Log a warning if the lock still appears to be held by this owner in Redis (this indicates the user might have expected it to live longer but didn't extend it, or a final refresh failed).
    *   **Else (User's intended lease has not expired):**
        *   Determine `targetExpiresAtMillis` for the refresh:
            *   **Final Leg (`0 < remainingUserTimeMillis <= safetyBufferMillis`):** `targetExpiresAtMillis = lockInfo.getUserIntendedExpireTimeMillis()`.
            *   **Standard Operation (`remainingUserTimeMillis > safetyBufferMillis`):** `targetExpiresAtMillis = System.currentTimeMillis() + safetyBufferMillis`.
        *   Execute `watchdog_refresh_lock.lua` via `RedisLockOperations` (which handles its own retries for this Redis command).
            *   **Lua Script (`watchdog_refresh_lock.lua`)**:
                *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `targetExpiresAtMillis`, `requestUuid`, `responseCacheTtl`.
                *   Implements idempotency wrapper.
                *   Atomically checks if `lockKey` in Redis is still held by `expectedOwnerId`.
                *   If owner matches:
                    *   Updates `expiresAtMillis` in `lockDataKey` to `targetExpiresAtMillis`.
                    *   Sets `PEXPIREAT` on `lockKey` to `targetExpiresAtMillis`.
                    *   Returns success status, new `expiresAtMillis` from Redis, and `originalLeaseTimeMillis` (read from `lockDataKey`, unchanged).
                *   If owner mismatch or lock not found, returns failure.
        *   **Result Handling**:
            *   **Success**: Update `lockInfo.setCurrentAbsoluteExpiresAtMillis()` with the new `expiresAtMillis` returned from the script. If it was the "Final Leg" refresh, unregister the lock from `monitoredLocks`.
            *   **Failure (owner mismatch, lock gone, or Redis error after retries)**: Remove `lockKey` from `monitoredLocks`. Log a WARNING.

### 4.3. Lock Unregistration (User Initiated)

1.  When `AbstractRedisLock.unlock()` is called.
2.  `unlock()` calls `lockWatchdog.unregisterLock(lockKey, ownerId)`.
3.  The `LockWatchdog` removes the entry from `monitoredLocks`.

### 4.4. User-Initiated Lock Extension (`AbstractRedisLock.extendLeaseAsync`)

1.  User calls `extendLeaseAsync(newRelativeLeaseTime)`.
2.  `AbstractRedisLock` calls `RedisLockOperations.extendLeaseAsync(...)` which executes `extend_lock.lua`.
    *   **Lua Script (`extend_lock.lua`)**:
        *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `newRelativeLeaseTime`, `requestUuid`, `responseCacheTtl`.
        *   Implements idempotency wrapper.
        *   Atomically checks ownership.
        *   If owner matches:
            *   Updates `originalLeaseTimeMillis` in `lockDataKey` to `newRelativeLeaseTime`.
            *   Calculates `newExpiresAtMillis` based on `currentTime + newRelativeLeaseTime`.
            *   Updates `expiresAtMillis` in `lockDataKey` to this `newExpiresAtMillis`.
            *   Sets `PEXPIREAT` on `lockKey` to this `newExpiresAtMillis`.
            *   Returns success, `newExpiresAtMillis`, and the updated `originalLeaseTimeMillis`.
3.  `AbstractRedisLock` then calls `lockWatchdog.updateRegistration(lockKey, ownerId, newOriginalLeaseTimeMillisFromScript, newExpiresAtMillisFromScript)`.
    *   The watchdog re-evaluates eligibility. If still monitored, it updates its `LockInfo` with the new `userIntendedExpireTimeMillis` (derived from `newOriginalLeaseTimeMillisFromScript`) and `currentAbsoluteExpiresAtMillis`. If the new lease makes it no longer eligible (e.g., new lease is too short), it unregisters it.

## 5. Key Considerations

*   **Atomicity of Lua Scripts**: `watchdog_refresh_lock.lua` and `extend_lock.lua` are critical for atomic check-and-set operations.
*   **Idempotency**: All watchdog-related Lua scripts must be idempotent.
*   **Configuration Alignment**: `watchdog.interval` and `watchdog.factor` (determining `safetyBufferMillis`) must be sensible.
*   **Clock Skew**: Using Redis `TIME` in Lua and `PEXPIREAT` helps manage consistency. The `safetyBufferMillis` provides a buffer.