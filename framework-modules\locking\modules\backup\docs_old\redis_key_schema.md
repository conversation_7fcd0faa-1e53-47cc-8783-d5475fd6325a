# Redis Locking Module: Final Redis Key Schema

## 1. Introduction

This document outlines the consolidated Redis key schema used by the `locking-redis-lock` module. A well-defined and consistent key schema is crucial for managing lock data effectively, avoiding conflicts, ensuring compatibility with Redis Cluster, and enabling **semantic isolation between different lock types via a mandatory lock-type segment**. This schema also introduces the concept of a `lockDataKey` for storing persistent lock metadata.

All Redis keys MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `RedisKey`, and `RedisKeyPrefix` from the `redis-core` module.

## 2. Key Structure Principles

*   **Hierarchical Naming**: Colons (`:`) as separators.
*   **Configurable Prefixing via `redis-core`**: Base prefix from `RedisCoreProperties.KeyspacePrefixes` (e.g., `application` or `distributed` prefix) + static segment (`:__lock_buckets__`).
*   **Mandatory Lock-Type Segment**: A segment identifying the lock type (e.g., `reentrant`, `stamped`, `state`, `readwrite`) is **mandatory** in all primary lock keys (`lock<PERSON>ey`) and their associated metadata keys (`lockData<PERSON>ey`).
*   **Namespacing**: Specific namespaces (e.g., `__locks__`, `__unlock_channels__`, `__resp_cache__`, `__rwttl__`).
*   **Bucket-Based Grouping**: Locks grouped by `bucketName`.
*   **Hash Tags for Cluster Compatibility**: The lock identifier (`<lockName>`) enclosed in `{<lockName>}` acts as a hash tag.
*   **`lockKey`**: The primary Redis key for the lock itself. This key has the actual Redis expiry, managed by `PEXPIREAT`. Its value can be the `ownerId` (for simple locks) or it can be a Redis Hash (for reentrant or complex locks like `RedisReadWriteLock`).
*   **`lockDataKey`**: A secondary Redis key, always a Redis Hash, storing persistent metadata associated with the `lockKey`. This includes `ownerId`, `originalLeaseTimeMillis` (the user's intended full lease duration), and `expiresAtMillis` (the current absolute expiry timestamp of `lockKey`). This key typically has a slightly longer TTL than `lockKey` or is managed alongside it to ensure data availability for watchdog and recovery.

## 3. General Key Format

Primary lock-related keys follow this format:
`<prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]`
The `<suffix>` is often `data` for the `lockDataKey`.

### **Note on `<prefix>`:**

Effective prefix is `RedisCoreProperties.keyspacePrefixes.(application|distributed) + :__lock_buckets__`.
Example: if `application` prefix is `myApp`, then `<prefix>` becomes `myApp:__lock_buckets__`.

## 4. Standardized Key Schema Table

| Key Type                               | Standardized Format                                                                                                                                            | Hash Tag       | Example                                                                                                                                                                    | Notes                                                                                                                                                                                                                                                                                                                           |
| :------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Main Lock (`lockKey`)                  | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`                                                                                                      | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`                                                                                                             | For `RedisReentrantLock`, this is a Redis Hash storing `ownerId` (field name) and `reentrantCount` (value). For other types, it might be a String holding `ownerId`. TTL set by `PEXPIREAT` to `expiresAtMillis`. **`<lockType>` is mandatory.**                                                                                |
| Lock Metadata (`lockDataKey`)          | `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data`                                                                                                 | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}:data`                                                                                                        | **Always a Redis Hash.** Stores fields: `ownerId` (String), `originalLeaseTimeMillis` (String/long - user's intended full relative lease), `expiresAtMillis` (String/long - current absolute expiry timestamp of `lockKey`). TTL managed alongside `lockKey` (e.g., `expiresAtMillis + buffer`). **`<lockType>` is mandatory.** |
| State Key (for `RedisStateLock`)       | `<prefix>:<bucketName>:__locks__:state:{<lockName>}:state` (`lockKey`) <br/> `<prefix>:<bucketName>:__locks__:state:{<lockName>}:state:data` (`lockDataKey`)   | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:state:{order123}:state`                                                                                                           | `lockKey` for state lock holds its primary lock data. `lockDataKey` holds its metadata. An additional Redis String key might store the actual application state value, with its TTL managed by `stateKeyExpiration`. The `lockType` here is `state`.                                                                            |
| ReadWriteLock Main Hash (`lockKey`)    | `<prefix>:<bucketName>:__locks__:readwrite:{<lockName>}`                                                                                                       | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}`                                                                                                            | Redis Hash. Stores `mode`, `writerId`, reader counts/IDs. TTL managed by `PEXPIREAT` based on watchdog (`safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock leases. `lockType` is `readwrite`.                                                                                              |
| ReadWriteLock Metadata (`lockDataKey`) | `<prefix>:<bucketName>:__locks__:readwrite:{<lockName>}:data`                                                                                                  | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}:data`                                                                                                       | Redis Hash. Stores `ownerId` (of writer if write-locked), `originalLeaseTimeMillis` for the R/W lock itself, `expiresAtMillis` of the main R/W `lockKey`.                                                                                                                                                                       |
| Stamped Lock Data (`lockKey`)          | `<prefix>:<bucketName>:__locks__:stamped:{<lockName>}`                                                                                                         | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}`                                                                                                                    | Redis Hash. Stores `version`, `write_owner_id`, `read_holders`, etc. TTL by `PEXPIREAT`. `lockType` is `stamped`.                                                                                                                                                                                                               |
| Stamped Lock Metadata (`lockDataKey`)  | `<prefix>:<bucketName>:__locks__:stamped:{<lockName>}:data`                                                                                                    | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}:data`                                                                                                               | Redis Hash. Stores relevant metadata for stamped lock like `ownerId` (of writer), `originalLeaseTimeMillis`, `expiresAtMillis`.                                                                                                                                                                                                 |
| Unlock Channel                         | Publish: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}` <br/> Subscribe Pattern: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*` | N/A            | Publish: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}` <br/> Subscribe Pattern: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*` | Locks publish to specific channels including `lockType`. `UnlockMessageListener` (per bucket) subscribes to a pattern. Message payload is *only* `UnlockType`.                                                                                                                                                                  |
| Individual Read Lock Timeout Key       | `<prefix>:<bucketName>:__rwttl__:readwrite:{<lockName>}:<readerId>:<reentrantCount>`                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{configA}:readerId_uuid:1`                                                                                            | Redis String. Stores placeholder. TTL set to `leaseTime` of specific read acquisition. Used by `RedisReadWriteLock`'s Lua scripts. `lockType` (`readwrite`) is part of the key.                                                                                                                                                 |
| Response Cache                         | `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`                                                                                   | `{<lockName>}` | `myApp:__lock_buckets__:orders:__resp_cache__:reentrant:{order123}:a1b2-c3d4`                                                                                              | Redis String. Caches Lua script responses (structured array: `{status, expiresAtMillis, originalLeaseTimeMillis}`) for idempotency. TTL by `responseCacheTtl`. Key includes `lockType`.                                                                                                                                         |

## 5. Key Design Considerations

*   **Semantic Isolation**: The mandatory `<lockType>` segment is crucial.
*   **Metadata Persistence**: `lockDataKey` ensures `originalLeaseTimeMillis` and current `expiresAtMillis` are reliably stored and accessible by Lua scripts, especially for watchdog operations, without relying on `lockKey`'s value for this metadata.
*   **Cluster Compatibility**: Hash tags `{<lockName>}` ensure co-location of `lockKey`, `lockDataKey`, and `responseCacheKey`.
*   **Implementation**: Java key construction utilities and Lua scripts must consistently use this schema.