# Redis Locking Module: Glossary

## A

**AbstractRedisLock**
: Base abstract class providing common functionality for all Redis-based distributed locks. Implements both synchronous and asynchronous lock operations.

**Acquire Timeout**
: Maximum time to wait for a lock acquisition operation to complete. This is an approximate limit for the entire application-level lock attempt and does not interrupt in-flight Redis commands. Distinct from lease time.

**AsyncLock**
: Primary interface for asynchronous lock operations, returning `CompletableFuture`s for non-blocking execution.

**Async-First**
: Design principle where all core operations are fundamentally asynchronous, with synchronous methods acting as blocking wrappers, often leveraging [[Virtual Threads]].

## B

**Bucket**
: Logical grouping of locks that share common configuration settings. Configured programmatically via builder API.

**Builder Pattern**
: Fluent API design used for configuring locks: `LockBucketRegistry` → `LockBucketBuilder` → `LockConfigBuilder` → Specific lock builders.

## C

**ClusterCommandExecutor**
: Component from `redis-core` module used exclusively for all Redis operations in the locking module.

**CompletableFuture**
: Java's asynchronous computation abstraction used throughout the module for non-blocking operations.

**Conditional Acquisition**
: Lock acquisition that depends on external conditions, such as state values in `RedisStateLock`.

## D

**Distributed Lock**
: Synchronization mechanism that works across multiple application instances using Redis as coordination backend.

**Distributed Reentrancy**
: Ability for the same logical owner to acquire a lock multiple times across different application instances.

## E

**expiresAtMillis**
: The absolute Unix timestamp (in milliseconds) when a lock is currently set to expire in Redis, calculated by the Redis server. This value is stored in the [[lockDataKey]] and used with `PEXPIREAT`.

**ExceptionMarkerProvider**
: Interface for structured logging integration, implemented by `AbstractRedisLockException` for rich log context.

## F

**Fairness**
: Property determining lock acquisition order when multiple clients are waiting (implementation-dependent).

**Fallback Polling**
: Secondary mechanism that retries lock acquisition after timeout if Pub/Sub notifications are missed.

## H

**Hash Tag**
: Redis Cluster feature using `{lockName}` syntax to ensure related keys are co-located on the same node.

**Hold Count**
: Number of times a reentrant lock has been acquired by the current owner without corresponding releases.

## I

**Idempotency**
: Property ensuring that performing an operation multiple times has the same effect as performing it once. Achieved through the [[Idempotency Wrapper]] and [[requestUuid]].

**Idempotency Wrapper**
: A pattern applied to all mutating [[Lua Script]]s. It checks a Redis response cache using a [[requestUuid]] before executing core logic, and stores the operation's result in the cache upon completion, preventing duplicate operations.

**Individual Read Timeout Key**
: Separate Redis key for each reentrant read lock acquisition in `RedisReadWriteLock`, managing individual TTLs.

## L

**Lease Extension**
: Process of extending a lock's TTL before it expires, typically performed by the [[Watchdog]].

**Lease Time**
: The duration for which a lock is held before automatic expiration. This can refer to the [[relativeLeaseTimeMillis]] (user-requested) or the [[originalLeaseTimeMillis]] (user's intended duration stored in Redis).

**lockDataKey**
: A secondary Redis Hash key used to store additional metadata associated with the [[lockKey]], such as [[ownerId]], [[originalLeaseTimeMillis]], and [[expiresAtMillis]]. Its key format follows the same lock-type-specific pattern as the [[lockKey]].

**lockKey**
: The primary Redis key for the distributed lock itself. This key holds the [[ownerId]] as its value and has an associated expiry. Its format includes a [[Lock-Type Segment]] for semantic isolation.

**Lock-Type Segment**
: A mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`) that semantically isolates different lock types, preventing cross-type interference. See [[Redis Key Schema]].

**Lock Bucket Config**
: Runtime configuration object containing resolved settings for a specific lock bucket.

**Lock Semaphore Holder**
: Component managing waiting threads/futures for a specific lock key, handling both synchronous and asynchronous waits.

**Lua-Only Operations**
: A strict requirement that all Redis operations related to lock state, TTL, `expiresAt`, or any lock metadata must be performed exclusively via [[Lua Script]]s to guarantee atomicity and consistency.

**Lua Script**
: Server-side script executed atomically on Redis to ensure consistency of multi-command operations. All critical lock operations are [[Lua-Only Operations]].

## M

**MDC (Mapped Diagnostic Context)**
: Logging mechanism for adding contextual information to log entries, propagated to [[Virtual Threads]].

**Mutual Exclusion**
: Property ensuring only one entity can hold a lock at any given time.

## N

**Non-Polling**
: Primary lock acquisition strategy using Redis Pub/Sub notifications instead of continuous polling.

## O

**Optimistic Reading**
: Reading data without acquiring a lock, then validating that the data hasn't changed (used in `RedisStampedLock`).

**originalLeaseTimeMillis**
: The [[relativeLeaseTimeMillis]] value that was last explicitly set by a user-initiated lock acquisition or extension. This value is stored persistently in Redis ([[lockDataKey]]) and is used by the [[Watchdog]] to determine the intended full lease duration for renewal cycles. It is never modified by the watchdog.

**ownerId**
: A unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). This identifies which application instance and thread/process holds the lock. Formerly known as `Lock Owner ID`.

**Owner Supplier**
: Component responsible for generating unique [[ownerId]]s and determining [[Watchdog Eligibility]].

## P

**Pub/Sub (Publish/Subscribe)**
: Redis messaging pattern used for unlock notifications to waiting clients.

**PTTL (Precision Time To Live)**
: Redis command returning remaining TTL in milliseconds.

## R

**Readers-Writer Lock**
: Concurrency pattern allowing multiple concurrent readers or a single exclusive writer.

**Redis Core**
: Framework module providing shared Redis client functionality and configuration.

**Reentrancy**
: Ability for the same owner to acquire a lock multiple times without deadlocking.

**relativeLeaseTimeMillis**
: The duration (in milliseconds) requested by the user for a lock's lease. This is a relative value (e.g., "30 seconds from now") and serves as the input to lock acquisition and extension operations. See also [[originalLeaseTimeMillis]].

**requestUuid**
: A unique identifier (UUID) generated per logical operation (e.g., a single `tryLock` call, including its internal retries). This UUID is used for the [[Idempotency Mechanism]] and [[Idempotency Wrapper]].

**Response Cache**
: Redis-based cache storing results of completed operations for [[Idempotency]].

**Retry Interval**
: Time to wait between lock acquisition attempts when using fallback polling, or between retries for individual Redis operations.

## S

**safetyBufferMillis**
: A calculated duration (`watchdog.interval * watchdog.factor`) used by the [[Watchdog]]. It determines the minimum user-provided lease time for watchdog eligibility and the target TTL the watchdog aims to maintain for eligible locks.

**Stamp**
: Version-based token used in `RedisStampedLock` for optimistic concurrency control.

**State Key**
: Separate Redis key storing state value for `RedisStateLock`.

**Structured Logging**
: Logging approach using structured data fields instead of plain text messages.

## T

**TTL (Time To Live)**
: Duration after which a Redis key automatically expires and is deleted.

## U

**Unlock Channel**
: Redis Pub/Sub channel used for publishing unlock notifications.

**Unlock Message Listener**
: Component subscribing to unlock channels and signaling waiting clients.

**Unlock Type**
: String identifier in Pub/Sub messages indicating the nature of the unlock event.

## V

**Version Control**
: Mechanism in `RedisStampedLock` using version numbers to detect concurrent modifications.

**Virtual Threads**
: Lightweight Java threads designed for high-throughput, I/O-bound operations. They efficiently park when blocking on I/O or `Thread.sleep()`, preventing the underlying platform threads from being blocked. Used for all core lock operations to enhance scalability.

## W

**Watchdog**
: Background service automatically extending lease times for long-running locks to prevent premature expiration. It uses [[safetyBufferMillis]] and preserves the [[originalLeaseTimeMillis]].

**Watchdog Eligibility**
: Conditions determining whether a lock should be monitored by the [[Watchdog]] (owner binding, and [[relativeLeaseTimeMillis]] being greater than [[safetyBufferMillis]]).

## Common Acronyms

- **API**: Application Programming Interface
- **CPU**: Central Processing Unit
- **JSON**: JavaScript Object Notation
- **JVM**: Java Virtual Machine
- **MDC**: Mapped Diagnostic Context
- **POJO**: Plain Old Java Object
- **PTTL**: Precision Time To Live
- **RTT**: Round Trip Time
- **SLF4J**: Simple Logging Facade for Java
- **TTL**: Time To Live
- **UUID**: Universally Unique Identifier
- **YAML**: YAML Ain't Markup Language

## Configuration Properties Prefixes

- `destilink.fw.locking.redis.*`: Main module configuration
- `destilink.fw.locking.redis.watchdog.*`: Watchdog-specific settings
- `destilink.fw.locking.redis.defaults.*`: Global default values for lock instances
- `destilink.fw.redis.core.*`: Shared Redis client configuration

## Key Redis Data Types Used

- **Hash**: Primary storage for lock data (owner, reentrancy count, mode)
- **String**: State values, timeout markers, response cache entries
- **Pub/Sub Channels**: Unlock notifications (not stored data)

This glossary provides quick reference for terminology used throughout the Redis locking module documentation.