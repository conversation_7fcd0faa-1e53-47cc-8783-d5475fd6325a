# Redis Locking Module: Final Modernization Plan & Assessment

## 1. Introduction

This document outlines key modernization tasks and assessment for the `locking-redis-lock` module, aligning with current Destilink Framework best practices, the **Async-First (Virtual Threads)** paradigm, **centralized idempotency**, **refined watchdog**, **lock-type specific keys**, and improved robustness.

## 2. Modernization Assessment Summary (Post-Detailed Plan)

The `detailed-plan.md` (Version 1.0, June 17, 2025) has driven a significant modernization:

*   **Efficient Lock Acquisition**: Notification-based (Pub/Sub) with Virtual Threads. `LockSemaphoreHolder` registered *before* first Redis attempt. Fallback uses `min(TTL, defaults.retryInterval)`.
*   **Atomicity**: Lua scripts for all state/TTL/metadata changes, now including `lockDataKey` management and mandatory idempotency wrappers.
*   **Distributed Reentrancy**: Correctly managed in Redis Hashes.
*   **Comprehensive Exception Handling**: Hierarchy with `ExceptionMarkerProvider`, `RedisLockErrorHandler`, specific exceptions for idempotency, retryable vs. non-retryable distinctions. `RedisLockOperationsImpl` handles retries for individual Redis ops.
*   **Clear Configuration**: `RedisLockProperties` with nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`). Obsolete properties removed.
*   **Detailed Metrics**: Via Micrometer.
*   **`acquireTimeout` Semantics**: Governs overall `tryLock`, non-interrupting of in-flight Redis ops.
*   **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` (same for its internal retries of a logical op). Lua scripts use `requestUuid` and `responseCacheTtl` for response caching.
*   **Refined Watchdog**: Always-active service, conditional monitoring (`userLeaseTime > safetyBufferMillis` & instance-bound). Uses `PEXPIREAT`, preserves `originalLeaseTimeMillis` from `lockDataKey`.
*   **Virtual Threads**: All lock operations, watchdog tasks, and listener tasks execute on Virtual Threads. MDC propagated.
*   **Lock-Type Specific Keys**: Mandatory `lockType` segment in keys for semantic isolation.
*   **Lua-Only Redis Operations**: Strict adherence.

## 3. Key Modernization Tasks (Reflecting `detailed-plan.md` Implementation)

### 3.1. Adopt Virtual Threads and Refine Async Operations
*   **Action**: Integrated Virtual Thread executor. All lock operations, watchdog, and listeners use VTs. MDC propagated. `Thread.sleep()` used for internal delays. `LockSemaphoreHolder` registration order corrected. `acquireTimeout` nuances implemented.

### 3.2. Implement Centralized Idempotency
*   **Action**: `RedisLockOperationsImpl` now generates `requestUuid` per logical operation and passes it with `responseCacheTtl` to Lua scripts. All mutating Lua scripts implement the idempotency wrapper (check cache, execute, store result).

### 3.3. Overhaul Watchdog Mechanism
*   **Action**: `LockWatchdog` is always active. Monitors locks if `userLeaseTime > safetyBufferMillis` (from `watchdog.interval * watchdog.factor`) and instance-bound. Uses `PEXPIREAT`. Manages `expiresAtMillis` while preserving `originalLeaseTimeMillis` (stored in `lockDataKey`). Lua scripts (`watchdog_refresh_lock.lua`, `extend_lock.lua`) updated.

### 3.4. Enforce Lock-Type Specific Keys & `lockDataKey`
*   **Action**: All key construction in Java and Lua now includes a mandatory `lockType` segment. A `lockDataKey` (Redis Hash) is used to store `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`.

### 3.5. Standardize Lua Scripts (Returns, Idempotency, `PEXPIREAT`)
*   **Action**: Lua scripts return structured arrays (`{status, expiresAtMillis, originalLeaseTimeMillis}`). All use `PEXPIREAT`. Mutating scripts are idempotent. All lock state/TTL/metadata changes are Lua-only.

### 3.6. Refine Configuration (`RedisLockProperties`)
*   **Action**: `RedisLockProperties` restructured with nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`). Obsolete properties (`pubSubWaitTimeout`, validation/length limits, `fairLockBehavior`, etc.) removed. `responseCacheTtl` added.

### 3.7. Update Exception Handling & `RedisLockOperationsImpl` Retries
*   **Action**: Exception hierarchy refined. `RedisLockOperationsImpl` now implements internal retries for *individual Redis operations* based on `defaults.maxRetries` and `defaults.retryInterval` for `RetryableLockException` types.

### 3.8. Align All Documentation and Tests
*   **Action**: All 17 markdown files and Javadoc updated to be 100% semantically consistent with `detailed-plan.md`. Test suite updated for new logic, removed features, and Virtual Thread considerations.

## 4. Verification
*   Validated against Destilink Framework guidelines.
*   Thorough testing (unit, integration, concurrency with VTs) performed.