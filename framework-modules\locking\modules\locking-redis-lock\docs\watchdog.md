# Redis Locking Module: Watchdog Mechanism

## 1. Introduction

The `LockWatchdog` is a critical component providing automatic lease extension for distributed locks in the `locking-redis-lock` module. It prevents locks from expiring prematurely while still actively held by an application instance, essential for long-running operations. The watchdog features an **always-active design** with **conditional per-lock monitoring** based on eligibility criteria.

## 2. Core Design Principles

### 2.1 Always-Active Service with Conditional Monitoring

* **Service State**: `LockWatchdog` is always active when the module is enabled (`destilink.fw.locking.redis.enabled=true`)
* **Conditional Monitoring**: Only monitors locks meeting specific eligibility criteria
* **Efficient Resource Usage**: Minimal overhead when no locks require monitoring
* **Immediate Availability**: Ready to monitor locks as soon as they become eligible

### 2.2 Lease Extension and Preservation

* **Lease Extension Pattern**: Implements automatic lease extension for eligible locks
* **Precise TTL Management**: Uses `PEXPIREAT` with Redis server time (`redis.call('TIME')`) for accuracy
* **`originalLeaseTimeMillis` Preservation**: **Never** modifies user's intended lease duration stored in `lockDataKey`
* **User Intent Respect**: Maintains user's original lease intention throughout the lock lifecycle

### 2.3 Resilience and Fault Tolerance

* **Application Crash Handling**: If application instance crashes, watchdog stops renewing and lock expires naturally
* **Explicit Release**: When lock is explicitly released, watchdog immediately stops monitoring
* **Network Resilience**: Handles transient Redis connectivity issues through retry mechanisms
* **Graceful Degradation**: System continues operating even if watchdog encounters issues

## 3. Core Components and Configuration

### 3.1 LockWatchdog Service

* **Spring-Managed Bean**: Singleton bean always instantiated when `destilink.fw.locking.redis.enabled=true`
* **Dependencies**: Uses `RedisLockOperationsImpl` for Lua script execution via `ClusterCommandExecutor`
* **Monitored Locks Registry**: Maintains `ConcurrentHashMap<String, LockInfo>` for active monitoring
* **Thread Safety**: All operations are thread-safe for concurrent access

### 3.2 LockInfo Data Structure

* **`ownerId`**: Unique identifier for the lock holder
* **`userIntendedExpireTimeMillis`**: Absolute Unix timestamp of user's intended expiry (calculated from `originalLeaseTimeMillis`)
* **`currentAbsoluteExpiresAtMillis`**: Current Redis lock expiry timestamp (updated after each successful refresh)

### 3.3 Configuration Properties

**`RedisLockProperties.WatchdogProperties`**:

* **`interval`** (Duration): Frequency of watchdog's scheduled lease extension checks
* **`factor`** (double): Multiplier for `interval` to calculate `safetyBufferMillis`
* **`corePoolSize`**: Thread pool size for watchdog executor
* **`threadNamePrefix`**: Naming prefix for watchdog threads
* **`shutdownAwaitTermination`**: Graceful shutdown timeout

**Calculated Values**:

* **`safetyBufferMillis`**: `interval.toMillis() * factor` - determines monitoring eligibility threshold

### 3.4 Lock Monitoring Eligibility Criteria

A lock is monitored by the watchdog if **all** conditions are met:

1. **Global Enable**: `destilink.fw.locking.redis.enabled` is `true`
2. **Instance-Bound**: `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returns `true`
3. **Sufficient Duration**: Lock's `userProvidedLeaseTime` > `safetyBufferMillis`

## 4. Operational Flow

### 4.1. Lock Registration & Initial TTL

1.  When a lock is successfully acquired by `AbstractRedisLock`:
    *   The `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) is known.
    *   The `originalLeaseTimeMillis` (which is this `userProvidedLeaseTime`) and the initial `expiresAtMillis` are stored in the `lockDataKey` in Redis by the acquisition Lua script.
2.  `AbstractRedisLock` checks watchdog monitoring eligibility:
    *   **If NOT Monitored**: The initial `lockKey` TTL in Redis is set based on the full `userProvidedLeaseTime`.
    *   **If Monitored**: The initial `lockKey` TTL in Redis is set to `safetyBufferMillis` (via `PEXPIREAT currentTime + safetyBufferMillis`).
3.  If monitored, `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, ownerId, userProvidedLeaseTime, initialExpiresAtMillisFromRedis)`.
4.  The `LockWatchdog` stores a `LockInfo` with `ownerId`, `userIntendedExpireTimeMillis = initialExpiresAtMillisFromRedis - safetyBufferMillis + userProvidedLeaseTime` (recalculating the user's intended absolute expiry based on the full lease), and `currentAbsoluteExpiresAtMillis = initialExpiresAtMillisFromRedis`.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `extendLocks()` method is scheduled to run every `watchdog.interval`.
2.  It iterates through `monitoredLocks`. For each `LockInfo`:
    *   Calculates `remainingUserTimeMillis = lockInfo.getUserIntendedExpireTimeMillis() - System.currentTimeMillis()`.
    *   **If `remainingUserTimeMillis <= 0` (User's intended lease expired):**
        *   Unregister the lock from `monitoredLocks`.
        *   Log a warning if the lock still appears to be held by this owner in Redis (this indicates the user might have expected it to live longer but didn't extend it, or a final refresh failed).
    *   **Else (User's intended lease has not expired):**
        *   Determine `targetExpiresAtMillis` for the refresh:
            *   **Final Leg (`0 < remainingUserTimeMillis <= safetyBufferMillis`):** `targetExpiresAtMillis = lockInfo.getUserIntendedExpireTimeMillis()`.
            *   **Standard Operation (`remainingUserTimeMillis > safetyBufferMillis`):** `targetExpiresAtMillis = System.currentTimeMillis() + safetyBufferMillis`.
        *   Execute `watchdog_refresh_lock.lua` via `RedisLockOperations` (which handles its own retries for this Redis command).
            *   **Lua Script (`watchdog_refresh_lock.lua`)**:
                *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `targetExpiresAtMillis`, `requestUuid`, `responseCacheTtl`.
                *   Implements idempotency wrapper.
                *   Atomically checks if `lockKey` in Redis is still held by `expectedOwnerId`.
                *   If owner matches:
                    *   Updates `expiresAtMillis` in `lockDataKey` to `targetExpiresAtMillis`.
                    *   Sets `PEXPIREAT` on `lockKey` to `targetExpiresAtMillis`.
                    *   Returns success status, new `expiresAtMillis` from Redis, and `originalLeaseTimeMillis` (read from `lockDataKey`, unchanged).
                *   If owner mismatch or lock not found, returns failure.
        *   **Result Handling**:
            *   **Success**: Update `lockInfo.setCurrentAbsoluteExpiresAtMillis()` with the new `expiresAtMillis` returned from the script. If it was the "Final Leg" refresh, unregister the lock from `monitoredLocks`.
            *   **Failure (owner mismatch, lock gone, or Redis error after retries)**: Remove `lockKey` from `monitoredLocks`. Log a WARNING.

### 4.3. Lock Unregistration (User Initiated)

1.  When `AbstractRedisLock.unlock()` is called.
2.  `unlock()` calls `lockWatchdog.unregisterLock(lockKey, ownerId)`.
3.  The `LockWatchdog` removes the entry from `monitoredLocks`.

### 4.4. User-Initiated Lock Extension (`AbstractRedisLock.extendLeaseAsync`)

1.  User calls `extendLeaseAsync(newRelativeLeaseTime)`.
2.  `AbstractRedisLock` calls `RedisLockOperations.extendLeaseAsync(...)` which executes `extend_lock.lua`.
    *   **Lua Script (`extend_lock.lua`)**:
        *   Takes `lockKey`, `lockDataKey`, `expectedOwnerId`, `newRelativeLeaseTime`, `requestUuid`, `responseCacheTtl`.
        *   Implements idempotency wrapper.
        *   Atomically checks ownership.
        *   If owner matches:
            *   Updates `originalLeaseTimeMillis` in `lockDataKey` to `newRelativeLeaseTime`.
            *   Calculates `newExpiresAtMillis` based on `currentTime + newRelativeLeaseTime`.
            *   Updates `expiresAtMillis` in `lockDataKey` to this `newExpiresAtMillis`.
            *   Sets `PEXPIREAT` on `lockKey` to this `newExpiresAtMillis`.
            *   Returns success, `newExpiresAtMillis`, and the updated `originalLeaseTimeMillis`.
3.  `AbstractRedisLock` then calls `lockWatchdog.updateRegistration(lockKey, ownerId, newOriginalLeaseTimeMillisFromScript, newExpiresAtMillisFromScript)`.
    *   The watchdog re-evaluates eligibility. If still monitored, it updates its `LockInfo` with the new `userIntendedExpireTimeMillis` (derived from `newOriginalLeaseTimeMillisFromScript`) and `currentAbsoluteExpiresAtMillis`. If the new lease makes it no longer eligible (e.g., new lease is too short), it unregisters it.

## 5. Key Considerations

*   **Atomicity of Lua Scripts**: `watchdog_refresh_lock.lua` and `extend_lock.lua` are critical for atomic check-and-set operations.
*   **Idempotency**: All watchdog-related Lua scripts must be idempotent.
*   **Configuration Alignment**: `watchdog.interval` and `watchdog.factor` (determining `safetyBufferMillis`) must be sensible.
*   **Clock Skew**: Using Redis `TIME` in Lua and `PEXPIREAT` helps manage consistency. The `safetyBufferMillis` provides a buffer.