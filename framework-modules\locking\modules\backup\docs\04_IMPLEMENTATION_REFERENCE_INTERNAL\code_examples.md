valueOperations).setIfAbsent(eq("testLock"), anyString(), eq(Duration.ofSeconds(10)));
    }

    @Test
    void releaseLock_Success() {
        when(redisTemplate.delete(anyString())).thenReturn(true);

        redisLockService.releaseLock("testLock");

        verify(redisTemplate).delete("testLock");
    }
}
```

Integration test example:

```java
package com.tui.destilink.framework.locking.redis.service.impl;

import com.tui.destilink.framework.locking.redis.config.RedisLockAutoConfiguration;
import com.tui.destilink.framework.locking.redis.service.RedisLockService;
import com.tui.destilink.framework.test.redis.annotation.EnableRedisTestSupport;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = RedisLockAutoConfiguration.class)
@EnableRedisTestSupport
@ActiveProfiles("test")
class RedisLockServiceIT {

    @Autowired
    private RedisLockService redisLockService;

    @Test
    void acquireAndReleaseLock() {
        String lockKey = "testLock";
        Duration lockDuration = Duration.ofSeconds(10);

        boolean acquired = redisLockService.acquireLock(lockKey, lockDuration);
        assertTrue(acquired, "Should acquire the lock");

        boolean acquiredAgain = redisLockService.acquireLock(lockKey, lockDuration);
        assertFalse(acquiredAgain, "Should not acquire the lock again");

        redisLockService.releaseLock(lockKey);

        boolean acquiredAfterRelease = redisLockService.acquireLock(lockKey, lockDuration);
        assertTrue(acquiredAfterRelease, "Should acquire the lock after release");
    }
}
```

## 12. Troubleshooting Guide

Here's a troubleshooting guide for common issues with Redis locks:

### 1. Lock Acquisition Failures

**Problem**: Unable to acquire locks consistently.

**Possible Causes**:
- High contention on the lock key
- Network issues between application and Redis
- Redis server overload

**Diagnostic Steps**:
1. Check Redis server logs for any errors or warnings
2. Monitor Redis server metrics (CPU, memory, network)
3. Analyze lock acquisition patterns in application logs

**Resolution**:
- Implement exponential backoff for lock retries
- Increase Redis server resources if overloaded
- Optimize lock granularity to reduce contention

Example of implementing exponential backoff:

```java
@Slf4j
@RequiredArgsConstructor
public class BackoffRedisLockServiceImpl implements RedisLockService {

    private final RedisTemplate<String, String> redisTemplate;
    private final int maxRetries;
    private final long initialBackoffMillis;

    @Override
    public boolean acquireLock(String lockKey, Duration lockDuration) {
        int retries = 0;
        long backoffMillis = initialBackoffMillis;

        while (retries < maxRetries) {
            Boolean acquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", lockDuration);
            if (Boolean.TRUE.equals(acquired)) {
                return true;
            }

            try {
                Thread.sleep(backoffMillis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RedisLockException("Interrupted while waiting to acquire lock", e);
            }

            retries++;
            backoffMillis *= 2; // Exponential backoff
        }

        log.warn("Failed to acquire lock after {} retries: {}", maxRetries, lockKey);
        return false;
    }

    // ... other methods
}
```

### 2. Unexpected Lock Releases

**Problem**: Locks are being released unexpectedly.

**Possible Causes**:
- Incorrect lock timeout configuration
- Network partitions causing watchdog failures
- Bugs in lock release logic

**Diagnostic Steps**:
1. Review lock timeout settings in configuration
2. Check application logs for watchdog failures
3. Analyze Redis key events for unexpected expirations

**Resolution**:
- Adjust lock timeout and watchdog interval settings
- Implement more robust error handling in watchdog mechanism
- Use Redis transactions for atomic lock operations

Example of using Redis transactions for atomic lock operations:

```java
@Slf4j
@RequiredArgsConstructor
public class TransactionalRedisLockServiceImpl implements RedisLockService {

    private final StringRedisTemplate redisTemplate;

    @Override
    public boolean acquireLock(String lockKey, Duration lockDuration) {
        return redisTemplate.execute(new SessionCallback<Boolean>() {
            @Override
            public Boolean execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                operations.opsForValue().setIfAbsent(lockKey, "locked", lockDuration);
                List<Object> result = operations.exec();
                return !result.isEmpty() && (Boolean) result.get(0);
            }
        });
    }

    @Override
    public void releaseLock(String lockKey) {
        redisTemplate.execute(new SessionCallback<Void>() {
            @Override
            public Void execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                operations.delete(lockKey);
                operations.exec();
                return null;
            }
        });
    }
}
```

### 3. Performance Degradation

**Problem**: Lock operations are becoming slow over time.

**Possible Causes**:
- Redis server resource constraints
- Network latency issues
- Inefficient lock implementation

**Diagnostic Steps**:
1. Monitor Redis server performance metrics
2. Analyze network latency between application and Redis
3. Profile lock acquisition and release operations

**Resolution**:
- Optimize Redis server configuration
- Implement connection pooling for Redis clients
- Use pipelining for batch lock operations

Example of using pipelining for batch lock operations:

```java
@Slf4j
@RequiredArgsConstructor
public class PipelinedRedisLockServiceImpl implements RedisLockService {

    private final StringRedisTemplate redisTemplate;

    public Map<String, Boolean> acquireBatchLocks(List<String> lockKeys, Duration lockDuration) {
        return redisTemplate.execute(new SessionCallback<Map<String, Boolean>>() {
            @Override
            public Map<String, Boolean> execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                for (String lockKey : lockKeys) {
                    operations.opsForValue().setIfAbsent(lockKey, "locked", lockDuration);
                }
                List<Object> results = operations.exec();
                
                Map<String, Boolean> lockResults = new HashMap<>();
                for (int i = 0; i < lockKeys.size(); i++) {
                    lockResults.put(lockKeys.get(i), (Boolean) results.get(i));
                }
                return lockResults;
            }
        });
    }

    // ... other methods
}
```

### 4. Deadlocks

**Problem**: Application threads are deadlocking when using multiple locks.

**Possible Causes**:
- Incorrect lock acquisition order
- Failure to release locks in finally blocks
- Nested lock acquisitions

**Diagnostic Steps**:
1. Review code for lock acquisition patterns
2. Analyze thread dumps during deadlock situations
3. Use distributed tracing to identify lock acquisition chains

**Resolution**:
- Implement a consistent lock acquisition order
- Use try-with-resources for automatic lock release
- Avoid nested lock acquisitions where possible

Example of using try-with-resources for automatic lock release:

```java
@Slf4j
@RequiredArgsConstructor
public class AutoCloseableRedisLockServiceImpl implements RedisLockService {

    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean acquireLock(String lockKey, Duration lockDuration) {
        Boolean acquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", lockDuration);
        return Boolean.TRUE.equals(acquired);
    }

    @Override
    public void releaseLock(String lockKey) {
        redisTemplate.delete(lockKey);
    }

    public AutoCloseableLock acquireAutoCloseableLock(String lockKey, Duration lockDuration) {
        if (acquireLock(lockKey, lockDuration)) {
            return new AutoCloseableLock(this, lockKey);
        }
        throw new RedisLockException("Failed to acquire lock: " + lockKey);
    }

    public static class AutoCloseableLock implements AutoCloseable {
        private final RedisLockService lockService;
        private final String lockKey;

        AutoCloseableLock(RedisLockService lockService, String lockKey) {
            this.lockService = lockService;
            this.lockKey = lockKey;
        }

        @Override
        public void close() {
            lockService.releaseLock(lockKey);
        }
    }
}
```

Usage example:

```java
try (AutoCloseableRedisLockServiceImpl.AutoCloseableLock lock = 
        redisLockService.acquireAutoCloseableLock("resourceA", Duration.ofSeconds(30))) {
    // Protected resource access
} // Lock is automatically released here
```

By following this troubleshooting guide and implementing the suggested solutions, you can address common issues with Redis locks in the Destilink Framework. Remember to always monitor your Redis server and application logs for early detection of potential problems.

This completes the code examples documentation for the Redis Lock Implementation in the Destilink Framework. The document now covers all required sections, providing comprehensive examples and guidelines for using and troubleshooting Redis locks within the framework.