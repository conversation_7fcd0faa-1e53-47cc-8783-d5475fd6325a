# Redis Locking Module: Troubleshooting Guide

## 1. Introduction

This guide provides systematic troubleshooting procedures for common issues encountered with the Redis locking module. It includes diagnostic steps, root cause analysis, and resolution strategies for production environments.

For monitoring and metrics, see [Monitoring and Metrics](monitoring_and_metrics.md). For performance optimization, see [Performance Tuning](performance_tuning.md).

## 2. Common Issues and Solutions

### 2.1. Lock Acquisition Failures

#### Symptoms
- High lock acquisition failure rates
- Timeouts during lock acquisition
- Applications unable to proceed with critical operations

#### Diagnostic Steps

**Step 1: Check Redis Connectivity**
```bash
# Test Redis connection
redis-cli ping
# Expected: PONG

# Check Redis server status
redis-cli info server
```

**Step 2: Analyze Lock Metrics**
```bash
# Check lock acquisition metrics
curl -s http://localhost:8080/actuator/metrics/redis.lock.acquisition.attempts.total

# Check lock acquisition duration
curl -s http://localhost:8080/actuator/metrics/redis.lock.acquisition.duration
```

**Step 3: Examine Application Logs**
```bash
# Look for lock-related errors
grep -i "lock.*error\|lock.*failed" application.log

# Check for timeout patterns
grep -i "timeout.*lock\|lock.*timeout" application.log
```

#### Root Causes and Solutions

**Cause 1: Redis Server Overload**
- **Symptoms**: High Redis CPU/memory usage, slow response times
- **Solution**: 
  ```yaml
  # Increase Redis resources
  # Scale Redis instance or cluster
  # Optimize Redis configuration
  redis:
    maxmemory: 2gb
    maxmemory-policy: allkeys-lru
  ```

**Cause 2: Network Issues**
- **Symptoms**: Intermittent connection failures, high latency
- **Solution**:
  ```yaml
  # Adjust connection timeouts
  spring:
    redis:
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
  ```

**Cause 3: Lock Contention**
- **Symptoms**: Many applications competing for same locks
- **Solution**:
  ```yaml
  # Increase retry attempts and intervals
  destilink:
    fw:
      locking:
        redis:
          defaults:
            max-retries: 5
            retry-interval: 200ms
  ```

**Cause 4: Configuration Issues**
- **Symptoms**: Locks timing out too quickly
- **Solution**:
  ```yaml
  # Adjust timeout settings
  destilink:
    fw:
      locking:
        redis:
          defaults:
            acquire-timeout: 10s
            redis-operation-timeout: 1s
  ```

### 2.2. Watchdog Issues

#### Symptoms
- Locks expiring unexpectedly
- Watchdog renewal failures
- Long-running operations interrupted

#### Diagnostic Steps

**Step 1: Check Watchdog Status**
```bash
# Check watchdog health
curl -s http://localhost:8080/actuator/health/redisLock | jq '.details.watchdogActive'

# Check monitored locks count
curl -s http://localhost:8080/actuator/metrics/redis.lock.watchdog.monitored.locks
```

**Step 2: Analyze Watchdog Logs**
```bash
# Look for watchdog-related logs
grep -i "watchdog" application.log | tail -50

# Check for renewal failures
grep -i "renewal.*failed\|failed.*renewal" application.log
```

**Step 3: Check Thread Pool Health**
```bash
# Check thread pool metrics (if available)
curl -s http://localhost:8080/actuator/metrics/executor.active | grep watchdog
```

#### Root Causes and Solutions

**Cause 1: Watchdog Thread Pool Exhaustion**
- **Symptoms**: No renewal activity, thread pool full
- **Solution**:
  ```yaml
  destilink:
    fw:
      locking:
        redis:
          watchdog:
            core-pool-size: 5
            interval: 3s
  ```

**Cause 2: Redis Operation Timeouts**
- **Symptoms**: Renewal operations timing out
- **Solution**:
  ```yaml
  destilink:
    fw:
      locking:
        redis:
          watchdog:
            redis-operation-timeout: 2s
  ```

**Cause 3: Application Resource Constraints**
- **Symptoms**: High CPU/memory usage, GC pressure
- **Solution**: Scale application resources or optimize code

**Cause 4: Watchdog Configuration Issues**
- **Symptoms**: Renewals happening too frequently or infrequently
- **Solution**:
  ```yaml
  destilink:
    fw:
      locking:
        redis:
          watchdog:
            interval: 5s
            min-lease-time-for-activation: 15s
            max-ttl-for-renewal-check: 20s
  ```

### 2.3. Lock Leaks

#### Symptoms
- Locks not being released
- Increasing number of active locks over time
- Applications hanging indefinitely

#### Diagnostic Steps

**Step 1: Monitor Active Locks**
```bash
# Check active locks count over time
curl -s http://localhost:8080/actuator/metrics/redis.lock.active.locks

# List all lock keys in Redis
redis-cli --scan --pattern "dl:lock:*"
```

**Step 2: Analyze Lock Hold Duration**
```bash
# Check lock hold duration metrics
curl -s http://localhost:8080/actuator/metrics/redis.lock.hold.duration

# Look for locks held too long
redis-cli eval "
local keys = redis.call('keys', 'dl:lock:*')
for i=1,#keys do
  local ttl = redis.call('ttl', keys[i])
  if ttl > 300 then
    redis.call('echo', keys[i] .. ' TTL: ' .. ttl)
  end
end
" 0
```

**Step 3: Check Application Code**
```bash
# Look for missing try-finally blocks
grep -r "lock\.acquire" src/ | grep -v "try\|finally"

# Check for exception handling around locks
grep -A 10 -B 5 "lock\.acquire" src/ | grep -C 5 "catch\|exception"
```

#### Root Causes and Solutions

**Cause 1: Missing Finally Blocks**
- **Symptoms**: Locks not released on exceptions
- **Solution**:
  ```java
  // Correct pattern
  var lock = lockBucket.lockConfig("my-lock").reentrantLock().build();
  if (lock.tryAcquire()) {
      try {
          // Critical section
      } finally {
          lock.release();
      }
  }
  ```

**Cause 2: Application Crashes**
- **Symptoms**: Locks remain after application restart
- **Solution**: Use appropriate lease times and enable watchdog
  ```yaml
  destilink:
    fw:
      locking:
        redis:
          defaults:
            lease-time: 60s  # Reasonable lease time
          watchdog:
            enabled: true
  ```

**Cause 3: Long-Running Operations**
- **Symptoms**: Operations taking longer than lease time
- **Solution**: Use longer lease times or break down operations
  ```java
  var lock = lockBucket.lockConfig("long-operation")
      .reentrantLock()
      .withLeaseTime(Duration.ofMinutes(10))  // Longer lease
      .build();
  ```

### 2.4. Performance Issues

#### Symptoms
- High lock acquisition latency
- Slow Redis operations
- Application performance degradation

#### Diagnostic Steps

**Step 1: Check Redis Performance**
```bash
# Monitor Redis operations
redis-cli monitor | grep -E "SET|GET|DEL|EVAL"

# Check Redis slow log
redis-cli slowlog get 10
```

**Step 2: Analyze Lock Metrics**
```bash
# Check acquisition latency percentiles
curl -s http://localhost:8080/actuator/metrics/redis.lock.acquisition.duration

# Check Redis operation latency
curl -s http://localhost:8080/actuator/metrics/redis.lock.redis.operation.duration
```

**Step 3: Profile Application**
```bash
# Use profiling tools to identify bottlenecks
# Check for blocking operations in critical sections
```

#### Root Causes and Solutions

**Cause 1: Inefficient Lua Scripts**
- **Symptoms**: High script execution times
- **Solution**: Review and optimize Lua scripts (see [Lua Scripts](../04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md))

**Cause 2: Large Lock Contention**
- **Symptoms**: Many threads waiting for same lock
- **Solution**: 
  - Reduce critical section size
  - Use more granular locking
  - Implement lock-free algorithms where possible

**Cause 3: Redis Memory Issues**
- **Symptoms**: High memory usage, evictions
- **Solution**:
  ```yaml
  # Configure appropriate TTLs
  destilink:
    fw:
      locking:
        redis:
          state-key-expiration: 2m
          uuid-cache-ttl-seconds: 120
  ```

### 2.5. Configuration Issues

#### Symptoms
- Module not starting
- Unexpected behavior
- Missing functionality

#### Diagnostic Steps

**Step 1: Check Configuration**
```bash
# Verify configuration properties
curl -s http://localhost:8080/actuator/configprops | jq '.destilink.fw.locking.redis'

# Check auto-configuration
curl -s http://localhost:8080/actuator/conditions | jq '.contexts.application.positiveMatches' | grep -i redis
```

**Step 2: Check Dependencies**
```bash
# Verify Redis dependencies
mvn dependency:tree | grep -i redis

# Check for version conflicts
mvn dependency:tree -Dverbose | grep -i redis
```

#### Root Causes and Solutions

**Cause 1: Missing Dependencies**
- **Symptoms**: ClassNotFoundException, NoClassDefFoundError
- **Solution**: Add required dependencies
  ```xml
  <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
  </dependency>
  ```

**Cause 2: Configuration Property Errors**
- **Symptoms**: Module not enabled, wrong behavior
- **Solution**: Verify configuration syntax
  ```yaml
  destilink:
    fw:
      locking:
        redis:
          enabled: true  # Ensure boolean, not string
          defaults:
            lease-time: PT60S  # Use ISO-8601 duration format
  ```

**Cause 3: Bean Configuration Issues**
- **Symptoms**: NoSuchBeanDefinitionException
- **Solution**: Check conditional annotations and bean definitions

## 3. Diagnostic Tools and Commands

### 3.1. Redis Diagnostic Commands

```bash
# Check Redis server info
redis-cli info all

# Monitor Redis operations in real-time
redis-cli monitor

# Check memory usage
redis-cli info memory

# List all keys with pattern
redis-cli --scan --pattern "dl:lock:*"

# Check key TTL
redis-cli ttl "dl:lock:bucket:key"

# Get key value
redis-cli get "dl:lock:bucket:key"

# Check pub/sub channels
redis-cli pubsub channels "dl:unlock:*"
```

### 3.2. Application Diagnostic Endpoints

```bash
# Health check
curl http://localhost:8080/actuator/health/redisLock

# Metrics
curl http://localhost:8080/actuator/metrics/redis.lock.acquisition.attempts.total

# Configuration properties
curl http://localhost:8080/actuator/configprops | jq '.destilink.fw.locking.redis'

# Auto-configuration report
curl http://localhost:8080/actuator/conditions | grep -i redis

# Thread dump (for watchdog issues)
curl http://localhost:8080/actuator/threaddump | jq '.threads[] | select(.threadName | contains("watchdog"))'
```

### 3.3. Log Analysis Commands

```bash
# Find lock-related errors
grep -i "lock.*error\|error.*lock" application.log

# Analyze lock acquisition patterns
grep "Lock acquired\|Lock released" application.log | awk '{print $1, $2}' | sort | uniq -c

# Check watchdog activity
grep -i "watchdog" application.log | tail -20

# Find timeout issues
grep -i "timeout" application.log | grep -i "lock"

# Analyze lock contention
grep "Lock acquisition failed" application.log | awk '{print $NF}' | sort | uniq -c | sort -nr
```

## 4. Emergency Procedures

### 4.1. Emergency Lock Release

**When to Use**: When locks are stuck and preventing critical operations

**Procedure**:
```bash
# 1. Identify stuck locks
redis-cli --scan --pattern "dl:lock:*" | xargs -I {} redis-cli ttl {}

# 2. Force release specific lock (DANGEROUS - use with caution)
redis-cli del "dl:lock:bucket:specific-key"

# 3. Clear all locks for a bucket (VERY DANGEROUS)
redis-cli --scan --pattern "dl:lock:bucket:*" | xargs redis-cli del

# 4. Restart application to reset state
kubectl rollout restart deployment/my-app
```

**⚠️ Warning**: Only use emergency procedures when absolutely necessary and you understand the consequences.

### 4.2. Watchdog Emergency Stop

**When to Use**: When watchdog is causing issues and needs to be stopped immediately

**Procedure**:
```yaml
# 1. Disable watchdog via configuration
destilink:
  fw:
    locking:
      redis:
        watchdog:
          enabled: false

# 2. Restart application
# 3. Monitor for lock expiration issues
```

### 4.3. Redis Connection Issues

**When to Use**: When Redis is unreachable or experiencing issues

**Procedure**:
```bash
# 1. Check Redis server status
redis-cli ping

# 2. Check network connectivity
telnet redis-host 6379

# 3. Review Redis logs
tail -f /var/log/redis/redis-server.log

# 4. Restart Redis if necessary (coordinate with team)
sudo systemctl restart redis-server

# 5. Monitor application recovery
curl http://localhost:8080/actuator/health/redisLock
```

## 5. Prevention Strategies

### 5.1. Code Review Checklist

- [ ] All lock acquisitions are in try-finally blocks
- [ ] Appropriate lease times are configured
- [ ] Lock scope is correctly set (APPLICATION_INSTANCE vs GLOBAL)
- [ ] Critical sections are minimized
- [ ] Error handling includes lock cleanup
- [ ] Timeouts are reasonable for the operation
- [ ] Lock keys are properly scoped to avoid conflicts

### 5.2. Monitoring Setup

- [ ] Set up alerts for high lock failure rates
- [ ] Monitor lock acquisition latency
- [ ] Track watchdog health and renewal success
- [ ] Monitor Redis connection health
- [ ] Set up dashboards for lock metrics
- [ ] Configure log aggregation for lock events

### 5.3. Testing Strategies

```java
@Test
public void testLockUnderHighContention() {
    // Test lock behavior under high contention
    int threadCount = 10;
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                var lock = lockBucket.lockConfig("contention-test")
                    .reentrantLock()
                    .withAcquireTimeout(Duration.ofSeconds(5))
                    .build();
                    
                if (lock.tryAcquire()) {
                    try {
                        successCount.incrementAndGet();
                        Thread.sleep(100); // Simulate work
                    } finally {
                        lock.release();
                    }
                }
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await();
    assertThat(successCount.get()).isEqualTo(threadCount);
}

@Test
public void testLockWithRedisFailure() {
    // Test behavior when Redis is unavailable
    // Stop Redis container
    // Attempt lock operations
    // Verify graceful degradation
}
```

## 6. Escalation Procedures

### 6.1. When to Escalate

- Redis server is down and cannot be restarted
- Data corruption suspected in Redis
- Performance issues persist after following troubleshooting steps
- Security concerns related to lock implementation
- Multiple applications affected by lock issues

### 6.2. Information to Gather

Before escalating, collect:

1. **Application Information**:
   - Application version and configuration
   - Lock module version
   - Deployment environment details

2. **Error Information**:
   - Complete error messages and stack traces
   - Relevant log excerpts with timestamps
   - Metrics and monitoring data

3. **Environment Information**:
   - Redis version and configuration
   - Network topology
   - Resource utilization data

4. **Reproduction Information**:
   - Steps to reproduce the issue
   - Frequency and timing of the issue
   - Impact on business operations

### 6.3. Escalation Contacts

- **Level 1**: Development team lead
- **Level 2**: Platform/Infrastructure team
- **Level 3**: Redis/Database specialists
- **Level 4**: Vendor support (if applicable)

## 7. Post-Incident Analysis

### 7.1. Root Cause Analysis Template

```markdown
# Lock Module Incident Analysis

## Incident Summary
- **Date/Time**: 
- **Duration**: 
- **Impact**: 
- **Affected Services**: 

## Timeline
- **Detection**: 
- **Investigation Started**: 
- **Root Cause Identified**: 
- **Resolution Applied**: 
- **Service Restored**: 

## Root Cause
- **Primary Cause**: 
- **Contributing Factors**: 
- **Why it wasn't detected earlier**: 

## Resolution
- **Immediate Actions**: 
- **Permanent Fix**: 
- **Verification**: 

## Prevention
- **Monitoring Improvements**: 
- **Code Changes**: 
- **Process Changes**: 
- **Documentation Updates**: 

## Lessons Learned
- **What worked well**: 
- **What could be improved**: 
- **Action items**: 
```

### 7.2. Follow-up Actions

1. **Update Documentation**: Reflect lessons learned in troubleshooting guides
2. **Improve Monitoring**: Add alerts for newly discovered failure modes
3. **Code Improvements**: Implement additional safeguards
4. **Training**: Share knowledge with team members
5. **Testing**: Add tests for the failure scenario

This troubleshooting guide provides a systematic approach to diagnosing and resolving Redis locking module issues. Regular review and updates based on operational experience will improve its effectiveness over time.