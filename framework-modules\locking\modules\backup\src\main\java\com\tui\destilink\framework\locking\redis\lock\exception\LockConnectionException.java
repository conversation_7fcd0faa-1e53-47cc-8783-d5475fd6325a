package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when there is a connection issue with Redis.
 * <p>
 * This exception is thrown when a Redis connection cannot be established
 * or when an existing connection fails during lock operations.
 * </p>
 */
public class LockConnectionException extends AbstractRedisLockException {

    private final String connectionError;

    /**
     * Constructs a new LockConnectionException with the specified details.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be
     *                        null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param connectionError Description of the connection error
     * @param message         Descriptive error message
     */
    public LockConnectionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String connectionError, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.connectionError = connectionError;
    }

    /**
     * Constructs a new LockConnectionException with the specified details and
     * cause.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be
     *                        null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be
     *                        null)
     * @param connectionError Description of the connection error
     * @param message         Descriptive error message
     * @param cause           The underlying cause of this exception
     */
    public LockConnectionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String connectionError, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.connectionError = connectionError;
    }

    /**
     * Gets the description of the connection error.
     *
     * @return The connection error description
     */
    public String getConnectionError() {
        return connectionError;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (connectionError != null) {
            contextMap.put("redis.connection.error", connectionError);
        }
    }
}