package com.tui.destilink.framework.locking.redis.lock.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Holds a semaphore for a specific lock key, allowing threads to wait for
 * unlock notifications.
 * <p>
 * This class is not Spring-managed and is created per lock name by the
 * {@link UnlockMessageListener}.
 * It encapsulates a {@link Semaphore} (initialized with 0 permits and fair
 * mode) and tracks the
 * number of waiting threads for its specific lock key.
 * </p>
 * <p>
 * The semaphore is used to implement non-polling wait for lock acquisition.
 * When a thread
 * attempts to acquire a lock and finds it held, it registers its interest in
 * receiving an
 * unlock notification via this holder, then waits on the semaphore with a
 * timeout.
 * </p>
 * <p>
 * When an unlock event is received via Redis Pub/Sub, the
 * {@link UnlockMessageListener} calls
 * the {@link #signal()} method to release a permit, waking up one or more
 * waiting threads.
 * </p>
 */
public class LockSemaphoreHolder {

    private static final Logger log = LoggerFactory.getLogger(LockSemaphoreHolder.class);

    /**
     * The semaphore used for signaling unlock events.
     * <p>
     * Initialized with 0 permits and fair mode to ensure threads are awakened in
     * FIFO order.
     * </p>
     */
    private final Semaphore semaphore;

    /**
     * Counter tracking the number of threads currently waiting on this semaphore.
     * <p>
     * This helps the {@link UnlockMessageListener} make intelligent decisions about
     * how many permits to release based on the unlock type and number of waiters.
     * </p>
     */
    private final AtomicInteger waitersCount;

    /**
     * Creates a new LockSemaphoreHolder with a fair semaphore and 0 initial
     * permits.
     */
    public LockSemaphoreHolder() {
        // Fair mode ensures threads are awakened in FIFO order
        this.semaphore = new Semaphore(0, true);
        this.waitersCount = new AtomicInteger(0);
    }

    /**
     * Waits for an unlock notification with the specified timeout.
     * <p>
     * This method first drains any stale permits from the semaphore to prevent a
     * thread
     * from immediately acquiring a permit that was released for a previous,
     * now-timed-out waiter.
     * It then increments the waiters count, attempts to acquire a permit with the
     * specified timeout,
     * and finally decrements the waiters count when done waiting.
     * </p>
     *
     * @param timeout the maximum time to wait
     * @param unit    the time unit of the timeout argument
     * @return {@code true} if a permit was acquired before the timeout,
     *         {@code false} otherwise
     * @throws InterruptedException if the current thread is interrupted while
     *                              waiting
     */
    public boolean waitForUnlock(long timeout, TimeUnit unit) throws InterruptedException {
        // Drain any stale permits that might have been released for previous waiters
        semaphore.drainPermits();

        // Increment waiters count before waiting
        waitersCount.incrementAndGet();

        try {
            // Wait for a permit with the specified timeout
            return semaphore.tryAcquire(timeout, unit);
        } finally {
            // Decrement waiters count after waiting (regardless of result)
            waitersCount.decrementAndGet();
        }
    }

    /**
     * Signals waiting threads by releasing a permit on the semaphore.
     * <p>
     * This method is called by the {@link UnlockMessageListener} when an unlock
     * event
     * is received via Redis Pub/Sub.
     * </p>
     */
    public void signal() {
        semaphore.release();
        log.trace("Released permit on semaphore, available permits: {}", semaphore.availablePermits());
    }

    /**
     * Signals multiple waiting threads by releasing the specified number of
     * permits.
     * <p>
     * This method is called by the {@link UnlockMessageListener} when an unlock
     * event
     * that should wake multiple waiters (e.g., for read locks) is received.
     * </p>
     *
     * @param permits the number of permits to release
     */
    public void signal(int permits) {
        semaphore.release(permits);
        log.trace("Released {} permits on semaphore, available permits: {}",
                permits, semaphore.availablePermits());
    }

    /**
     * Gets the current number of threads waiting on this semaphore.
     *
     * @return the number of waiting threads
     */
    public int getWaitersCount() {
        return waitersCount.get();
    }
}