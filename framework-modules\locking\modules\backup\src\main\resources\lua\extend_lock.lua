-- KEYS[1] lockName
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] newLeaseTimeMs
-- ARGV[3] lockOwnerId

-- Helper function to cache response and return result
local function cacheAndReturn(result)
    redis.call('set', KEYS[2], result, 'px', 300); -- Cache result for 300ms
    return result;
end

-- Check if we have a cached response for this request
local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    return tonumber(cachedResult);
end;

-- Check if the lock exists
local lockExists = redis.call('exists', KEYS[1]);
if lockExists == 0 then
    return cacheAndReturn(0); -- Lock does not exist
end;

-- Check if this is a write lock
local writeOwnerId = redis.call('hget', KEYS[1], 'write_owner_id');
if writeOwnerId ~= false then
    -- This is a write lock, check if the caller is the owner
    if writeOwnerId ~= ARGV[3] then
        return cacheAndReturn(0); -- Not owned by caller
    end
    
    -- Extend the lock lease
    redis.call('pexpire', KEYS[1], ARGV[2]);
    return cacheAndReturn(1); -- Success
end;

-- Check if this is a read lock
local readHolders = redis.call('hget', KEYS[1], 'read_holders');
if readHolders ~= false then
    -- This is a read lock, check if the caller is one of the holders
    if not string.find(readHolders, ARGV[3]) then
        return cacheAndReturn(0); -- Not owned by caller
    end
    
    -- Extend the lock lease
    redis.call('pexpire', KEYS[1], ARGV[2]);
    return cacheAndReturn(1); -- Success
end;

-- If we get here, the lock exists but doesn't have the expected structure
return cacheAndReturn(0); -- Cannot extend lock with unknown structure