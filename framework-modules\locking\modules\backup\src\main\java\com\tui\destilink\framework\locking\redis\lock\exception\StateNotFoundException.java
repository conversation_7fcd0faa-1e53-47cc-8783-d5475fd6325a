package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when a state value is not found in Redis.
 * <p>
 * This exception is specific to StateLock operations and indicates that
 * a requested state key does not exist in Redis.
 * </p>
 */
public class StateNotFoundException extends AbstractRedisLockException {

    private final String stateKey;

    /**
     * Constructs a new StateNotFoundException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param stateKey    The key identifying the state that was not found
     * @param message     Descriptive error message
     */
    public StateNotFoundException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.stateKey = stateKey;
    }

    /**
     * Constructs a new StateNotFoundException with the specified details and cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param stateKey    The key identifying the state that was not found
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public StateNotFoundException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String stateKey, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.stateKey = stateKey;
    }

    /**
     * Gets the key identifying the state that was not found.
     *
     * @return The state key
     */
    public String getStateKey() {
        return stateKey;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (stateKey != null) {
            contextMap.put("lock.state.key", stateKey);
        }
    }
}