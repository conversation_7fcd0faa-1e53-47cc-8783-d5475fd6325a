# Destilink Framework: Redis Locking Module - Final Architecture & Plan

This document outlines the final consolidated architecture and plan for the `locking-redis-lock` module, derived from the analysis and synthesis of previous planning phases and updated to reflect the **Async-First design (utilizing Virtual Threads)**, **mandatory `redis-core` integration**, **centralized idempotency**, **refined watchdog mechanism**, **lock-type specific key schemas**, and **strict adherence to Destilink Framework guidelines**.

## Core Documentation

*   [Architecture Overview](architecture_overview.md) - *Updated to reflect Async-First with Virtual Threads, `redis-core` usage, and new operational semantics.*
*   [Configuration](configuration.md) - *Updated to detail `redis-core` property integration, refined `RedisLockProperties` structure (including `WatchdogProperties` and `Defaults`), and removal of obsolete properties.*
*   [Redis Key Schema](redis_key_schema.md) - *Updated for `redis-core` prefix usage and mandatory lock-type segments in keys.*
*   [<PERSON><PERSON>](lua_scripts_2.md) - *Consolidated and updated to reflect Redisson-inspired logic for ReadWriteLocks, emphasizing `ClusterCommandExecutor` usage, standardized return values, and mandatory idempotency wrappers.*
*   [Lock Acquisition Mechanism](lock_acquisition.md) - *Updated to highlight Async-First flow with Virtual Threads, `CompletableFuture` handling, `LockSemaphoreHolder` registration order, and `acquireTimeout` nuances.*
*   [Unlock Messaging](messaging.md) - *Updated to emphasize asynchronous message processing, `ClusterCommandExecutor` usage, and channel naming including lock-type segments.*
*   [Watchdog Mechanism](watchdog.md) - *Updated to clarify always-active watchdog service, conditional lock monitoring based on `safetyBufferMillis`, `PEXPIREAT` usage, and management of `originalLeaseTimeMillis` vs. `expiresAtMillis`.*
*   [Exception Handling](exception_handling.md) - *Updated to detail exception propagation in async flows, `redis-core` related exceptions, and new exception types related to refined operations.*
*   [Implementation Details](implementation_details.md) - *Updated to extensively cover Async-First with Virtual Threads, `redis-core` integration, guideline adherence, centralized idempotency, and refined operational semantics.*

## Supporting Documentation

*   [Performance Considerations](performance_considerations.md) - *Reviewed for Async-First/Virtual Thread impact and new configuration parameters.*
*   [Metrics](metrics.md) - *Reviewed for Async-First/Virtual Thread impact.*
*   [Testing Strategy](testing_strategy.md) - *Updated with explicit `test-support` module requirements, async testing with Virtual Threads, and scenarios for new watchdog/idempotency logic.*
*   [Modernization Plan & Assessment](modernization.md) - *Reviewed for comprehensive reflection of Async-First/Virtual Thread adoption, `redis-core` adoption, and all detailed plan changes.*
*   [Migration Notes](migration_notes.md) - *Reviewed for implications of Async-First/Virtual Threads, `redis-core` changes, property removals, and new key schema for users.*
*   [Glossary](glossary.md) - *Updated with new terms and clarifications related to Async-First/Virtual Threads, refined watchdog, idempotency, and key management.*