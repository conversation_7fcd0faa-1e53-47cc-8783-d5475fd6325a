# Redis Locking Module: Lock Types Deep Dive

## 1. Introduction

This document provides detailed information about each lock type available in the `locking-redis-lock` module. Each lock type is designed for specific use cases and provides different concurrency semantics while maintaining consistency through Redis-based coordination.

For architectural context, see [Architecture Overview](architecture.md).
For class relationships, see [Class Hierarchy](class_hierarchy.md).

## 2. RedisReentrantLock

### 2.1. Overview
`RedisReentrantLock` provides distributed reentrant mutual exclusion semantics, similar to `java.util.concurrent.locks.ReentrantLock` but across multiple application instances.

### 2.2. Key Characteristics
- **Reentrant**: The same owner can acquire the lock multiple times
- **Exclusive**: Only one owner can hold the lock at any time
- **Distributed**: Works across multiple application instances
- **Fair/Unfair**: Configurable fairness (implementation-dependent)

### 2.3. Redis Data Structure
```
Key: <prefix>:<bucket>:__locks__:{lockName}
Type: Hash
Fields:
  - owner: String (owner ID)
  - count: Integer (reentrancy count)
TTL: Set to leaseTime, extended by watchdog if applicable
```

### 2.4. Usage Example
```java
// Create lock
RedisReentrantLock lock = lockBucketRegistry
    .builder("myBucket")
    .lock()
    .reentrant()
    .withLeaseTime(Duration.ofMinutes(5))
    .build();

// Synchronous usage
lock.lock();
try {
    // Critical section
    // Can call lock.lock() again (reentrant)
} finally {
    lock.unlock();
}

// Asynchronous usage
lock.lockAsync()
    .thenRun(() -> {
        // Critical section
    })
    .whenComplete((result, throwable) -> {
        lock.unlockAsync();
    });
```

### 2.5. Lua Scripts Used
- `try_lock.lua`: Atomic acquisition with reentrancy support
- `unlock.lua`: Atomic release with reentrancy count management
- `extend_lock.lua`: Watchdog lease extension

## 3. RedisStateLock

### 3.1. Overview
`RedisStateLock` provides conditional locking based on an associated state value. The lock can only be acquired when the state matches an expected value, making it ideal for state-machine-like scenarios.

### 3.2. Key Characteristics
- **Conditional**: Acquisition depends on state value
- **State Management**: Can read and update state while holding lock
- **Non-Reentrant**: Typically single acquisition per owner
- **State Persistence**: State survives lock releases

### 3.3. Redis Data Structure
```
Main Lock Key: <prefix>:<bucket>:__locks__:{lockName}
Type: Hash
Fields:
  - owner: String (owner ID)
  - count: Integer (usually 1 for non-reentrant)

State Key: <prefix>:<bucket>:__locks__:{lockName}:state
Type: String
Value: Current state value
TTL: Configurable via stateKeyExpiration
```

### 3.4. Usage Example
```java
// Create state lock expecting "PENDING" state
RedisStateLock stateLock = lockBucketRegistry
    .builder("orderBucket")
    .lock()
    .state("PENDING")
    .withInitIfNotExist(true)
    .withStateExpiration(Duration.ofHours(1))
    .build();

// Acquire lock only if state is "PENDING"
if (stateLock.tryLock()) {
    try {
        // Process order
        String currentState = stateLock.getStateAsync().join();
        
        // Update state while holding lock
        stateLock.updateStateAsync("PROCESSING").join();
        
        // Or unlock with new state
        stateLock.unlockWithStateAsync("COMPLETED").join();
    } finally {
        if (stateLock.isHeldByCurrentThread()) {
            stateLock.unlock();
        }
    }
}
```

### 3.5. Lua Scripts Used
- `try_state_lock.lua`: Conditional acquisition based on state
- `update_state.lua`: State update while holding lock
- `unlock_state_lock.lua`: Release with optional state update

## 4. RedisReadWriteLock

### 4.1. Overview
`RedisReadWriteLock` implements the readers-writer pattern, allowing multiple concurrent readers or a single exclusive writer, but not both simultaneously.

### 4.2. Key Characteristics
- **Concurrent Reads**: Multiple readers can hold the lock simultaneously
- **Exclusive Writes**: Only one writer, blocks all readers
- **Reentrant**: Both read and write locks support reentrancy
- **Upgrade/Downgrade**: Limited support for lock conversion

### 4.3. Redis Data Structure
```
Main Lock Key: <prefix>:<bucket>:__locks__:{lockName}
Type: Hash
Fields:
  - mode: String ("read" or "write")
  - write_owner_id: String (writer's ID and reentrancy count)
  - reader_A_id: Integer (reader A's reentrancy count)
  - reader_B_id: Integer (reader B's reentrancy count)
  - ... (one field per active reader)

Individual Read Timeout Keys: <prefix>:<bucket>:__rwttl__:{lockName}:<readerId>:<count>
Type: String
Value: "1" (placeholder)
TTL: Individual read lock lease time
```

### 4.4. TTL Management
The main lock key's TTL is dynamically managed:
- **With Active Writer**: TTL managed by writer's lease time and watchdog
- **With Active Readers**: TTL set to maximum remaining TTL of all individual read timeout keys
- **Mode Transitions**: TTL recalculated during read/write mode changes

### 4.5. Usage Example
```java
RedisReadWriteLock rwLock = lockBucketRegistry
    .builder("resourceBucket")
    .lock()
    .readWrite()
    .withLeaseTime(Duration.ofMinutes(10))
    .build();

// Multiple readers can acquire simultaneously
AsyncLock readLock = rwLock.readLock();
readLock.lockAsync().thenRun(() -> {
    // Read operations - multiple threads can execute concurrently
}).whenComplete((result, throwable) -> {
    readLock.unlockAsync();
});

// Exclusive writer
AsyncLock writeLock = rwLock.writeLock();
writeLock.lockAsync().thenRun(() -> {
    // Write operations - exclusive access
}).whenComplete((result, throwable) -> {
    writeLock.unlockAsync();
});
```

### 4.6. Lua Scripts Used
- `try_read_lock.lua`: Acquire read lock with TTL coordination
- `unlock_read_lock.lua`: Release read lock with TTL recalculation
- `try_write_lock.lua`: Acquire exclusive write lock
- `unlock_write_lock.lua`: Release write lock with mode transition

## 5. RedisStampedLock

### 5.1. Overview
`RedisStampedLock` provides optimistic reading capabilities with version-based validation, similar to `java.util.concurrent.locks.StampedLock` but distributed.

### 5.2. Key Characteristics
- **Optimistic Reading**: Non-blocking reads with validation
- **Pessimistic Locking**: Traditional read/write locks available
- **Version Control**: Stamp-based validation for optimistic reads
- **Lock Conversion**: Convert between read and write modes

### 5.3. Redis Data Structure
```
Stamped Data Key: <prefix>:<bucket>:__locks__:{lockName}:stamped_data
Type: Hash
Fields:
  - version: Integer (incremented on write lock acquisition)
  - write_owner_id: String (current writer, if any)
  - write_reentrancy_count: Integer (writer's reentrancy count)
  - read_holders_count: Integer (number of active readers)
  - read_holders: Set/String (tracking active readers)
```

### 5.4. Stamp Format
Stamps are encoded as long values containing:
- **Type**: "O" (optimistic), "R" (read), "W" (write)
- **Version**: Current version number
- **Owner**: Owner ID for write stamps
- **Timestamp**: Optional timestamp component

### 5.5. Usage Example
```java
RedisStampedLock stampedLock = lockBucketRegistry
    .builder("dataBucket")
    .lock()
    .stamped()
    .withLeaseTime(Duration.ofMinutes(5))
    .build();

// Optimistic reading
long stamp = stampedLock.tryOptimisticRead();
// Read data without locking
Data data = readData();
if (stampedLock.validate(stamp)) {
    // Data is consistent, use it
    return data;
}

// Fall back to pessimistic read lock
stamp = stampedLock.readLock();
try {
    data = readData();
    return data;
} finally {
    stampedLock.unlock(stamp);
}

// Write lock with conversion
stamp = stampedLock.readLock();
try {
    data = readData();
    
    // Try to upgrade to write lock
    long writeStamp = stampedLock.tryConvertToWriteLock(stamp);
    if (writeStamp != 0) {
        stamp = writeStamp;
        // Now have write lock, can modify data
        modifyData(data);
    } else {
        // Upgrade failed, release read lock and acquire write lock
        stampedLock.unlock(stamp);
        stamp = stampedLock.writeLock();
        modifyData(data);
    }
} finally {
    stampedLock.unlock(stamp);
}
```

### 5.6. Lua Scripts Used
- `try_stamped_lock.lua`: Acquire read/write/optimistic locks
- `unlock_stamped_lock.lua`: Release locks with version management
- `validate_stamp.lua`: Validate optimistic read stamps
- `convert_to_write_lock.lua`: Upgrade read to write lock
- `convert_to_read_lock.lua`: Downgrade write to read lock

## 6. Lock Type Comparison

| Feature | RedisReentrantLock | RedisStateLock | RedisReadWriteLock | RedisStampedLock |
|---------|-------------------|----------------|-------------------|------------------|
| **Concurrency Model** | Exclusive | Exclusive | Readers-Writer | Optimistic + Readers-Writer |
| **Reentrancy** | Yes | Configurable | Yes (both read/write) | Yes (read/write modes) |
| **State Management** | No | Yes | No | Version-based |
| **Optimistic Reading** | No | No | No | Yes |
| **Lock Conversion** | No | No | Limited | Yes |
| **Use Cases** | General mutual exclusion | State machines, workflows | Read-heavy scenarios | High-contention reads |
| **Complexity** | Low | Medium | High | High |
| **Performance** | Good | Good | Excellent for reads | Excellent for optimistic reads |

## 7. Choosing the Right Lock Type

### 7.1. Use RedisReentrantLock When:
- Simple mutual exclusion is needed
- Code may need to reacquire the same lock
- Straightforward critical sections
- General-purpose distributed locking

### 7.2. Use RedisStateLock When:
- Lock acquisition depends on external state
- Implementing state machines or workflows
- Need to atomically update state with lock operations
- Conditional processing based on current state

### 7.3. Use RedisReadWriteLock When:
- Read operations significantly outnumber writes
- Multiple readers can safely operate concurrently
- Write operations need exclusive access
- Data consistency is critical during updates

### 7.4. Use RedisStampedLock When:
- Very high read contention with occasional writes
- Optimistic reading can improve performance
- Need lock conversion capabilities
- Can handle validation failures gracefully

## 8. Performance Considerations

### 8.1. RedisReentrantLock
- **Pros**: Simple, low overhead, predictable performance
- **Cons**: All operations are exclusive, potential bottleneck

### 8.2. RedisStateLock
- **Pros**: Conditional logic reduces unnecessary blocking
- **Cons**: Additional state key operations, more complex scripts

### 8.3. RedisReadWriteLock
- **Pros**: Excellent read scalability, complex TTL management optimized
- **Cons**: Write operations more expensive, complex coordination

### 8.4. RedisStampedLock
- **Pros**: Optimistic reads have minimal overhead, excellent for read-heavy workloads
- **Cons**: Validation failures require retry logic, complex implementation

Each lock type is optimized for its specific use case, and the choice should align with the application's concurrency patterns and performance requirements.