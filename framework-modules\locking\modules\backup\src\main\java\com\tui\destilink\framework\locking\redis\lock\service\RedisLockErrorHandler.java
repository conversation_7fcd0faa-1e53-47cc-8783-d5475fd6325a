package com.tui.destilink.framework.locking.redis.lock.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Handler for Redis lock-related errors.
 * <p>
 * This component provides centralized error handling for Redis lock operations,
 * including standardized logging, error classification, and optional recovery
 * strategies.
 * </p>
 */
public class RedisLockErrorHandler {

    private static final Logger log = LoggerFactory.getLogger(RedisLockErrorHandler.class);

    /**
     * Handles errors that occur during lock acquisition.
     *
     * @param lockName  The name of the lock being acquired
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockAcquisitionError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to acquire lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during lock release.
     *
     * @param lockName  The name of the lock being released
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockReleaseError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to release lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during lock extension.
     *
     * @param lockName  The name of the lock being extended
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockExtensionError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to extend lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during Redis Pub/Sub operations.
     *
     * @param channel   The Redis channel involved
     * @param exception The exception that occurred
     */
    public void handlePubSubError(String channel, Exception exception) {
        log.error("Redis Pub/Sub error on channel '{}': {}",
                channel, exception.getMessage(), exception);
    }
}