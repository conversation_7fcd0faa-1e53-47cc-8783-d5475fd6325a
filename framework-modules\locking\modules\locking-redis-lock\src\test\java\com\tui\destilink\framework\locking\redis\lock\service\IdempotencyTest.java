package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.*;

/**
 * Test suite for idempotency mechanism in Redis lock operations.
 * <p>
 * Tests ensure that:
 * - Operations with the same requestUuid are idempotent
 * - Response cache works correctly with responseCacheTtl
 * - Retried operations don't cause duplicate side effects
 * - Concurrent operations with same requestUuid are handled correctly
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-idempotency:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class IdempotencyTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(IdempotencyTest.class);

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    private String lockKey;
    private String ownerId;
    private Duration leaseTime;

    @BeforeEach
    void setUp() {
        lockKey = "test-idempotency:" + UNIQUE_ID + ":lock";
        ownerId = lockOwnerSupplier.getLockOwnerId();
        leaseTime = Duration.ofSeconds(30);
    }

    @Test
    @DisplayName("Should handle duplicate lock acquisition requests idempotently")
    void shouldHandleDuplicateLockAcquisitionIdempotently() {
        // First acquisition should succeed
        CompletableFuture<Boolean> firstAttempt = redisLockOperations.tryLock(lockKey, ownerId, leaseTime);
        assertThat(firstAttempt).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);

        // Second acquisition with same owner should be idempotent (succeed without error)
        CompletableFuture<Boolean> secondAttempt = redisLockOperations.tryLock(lockKey, ownerId, leaseTime);
        assertThat(secondAttempt).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);

        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle duplicate unlock requests idempotently")
    void shouldHandleDuplicateUnlockIdempotently() {
        // Acquire lock first
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();

        // First unlock should succeed
        CompletableFuture<Boolean> firstUnlock = redisLockOperations.unlock(lockKey, ownerId);
        assertThat(firstUnlock).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);

        // Second unlock should be idempotent (not throw error, return false)
        CompletableFuture<Boolean> secondUnlock = redisLockOperations.unlock(lockKey, ownerId);
        assertThat(secondUnlock).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(false);
    }

    @Test
    @DisplayName("Should handle duplicate extend lease requests idempotently")
    void shouldHandleDuplicateExtendLeaseIdempotently() {
        // Acquire lock first
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();

        Duration newLeaseTime = Duration.ofSeconds(60);

        // First extend should succeed
        CompletableFuture<Boolean> firstExtend = redisLockOperations.extendLock(lockKey, ownerId, newLeaseTime);
        assertThat(firstExtend).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);

        // Second extend with same parameters should be idempotent
        CompletableFuture<Boolean> secondExtend = redisLockOperations.extendLock(lockKey, ownerId, newLeaseTime);
        assertThat(secondExtend).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);

        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle concurrent operations with proper idempotency")
    void shouldHandleConcurrentOperationsWithIdempotency() throws InterruptedException {
        int numberOfThreads = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(numberOfThreads);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);

        // Submit multiple concurrent lock acquisition attempts
        for (int i = 0; i < numberOfThreads; i++) {
            executor.submit(() -> {
                try {
                    startLatch.await(); // Wait for all threads to be ready
                    
                    boolean acquired = redisLockOperations.tryLock(lockKey, ownerId, leaseTime)
                            .get(5, TimeUnit.SECONDS);
                    
                    if (acquired) {
                        successCount.incrementAndGet();
                    } else {
                        failureCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        // Start all threads simultaneously
        startLatch.countDown();
        
        // Wait for all threads to complete
        assertThat(completionLatch.await(10, TimeUnit.SECONDS)).isTrue();

        // Due to idempotency, all attempts with same owner should succeed
        assertThat(successCount.get()).isEqualTo(numberOfThreads);
        assertThat(failureCount.get()).isEqualTo(0);

        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
        executor.shutdown();
    }

    @Test
    @DisplayName("Should respect response cache TTL for idempotency")
    void shouldRespectResponseCacheTtlForIdempotency() {
        // This test would require access to Redis directly to verify cache entries
        // For now, we test the behavior indirectly by ensuring operations remain idempotent
        
        // Acquire lock
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();
        
        // Multiple operations should be idempotent
        for (int i = 0; i < 5; i++) {
            CompletableFuture<Boolean> result = redisLockOperations.tryLock(lockKey, ownerId, leaseTime);
            assertThat(result).succeedsWithin(Duration.ofSeconds(5))
                    .isEqualTo(true);
        }
        
        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }
}
