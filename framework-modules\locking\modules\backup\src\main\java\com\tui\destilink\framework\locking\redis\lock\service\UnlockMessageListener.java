package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.model.UnlockType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * Redis message listener for unlock notifications.
 * <p>
 * This class is not Spring-managed and is created per Redis bucket by the
 * {@link UnlockMessageListenerManager}. It subscribes to a Redis Pub/Sub
 * channel pattern
 * for unlock events and manages a collection of {@link LockSemaphoreHolder}
 * instances
 * for each lock key.
 * </p>
 * <p>
 * When an unlock event is received, it parses the message to determine the lock
 * name
 * and unlock type, then signals the appropriate {@link LockSemaphoreHolder} to
 * wake up
 * waiting threads.
 * </p>
 */
public class UnlockMessageListener implements MessageListener {

    private static final Logger log = LoggerFactory.getLogger(UnlockMessageListener.class);

    /**
     * The Redis bucket name this listener is associated with.
     */
    private final String bucketName;

    /**
     * The channel pattern this listener subscribes to.
     */
    private final String channelPattern;

    /**
     * Map of lock names to their corresponding semaphore holders.
     */
    private final Map<String, LockSemaphoreHolder> lockSemaphoreHolders = new ConcurrentHashMap<>();

    /**
     * Executor for processing unlock messages asynchronously to avoid blocking
     * Redis client threads.
     */
    private final Executor unlockMessageExecutor;

    /**
     * Creates a new UnlockMessageListener for the specified bucket.
     *
     * @param bucketName            The Redis bucket name
     * @param channelPattern        The Redis channel pattern to subscribe to
     * @param unlockMessageExecutor The executor for processing unlock messages
     */
    public UnlockMessageListener(String bucketName, String channelPattern, Executor unlockMessageExecutor) {
        this.bucketName = bucketName;
        this.channelPattern = channelPattern;
        this.unlockMessageExecutor = unlockMessageExecutor;
    }

    /**
     * Handles Redis Pub/Sub messages for unlock events.
     * <p>
     * This method is called by the Redis client when a message is received on the
     * subscribed channel.
     * It extracts the lock name from the channel and the unlock type from the
     * message body,
     * then delegates to the appropriate {@link LockSemaphoreHolder} to signal
     * waiting threads.
     * </p>
     * <p>
     * Processing is done asynchronously using the provided executor to avoid
     * blocking Redis client threads.
     * </p>
     *
     * @param message The Redis message containing the unlock type
     * @param pattern The pattern that matched the channel (not used)
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        // Extract the lock name from the channel
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        String lockName = extractLockNameFromChannel(channel);

        // Extract the unlock type from the message body
        String unlockTypeStr = new String(message.getBody(), StandardCharsets.UTF_8);

        // Process the message asynchronously to avoid blocking Redis client threads
        unlockMessageExecutor.execute(() -> processUnlockMessage(lockName, unlockTypeStr));
    }

    /**
     * Processes an unlock message for a specific lock.
     *
     * @param lockName      The name of the lock that was released
     * @param unlockTypeStr The type of unlock event as a string
     */
    private void processUnlockMessage(String lockName, String unlockTypeStr) {
        try {
            UnlockType unlockType = UnlockType.valueOf(unlockTypeStr);
            LockSemaphoreHolder semaphoreHolder = lockSemaphoreHolders.get(lockName);

            if (semaphoreHolder == null) {
                log.trace("Received unlock notification for lock '{}' with type '{}', but no waiters registered",
                        lockName, unlockType);
                return;
            }

            int waitersCount = semaphoreHolder.getWaitersCount();
            if (waitersCount <= 0) {
                log.trace("Received unlock notification for lock '{}' with type '{}', but no active waiters",
                        lockName, unlockType);
                return;
            }

            // Signal the appropriate number of waiters based on the unlock type
            switch (unlockType) {
                case REENTRANT_FULLY_RELEASED:
                case NON_REENTRANT_RELEASED:
                case STATE_LOCK_RELEASED_STATE_UNCHANGED:
                case STATE_LOCK_RELEASED_STATE_UPDATED:
                case RW_READ_RELEASED_WAKEN_SINGLE_WRITER:
                case STAMPED_WRITE_RELEASED:
                case STAMPED_CONVERTED_TO_READ:
                case STAMPED_CONVERTED_TO_WRITE:
                    // Wake up a single waiter
                    semaphoreHolder.signal();
                    log.trace("Signaled 1 waiter for lock '{}' with type '{}'", lockName, unlockType);
                    break;

                case RW_READ_RELEASED_WAKEN_READERS:
                case RW_WRITE_RELEASED_WAKEN_ALL:
                case STAMPED_READ_RELEASED:
                    // Wake up all waiters
                    semaphoreHolder.signal(waitersCount);
                    log.trace("Signaled all {} waiters for lock '{}' with type '{}'",
                            waitersCount, lockName, unlockType);
                    break;

                default:
                    log.warn("Unknown unlock type '{}' for lock '{}'", unlockType, lockName);
                    // Wake up a single waiter as a fallback
                    semaphoreHolder.signal();
            }
        } catch (IllegalArgumentException e) {
            log.warn("Received invalid unlock type '{}' for lock '{}'", unlockTypeStr, lockName, e);
        } catch (Exception e) {
            log.error("Error processing unlock notification for lock '{}' with type '{}'",
                    lockName, unlockTypeStr, e);
        }
    }

    /**
     * Extracts the lock name from the Redis channel.
     * <p>
     * The channel format is expected to be:
     * {@code <prefix>:<bucketName>:__unlock_channels__:{<lockName>}}
     * </p>
     *
     * @param channel The Redis channel
     * @return The extracted lock name
     */
    private String extractLockNameFromChannel(String channel) {
        // Extract the lock name from the channel pattern
        // Format: <prefix>:<bucketName>:__unlock_channels__:{<lockName>}
        int startIndex = channel.lastIndexOf('{');
        int endIndex = channel.lastIndexOf('}');

        if (startIndex >= 0 && endIndex > startIndex) {
            return channel.substring(startIndex + 1, endIndex);
        }

        // Fallback if the channel format is unexpected
        log.warn("Unexpected channel format: {}", channel);
        return channel;
    }

    /**
     * Registers a semaphore holder for a specific lock key.
     *
     * @param lockName        The name of the lock
     * @param semaphoreHolder The semaphore holder to register
     * @return The registered semaphore holder (same as the input)
     */
    public LockSemaphoreHolder registerLockSemaphoreHolder(String lockName, LockSemaphoreHolder semaphoreHolder) {
        lockSemaphoreHolders.put(lockName, semaphoreHolder);
        return semaphoreHolder;
    }

    /**
     * Unregisters a semaphore holder for a specific lock key.
     *
     * @param lockName The name of the lock
     * @return The unregistered semaphore holder, or null if none was registered
     */
    public LockSemaphoreHolder unregisterLockSemaphoreHolder(String lockName) {
        return lockSemaphoreHolders.remove(lockName);
    }

    /**
     * Gets the bucket name this listener is associated with.
     *
     * @return The bucket name
     */
    public String getBucketName() {
        return bucketName;
    }

    /**
     * Gets the channel pattern this listener subscribes to.
     *
     * @return The channel pattern
     */
    public String getChannelPattern() {
        return channelPattern;
    }
}