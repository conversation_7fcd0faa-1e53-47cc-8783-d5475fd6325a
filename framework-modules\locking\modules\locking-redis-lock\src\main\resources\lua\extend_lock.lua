-- Redis Extend Lock Script with Idempotency (User-Initiated)
-- This script atomically extends a lock's lease time with idempotency support
--
-- KEYS[1] - The main lock key (format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>})
-- KEYS[2] - The lock data key (Redis Hash for metadata)
-- KEYS[3] - The response cache key for idempotency
--
-- ARGV[1] - Request UUID for idempotency
-- ARGV[2] - Response cache TTL in seconds
-- ARGV[3] - Lock owner ID
-- ARGV[4] - New relative lease time in milliseconds (user-provided)
-- ARGV[5] - Lock type (e.g., "reentrant", "stamped", "state")

local lockKey = KEYS[1]
local lockDataKey = KEYS[2]
local responseCacheKey = KEYS[3]

local requestUuid = ARGV[1]
local responseCacheTtl = tonumber(ARGV[2])
local ownerId = ARGV[3]
local newRelativeLeaseTimeMillis = tonumber(ARGV[4])
local lockType = ARGV[5]

-- ========================================
-- IDEMPOTENCY CHECK
-- ========================================
-- Check if this operation was already executed
local cachedResponse = redis.call('GET', responseCacheKey)
if cachedResponse then
    -- Operation already completed, return cached result
    local result = cjson.decode(cachedResponse)
    return result
end

-- ========================================
-- CORE EXTEND LOCK LOGIC
-- ========================================
-- Get current Redis server time for precise calculations
local timeResult = redis.call('TIME')
local currentTimeMillis = tonumber(timeResult[1]) * 1000 + math.floor(tonumber(timeResult[2]) / 1000)

-- Calculate new absolute expiration time
local newExpiresAtMillis = currentTimeMillis + newRelativeLeaseTimeMillis

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local statusCode = 0  -- Default: not extended
local extended = false

if currentOwner == ownerId then
    -- Lock is held by the requesting owner, extend it
    redis.call('PEXPIREAT', lockKey, newExpiresAtMillis)

    -- Update metadata with new lease time and expiration
    -- IMPORTANT: This updates originalLeaseTimeMillis as it's user-initiated
    redis.call('HSET', lockDataKey,
        'expiresAtMillis', newExpiresAtMillis,
        'originalLeaseTimeMillis', newRelativeLeaseTimeMillis)
    redis.call('PEXPIREAT', lockDataKey, newExpiresAtMillis)

    statusCode = 1  -- Successfully extended
    extended = true

elseif currentOwner == false then
    -- Lock doesn't exist (expired or never acquired)
    statusCode = 0  -- Not extended (lock doesn't exist)

else
    -- Lock is held by a different owner
    statusCode = -1  -- Not extended (not owner)
end

-- ========================================
-- PREPARE STRUCTURED RESPONSE
-- ========================================
local response = {
    statusCode,
    newExpiresAtMillis,
    newRelativeLeaseTimeMillis
}

-- ========================================
-- CACHE RESPONSE FOR IDEMPOTENCY
-- ========================================
-- Store the response in cache for future idempotency checks
redis.call('SETEX', responseCacheKey, responseCacheTtl, cjson.encode(response))

return response