package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when there is an error serializing lock data.
 * <p>
 * This exception indicates that an object could not be properly serialized
 * for storage in Redis as part of a lock operation.
 * </p>
 */
public class LockSerializationException extends AbstractRedisLockException {

    /**
     * Constructs a new LockSerializationException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisLock",
     *                    "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     */
    public LockSerializationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
    }

    /**
     * Constructs a new LockSerializationException with the specified details and
     * cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisLock",
     *                    "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public LockSerializationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // No additional specific markers beyond the base class
    }
}