package com.tui.destilink.framework.locking.redis.lock.config;

import lombok.Builder;
import lombok.Value;
import lombok.experimental.Accessors;

import java.time.Duration;

/**
 * Represents the resolved and effective configuration for a specific lock
 * bucket.
 * &lt;p&gt;
 * This class is a non-Spring managed POJO that holds the configuration values
 * for a lock bucket, including lease time, retry interval, and other related
 * settings.
 * &lt;/p&gt;
 */
@Value
@Builder
@Accessors(fluent = true)
public class LockBucketConfig {

    /**
     * The lease time for locks in this bucket.
     */
    Duration leaseTime;

    /**
     * The retry interval for lock acquisition attempts in this bucket.
     */
    Duration retryInterval;

    /**
     * The expiration time for lock state keys in Redis for this bucket.
     */
    Duration stateKeyExpiration;

    /**
     * Maximum number of retry attempts for lock acquisition in this bucket.
     * This can override global settings.
     */
    Integer maxRetries; // Added field

    // The LockOwnerSupplier will be added later when it's defined
    // LockOwnerSupplier lockOwnerSupplier;
}