# Redis Distributed Locking Module API Reference

## Table of Contents
- [Redis Distributed Locking Module API Reference](#redis-distributed-locking-module-api-reference)
  - [Table of Contents](#table-of-contents)
  - [Introduction](#introduction)
  - [Core Interfaces](#core-interfaces)
    - [LockManager](#lockmanager)
    - [Lock](#lock)
  - [Lock Types](#lock-types)
    - [1. Reentrant Lock](#1-reentrant-lock)
    - [2. State Lock](#2-state-lock)
    - [3. Semaphore](#3-semaphore)
  - [Configuration](#configuration)
  - [Usage Examples](#usage-examples)
    - [Acquiring a Reentrant Lock](#acquiring-a-reentrant-lock)
    - [Using a State Lock](#using-a-state-lock)
    - [Using a Semaphore](#using-a-semaphore)
    - [Using Idempotency](#using-idempotency)
    - [Virtual Threads Integration](#virtual-threads-integration)
  - [Error Handling](#error-handling)
    - [Exception Handling Best Practices](#exception-handling-best-practices)
  - [Virtual Threads Integration](#virtual-threads-integration-1)
  - [Idempotency Mechanisms](#idempotency-mechanisms)
    - [Reentrant Lock Idempotency](#reentrant-lock-idempotency)
    - [State Lock Idempotency](#state-lock-idempotency)
    - [Semaphore Idempotency](#semaphore-idempotency)
  - [Lua Script Integration](#lua-script-integration)
  - [Performance Considerations and Best Practices](#performance-considerations-and-best-practices)
  - [Security Considerations](#security-considerations)

## Introduction

This document provides a comprehensive reference for the Redis Distributed Locking Module API. It covers the public interfaces, configuration options, and usage patterns for implementing distributed locking in your applications.

## Core Interfaces

### LockManager

The primary interface for interacting with the locking system.

```java
public interface LockManager {
    Lock acquire(String lockName, Duration leaseTime) throws LockAcquisitionException;
    boolean release(Lock lock) throws LockReleaseException;
    boolean extend(Lock lock, Duration additionalLeaseTime) throws LockExtensionException;
}
```

### Lock

Represents an acquired lock.

```java
public interface Lock {
    String getName();
    String getToken();
    Instant getExpirationTime();
}
```

## Lock Types

The module supports the following lock types, each with its own specific API methods and behaviors:

### 1. Reentrant Lock

Allows the same thread to acquire the lock multiple times.

```java
public interface ReentrantLock extends Lock {
    boolean lock(Duration leaseTime) throws LockAcquisitionException;
    boolean tryLock(Duration leaseTime, Duration waitTime) throws LockAcquisitionException, InterruptedException;
    void unlock() throws LockReleaseException;
    boolean isLocked() throws LockOperationException;
    int getHoldCount() throws LockOperationException;
}
```

### 2. State Lock

Associates state information with the lock.

```java
public interface StateLock<T> extends Lock {
    boolean setState(T state, Duration leaseTime) throws LockAcquisitionException;
    T getState() throws LockOperationException;
    boolean compareAndSet(T expectedState, T newState, Duration leaseTime) throws LockOperationException;
    T waitForState(Predicate<T> condition, Duration timeout) throws LockOperationException, InterruptedException;
}
```

### 3. Semaphore

Limits the number of concurrent holders.

```java
public interface Semaphore extends Lock {
    boolean acquire(int permits, Duration leaseTime) throws LockAcquisitionException;
    boolean tryAcquire(int permits, Duration leaseTime, Duration waitTime) throws LockAcquisitionException, InterruptedException;
    void release(int permits) throws LockReleaseException;
    int availablePermits() throws LockOperationException;
}
```

Each lock type supports idempotent operations and Virtual Threads integration. For idempotent operations, an additional `requestUuid` parameter is included in the method signatures.

## Configuration

Configuration is managed through the `RedisLockProperties` class:

```java
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
public class RedisLockProperties {
    private boolean enabled = true;
    private Duration defaultLeaseTime = Duration.ofSeconds(30);
    private Duration safetyBufferFactor = Duration.ofMillis(500);
    private String keyPrefix = "destilink:lock:";
    private int maxRetries = 3;
    private Duration retryDelay = Duration.ofMillis(100);
    private boolean useVirtualThreads = true;
    private int virtualThreadPoolSize = 100;
    private Duration lockWatchdogInterval = Duration.ofSeconds(10);
    private boolean useIdempotency = true;
    private Duration idempotencyTtl = Duration.ofMinutes(5);
    private int maxConcurrentLocks = 1000;
}
```

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| enabled | boolean | true | Enables/disables the Redis locking module |
| defaultLeaseTime | Duration | 30s | Default lease time for locks if not specified |
| safetyBufferFactor | Duration | 500ms | Safety buffer added to lease time for clock drift |
| keyPrefix | String | "destilink:lock:" | Prefix for Redis keys used by the locking module |
| maxRetries | int | 3 | Maximum number of retries for lock operations |
| retryDelay | Duration | 100ms | Delay between retries for lock operations |
| useVirtualThreads | boolean | true | Enables/disables the use of Virtual Threads |
| virtualThreadPoolSize | int | 100 | Size of the Virtual Thread pool |
| lockWatchdogInterval | Duration | 10s | Interval for the lock watchdog to check and extend locks |
| useIdempotency | boolean | true | Enables/disables idempotency support |
| idempotencyTtl | Duration | 5m | Time-to-live for idempotency records |
| maxConcurrentLocks | int | 1000 | Maximum number of concurrent locks allowed |

Example configuration in `application.yml`:

```yaml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        default-lease-time: 1m
        safety-buffer-factor: 1s
        key-prefix: "myapp:lock:"
        max-retries: 5
        retry-delay: 200ms
        use-virtual-threads: true
        virtual-thread-pool-size: 200
        lock-watchdog-interval: 30s
        use-idempotency: true
        idempotency-ttl: 10m
        max-concurrent-locks: 2000
```

## Usage Examples

### Acquiring a Reentrant Lock

```java
ReentrantLock lock = (ReentrantLock) lockManager.acquire("my-resource", Duration.ofMinutes(5));
try {
    if (lock.lock(Duration.ofMinutes(5))) {
        try {
            // Critical section
            if (lock.getHoldCount() > 1) {
                log.info("Lock acquired multiple times by the same thread");
            }
        } finally {
            lock.unlock();
        }
    } else {
        log.warn("Failed to acquire lock");
    }
} catch (LockAcquisitionException | LockReleaseException e) {
    log.error("Lock operation failed", e);
}
```

### Using a State Lock

```java
StateLock<String> lock = (StateLock<String>) lockManager.acquire("state-resource", Duration.ofMinutes(5));
try {
    if (lock.setState("PROCESSING", Duration.ofMinutes(5))) {
        try {
            // Process the resource
            String currentState = lock.getState();
            if (lock.compareAndSet(currentState, "COMPLETED", Duration.ofMinutes(5))) {
                log.info("Resource processing completed");
            }
        } finally {
            lock.setState("IDLE", Duration.ofMinutes(1));
        }
    } else {
        log.warn("Failed to set initial state");
    }
} catch (LockAcquisitionException | LockOperationException e) {
    log.error("State lock operation failed", e);
}
```

### Using a Semaphore

```java
Semaphore semaphore = (Semaphore) lockManager.acquire("resource-pool", Duration.ofMinutes(10));
try {
    if (semaphore.acquire(2, Duration.ofMinutes(5))) {
        try {
            // Use 2 resources from the pool
            int available = semaphore.availablePermits();
            log.info("Available permits: {}", available);
        } finally {
            semaphore.release(2);
        }
    } else {
        log.warn("Failed to acquire semaphore permits");
    }
} catch (LockAcquisitionException | LockReleaseException | LockOperationException e) {
    log.error("Semaphore operation failed", e);
}
```

### Using Idempotency

```java
String requestUuid = UUID.randomUUID().toString();
try {
    Lock lock = lockManager.acquireWithIdempotency("idempotent-resource", Duration.ofMinutes(5), requestUuid);
    try {
        // Perform idempotent operation
    } finally {
        lockManager.release(lock);
    }
} catch (LockAcquisitionException e) {
    if (e.isIdempotentOperationAlreadyExecuted()) {
        log.info("Operation already executed with UUID: {}", requestUuid);
    } else {
        log.error("Failed to acquire lock", e);
    }
}
```

### Virtual Threads Integration

```java
try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
    Future<Lock> lock1Future = scope.fork(() -> lockManager.acquire("resource1", Duration.ofMinutes(1)));
    Future<Lock> lock2Future = scope.fork(() -> lockManager.acquire("resource2", Duration.ofMinutes(1)));
    
    scope.join();
    scope.throwIfFailed();
    
    Lock lock1 = lock1Future.resultNow();
    Lock lock2 = lock2Future.resultNow();
    
    try {
        // Use acquired locks
    } finally {
        lockManager.release(lock1);
        lockManager.release(lock2);
    }
} catch (InterruptedException | ExecutionException e) {
    log.error("Failed to acquire locks concurrently", e);
}
```

## Error Handling

The module uses custom exceptions for different error scenarios:

- `LockAcquisitionException`: Thrown when a lock cannot be acquired.
  - Possible causes: Redis connection issues, concurrent lock attempts, invalid lock parameters.
  - Recovery: Implement retry logic with exponential backoff, or fail-fast depending on the use case.

- `LockReleaseException`: Thrown when a lock cannot be released.
  - Possible causes: Redis connection issues, lock expiration before release attempt.
  - Recovery: Log the error, as the lock will eventually expire. Consider alerting for persistent issues.

- `LockExtensionException`: Thrown when a lock's lease time cannot be extended.
  - Possible causes: Redis connection issues, lock expiration before extension attempt.
  - Recovery: Treat as a lock acquisition failure and re-acquire the lock if necessary.

- `LockOperationException`: General exception for lock operations (e.g., getState, compareAndSet).
  - Possible causes: Redis connection issues, unexpected data format.
  - Recovery: Depends on the specific operation. May require re-acquiring the lock or failing the operation.

### Exception Handling Best Practices

1. Always catch and handle lock exceptions in your application code.
2. Implement retry logic for transient errors, especially for lock acquisition.
3. Use circuit breakers to prevent cascading failures during persistent Redis issues.
4. Log all lock exceptions with appropriate context for troubleshooting.
5. Consider using custom exception handlers or aspect-oriented programming for centralized error handling.

Example error handling with retry logic:

```java
int maxRetries = 3;
int retryDelayMs = 100;

for (int attempt = 0; attempt < maxRetries; attempt++) {
    try {
        Lock lock = lockManager.acquire("my-resource", Duration.ofMinutes(5));
        try {
            // Critical section
        } finally {
            lockManager.release(lock);
        }
        break; // Success, exit the retry loop
    } catch (LockAcquisitionException e) {
        if (attempt == maxRetries - 1) {
            log.error("Failed to acquire lock after {} attempts", maxRetries, e);
            throw e; // Rethrow if all retries are exhausted
        }
        log.warn("Lock acquisition failed, retrying (attempt {})", attempt + 1, e);
        Thread.sleep(retryDelayMs * (attempt + 1)); // Exponential backoff
    } catch (LockReleaseException e) {
        log.error("Failed to release lock", e);
        // Consider alerting or taking corrective action
    }
}
```

## Virtual Threads Integration

The module leverages Java's Virtual Threads for improved scalability:

```java
try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
    scope.fork(() -> lockManager.acquire("resource1", Duration.ofMinutes(1)));
    scope.fork(() -> lockManager.acquire("resource2", Duration.ofMinutes(1)));
    scope.join();
    scope.throwIfFailed();
    // ... use acquired locks
}
```

## Idempotency Mechanisms

Idempotency is supported through the use of `requestUuid` for all lock types. This ensures that operations are not accidentally repeated due to network issues or client retries.

```java
public interface IdempotentLockManager extends LockManager {
    Lock acquireWithIdempotency(String lockName, Duration leaseTime, String requestUuid) throws LockAcquisitionException;
}
```

### Reentrant Lock Idempotency

```java
public interface IdempotentReentrantLock extends ReentrantLock {
    boolean lockWithIdempotency(Duration leaseTime, String requestUuid) throws LockAcquisitionException;
    boolean tryLockWithIdempotency(Duration leaseTime, Duration waitTime, String requestUuid) throws LockAcquisitionException, InterruptedException;
}
```

### State Lock Idempotency

```java
public interface IdempotentStateLock<T> extends StateLock<T> {
    boolean setStateWithIdempotency(T state, Duration leaseTime, String requestUuid) throws LockAcquisitionException;
    boolean compareAndSetWithIdempotency(T expectedState, T newState, Duration leaseTime, String requestUuid) throws LockOperationException;
}
```

### Semaphore Idempotency

```java
public interface IdempotentSemaphore extends Semaphore {
    boolean acquireWithIdempotency(int permits, Duration leaseTime, String requestUuid) throws LockAcquisitionException;
    boolean tryAcquireWithIdempotency(int permits, Duration leaseTime, Duration waitTime, String requestUuid) throws LockAcquisitionException, InterruptedException;
}
```

Idempotent operations are implemented using a Redis hash that stores the `requestUuid` and operation result. Before performing an operation, the system checks if the `requestUuid` exists:

- If it exists and the operation was successful, the previous result is returned.
- If it exists and the operation failed, the failure is returned.
- If it doesn't exist, the operation is performed, and the result is stored with the `requestUuid`.

The `idempotencyTtl` configuration property determines how long idempotency records are kept.

## Lua Script Integration

All Redis operations are performed through Lua scripts for atomic execution:

```java
public class RedisLockManagerImpl implements LockManager {
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisScript<Boolean> acquireLockScript;
    // ... other scripts

    public Lock acquire(String lockName, Duration leaseTime) {
        String token = generateToken();
        Boolean acquired = redisTemplate.execute(acquireLockScript, 
            Collections.singletonList(getRedisKey(lockName)), 
            token, String.valueOf(leaseTime.toMillis()));
        // ... handle result
    }
    // ... other methods
}
```

## Performance Considerations and Best Practices

1. **Optimal Lease Times**:
   - Set lease times to be as short as possible while still covering the expected operation duration.
   - Longer lease times reduce contention but may lead to resource underutilization if locks are held longer than necessary.

2. **Lock Granularity**:
   - Use fine-grained locks when possible to reduce contention.
   - Example: Lock on individual user IDs rather than a global "users" lock.

3. **Avoid Lock Nesting**:
   - Minimize nested locks to prevent deadlocks and reduce complexity.
   - If nested locks are necessary, always acquire them in the same order to prevent deadlocks.

4. **Use Try-Lock for Non-Critical Operations**:
   - For operations that can be skipped or retried later, use `tryLock` with a short timeout instead of blocking indefinitely.

5. **Implement Lock Timeouts**:
   - Always use a timeout when acquiring locks to prevent indefinite waiting.
   - Handle timeout exceptions gracefully in your application logic.

6. **Optimize Redis Connection Pool**:
   - Tune the Redis connection pool size based on your application's concurrency requirements.
   - Monitor connection usage and adjust as needed.

7. **Lua Script Caching**:
   - The module uses Lua scripts for atomic operations. Ensure your Redis client is configured to cache these scripts for optimal performance.

8. **Monitor Lock Durations**:
   - Set up monitoring for lock acquisition times and hold durations.
   - Alert on unexpectedly long lock holds, which may indicate issues in your application logic.

9. **Use Idempotency Judiciously**:
   - While idempotency provides safety, it comes with a performance cost due to additional Redis operations.
   - Use idempotency for critical operations or where retries are expected, but consider skipping it for high-volume, low-risk operations.

10. **Leverage Virtual Threads**:
    - For high-concurrency scenarios, use Virtual Threads to efficiently manage many concurrent lock operations without overwhelming system resources.

11. **Regular Maintenance**:
    - Periodically clean up expired locks and idempotency records to prevent Redis memory growth.
    - The module handles this automatically, but ensure the cleanup job is running as expected.

## Security Considerations

1. **Token Generation**:
   - Use cryptographically secure random number generators for lock tokens.
   - Example: `java.security.SecureRandom` for generating lock tokens.

2. **Lease Time Limits**:
   - Enforce maximum lease times to prevent indefinite locking.
   - Configure `maxLeaseTime` in `RedisLockProperties` to set an upper bound.

3. **Access Control**:
   - Implement proper access controls on Redis to prevent unauthorized access to lock keys.
   - Use Redis ACLs or separate Redis instances for different security domains.

4. **Monitoring and Alerting**:
   - Set up monitoring for lock acquisition patterns and potential deadlocks.
   - Alert on suspicious activities like rapid lock/unlock cycles or long-held locks.

5. **Data Encryption**:
   - If locks contain sensitive information (e.g., in state locks), consider encrypting the data before storing in Redis.

6. **Secure Communication**:
   - Use SSL/TLS for Redis connections in production environments.

7. **Audit Logging**:
   - Implement audit logging for sensitive lock operations, especially for high-value resources.

8. **Rate Limiting**:
   - Consider implementing rate limiting on lock acquisitions to prevent potential DoS attacks.

9. **Principle of Least Privilege**:
   - Ensure that applications only have access to the specific Redis keyspace they ne
10. **Regular Security Reviews**:
    - Conduct periodic security reviews of your locking implementation and usage patterns.

For more detailed security guidelines, refer to the Security Considerations document.
