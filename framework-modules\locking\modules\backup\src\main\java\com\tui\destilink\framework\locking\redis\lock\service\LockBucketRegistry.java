package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class LockBucketRegistry {
    private final LockComponentRegistry componentRegistry;
    private final RedisLockProperties globalProperties;

    public LockBucketBuilder builder(String bucketName) {
        LockBucketConfig defaultConfig = LockBucketConfig.builder()
                .leaseTime(globalProperties.getDefaults().getLeaseTime())
                .retryInterval(globalProperties.getDefaults().getRetryInterval())
                .stateKeyExpiration(globalProperties.getStateKeyExpiration())
                .build();
        return new LockBucketBuilder(componentRegistry, bucketName, defaultConfig);
    }
}
