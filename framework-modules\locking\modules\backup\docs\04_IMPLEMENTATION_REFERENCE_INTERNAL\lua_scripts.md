# Redis Locking Module: Lua Scripts Reference

## 1. Introduction

The Redis locking module relies exclusively on Lua scripts to ensure atomic operations and maintain consistency. All critical lock operations are implemented as Lua scripts to guarantee atomicity and prevent race conditions that could occur with multiple Redis commands.

This document provides a comprehensive reference for all Lua scripts used in the module, including their parameters, return values, and implementation details. For architectural context, see [Architecture Overview](../01_OVERVIEW_AND_CONCEPTS/architecture.md).

## 2. Lua-Only Architecture

### 2.1. Mandatory Requirement

All Redis operations for lock state, TTL, and metadata MUST go through Lua scripts. Direct Redis commands for lock operations are strictly prohibited in the framework.

### 2.2. Justification

1. **Atomicity**: Lua scripts ensure that complex multi-key operations are executed atomically, preventing race conditions and maintaining data consistency.
2. **Performance**: By executing multiple operations in a single Redis call, we reduce network overhead and improve overall performance.
3. **Consistency**: Centralized logic in Lua scripts ensures consistent behavior across all lock operations.
4. **Security**: Limiting operations to predefined scripts reduces the risk of arbitrary or malicious commands being executed.

## 3. Script Architecture Principles

### 3.1. Design Principles

- **Atomicity**: All scripts execute atomically within Redis
- **Idempotency**: Scripts are designed to be safely retryable
- **Error Handling**: Comprehensive error detection and reporting
- **Performance**: Optimized for minimal Redis operations
- **Consistency**: Consistent return value formats across all scripts
- **Lock-Type Awareness**: All scripts respect lock-type segments in key patterns
- **Virtual Thread Compatibility**: Non-blocking design for high-concurrency environments

### 3.2. Common Patterns

All scripts follow these patterns:

```lua
-- Parameter validation
local param = ARGV[1]
if not param then
    return {false, "MISSING_PARAMETER", "Parameter description"}
end

-- Lock-type awareness
local lockType = KEYS[1]:match("__locks__:(%w+):")
if not lockType then
    return {false, "INVALID_KEY_FORMAT", "Lock type not found in key"}
end

-- Main logic with error handling
local result = redis.call('COMMAND', key, value)
if not result then
    return {false, "OPERATION_FAILED", "Detailed error message"}
end

-- Success response
return {true, "SUCCESS", additionalData}
```

### 3.3. Standardized Return Conventions

All scripts return a consistent format:
```lua
{success, status, [data]}
```

Where:
- `success`: Boolean indicating operation success
- `status`: String status code (e.g., "SUCCESS", "LOCK_HELD", "NOT_OWNER")
- `data`: Optional additional data (varies by operation)

Error codes and messages are standardized across all scripts for consistent error handling.

### 3.4. Lock-Type Integration

- All scripts use the key format: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`
- Scripts include logic to handle different lock types
- Operations across different lock types are prevented

### 3.5. Virtual Thread Compatibility

- Scripts are designed for non-blocking execution in Virtual Thread environments
- Response caching is integrated with `requestUuid` for idempotency

## 4. Core Lock Scripts

### 4.1. Reentrant Lock Acquisition

**File**: `acquire_reentrant_lock.lua`

```lua
-- Acquire a reentrant lock with TTL and owner tracking
-- KEYS[1]: lock key
-- KEYS[2]: idempotency key (optional)
-- KEYS[3]: unlock notification channel
-- ARGV[1]: owner ID
-- ARGV[2]: TTL in milliseconds
-- ARGV[3]: current timestamp
-- ARGV[4]: idempotency TTL (optional)
-- ARGV[5]: requestUuid (for idempotency)

local lockKey = KEYS[1]
local idempotencyKey = KEYS[2]
local unlockChannel = KEYS[3]
local ownerId = ARGV[1]
local ttlMs = tonumber(ARGV[2])
local currentTime = tonumber(ARGV[3])
local idempotencyTtlMs = tonumber(ARGV[4])
local requestUuid = ARGV[5]

-- Validate parameters
if not lockKey or not ownerId or not ttlMs or not requestUuid then
    return {false, "INVALID_PARAMETERS", "Missing required parameters"}
end

-- Extract lock type from key
local lockType = lockKey:match("__locks__:(%w+):")
if not lockType then
    return {false, "INVALID_KEY_FORMAT", "Lock type not found in key"}
end

-- Check idempotency if key provided
if idempotencyKey then
    local cachedResult = redis.call('GET', idempotencyKey)
    if cachedResult then
        local result = cjson.decode(cachedResult)
        if result.requestUuid == requestUuid then
            return {true, "FROM_CACHE", result.data}
        end
    end
end

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local acquired = false
local reentrant = false
local resultData = {}

if not currentOwner then
    -- Lock is available
    redis.call('SET', lockKey, ownerId, 'PX', ttlMs)
    acquired = true
    resultData = {
        acquired = true,
        owner = ownerId,
        ttl = ttlMs,
        timestamp = currentTime,
        lockType = lockType
    }
elseif currentOwner == ownerId then
    -- Reentrant acquisition by same owner
    redis.call('PEXPIRE', lockKey, ttlMs)
    acquired = true
    reentrant = true
    resultData = {
        acquired = true,
        owner = ownerId,
        ttl = ttlMs,
        reentrant = true,
        timestamp = currentTime,
        lockType = lockType
    }
else
    -- Lock held by different owner
    local remainingTtl = redis.call('PTTL', lockKey)
    acquired = false
    resultData = {
        acquired = false,
        currentOwner = currentOwner,
        remainingTtl = remainingTtl,
        timestamp = currentTime,
        lockType = lockType
    }
end

-- Cache result if idempotency key provided
if idempotencyKey and idempotencyTtlMs then
    local cacheData = {
        success = acquired,
        status = acquired and "SUCCESS" or "LOCK_HELD",
        data = resultData,
        timestamp = currentTime,
        requestUuid = requestUuid
    }
    redis.call('SET', idempotencyKey, cjson.encode(cacheData), 'PX', idempotencyTtlMs)
end

local status = acquired and "SUCCESS" or "LOCK_HELD"
return {acquired, status, resultData}
```

### 4.2. Reentrant Lock Release

**File**: `release_reentrant_lock.lua`

```lua
-- Release a reentrant lock with notification
-- KEYS[1]: lock key
-- KEYS[2]: idempotency key (optional)
-- KEYS[3]: unlock notification channel
-- ARGV[1]: owner ID
-- ARGV[2]: current timestamp
-- ARGV[3]: idempotency TTL (optional)
-- ARGV[4]: requestUuid (for idempotency)

local lockKey = KEYS[1]
local idempotencyKey = KEYS[2]
local unlockChannel = KEYS[3]
local ownerId = ARGV[1]
local currentTime = tonumber(ARGV[2])
local idempotencyTtlMs = tonumber(ARGV[3])
local requestUuid = ARGV[4]

-- Validate parameters
if not lockKey or not ownerId or not requestUuid then
    return {false, "INVALID_PARAMETERS", "Missing required parameters"}
end

-- Extract lock type from key
local lockType = lockKey:match("__locks__:(%w+):")
if not lockType then
    return {false, "INVALID_KEY_FORMAT", "Lock type not found in key"}
end

-- Check idempotency if key provided
if idempotencyKey then
    local cachedResult = redis.call('GET', idempotencyKey)
    if cachedResult then
        local result = cjson.decode(cachedResult)
        if result.requestUuid == requestUuid then
            return {true, "FROM_CACHE", result.data}
        end
    end
end

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local released = false
local resultData = {}

if not currentOwner then
    -- Lock doesn't exist (already released or expired)
    released = true
    resultData = {
        released = true,
        reason = "ALREADY_RELEASED",
        timestamp = currentTime,
        lockType = lockType
    }
elseif currentOwner == ownerId then
    -- Valid release by owner
    redis.call('DEL', lockKey)
    
    -- Send unlock notification
    if unlockChannel then
        local notification = cjson.encode({
            lockKey = lockKey,
            previousOwner = ownerId,
            timestamp = currentTime,
            type = "UNLOCK",
            lockType = lockType
        })
        redis.call('PUBLISH', unlockChannel, notification)
    end
    
    released = true
    resultData = {
        released = true,
        reason = "RELEASED_BY_OWNER",
        previousOwner = ownerId,
        timestamp = currentTime,
        lockType = lockType
    }
else
    -- Attempted release by non-owner
    released = false
    resultData = {
        released = false,
        reason = "NOT_OWNER",
        currentOwner = currentOwner,
        attemptedBy = ownerId,
        timestamp = currentTime,
        lockType = lockType
    }
end

-- Cache result if idempotency key provided
if idempotencyKey and idempotencyTtlMs then
    local cacheData = {
        success = released,
        status = released and "SUCCESS" or "NOT_OWNER",
        data = resultData,
        timestamp = currentTime,
        requestUuid = requestUuid
    }
    redis.call('SET', idempotencyKey, cjson.encode(cacheData), 'PX', idempotencyTtlMs)
end

local status = released and "SUCCESS" or "NOT_OWNER"
return {released, status, resultData}
```

### 4.3. Lock TTL Extension (Watchdog)

**File**: `extend_lock_ttl.lua`

```lua
-- Extend lock TTL for watchdog renewal
-- KEYS[1]: lock key
-- KEYS[2]: idempotency key (optional)
-- ARGV[1]: expected owner ID
-- ARGV[2]: new TTL in milliseconds
-- ARGV[3]: current timestamp
-- ARGV[4]: idempotency TTL (optional)
-- ARGV[5]: requestUuid (for idempotency)

local lockKey = KEYS[1]
local idempotencyKey = KEYS[2]
local expectedOwner = ARGV[1]
local newTtlMs = tonumber(ARGV[2])
local currentTime = tonumber(ARGV[3])
local idempotencyTtlMs = tonumber(ARGV[4])
local requestUuid = ARGV[5]

-- Validate parameters
if not lockKey or not expectedOwner or not newTtlMs or not requestUuid then
    return {false, "INVALID_PARAMETERS", "Missing required parameters"}
end

-- Extract lock type from key
local lockType = lockKey:match("__locks__:(%w+):")
if not lockType then
    return {false, "INVALID_KEY_FORMAT", "Lock type not found in key"}
end

-- Check idempotency if key provided
if idempotencyKey then
    local cachedResult = redis.call('GET', idempotencyKey)
    if cachedResult then
        local result = cjson.decode(cachedResult)
        if result.requestUuid == requestUuid then
            return {true, "FROM_CACHE", result.data}
        end
    end
end

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)

if not currentOwner then
    local resultData = {
        reason = "Lock does not exist",
        timestamp = currentTime,
        lockType = lockType
    }
    return {false, "LOCK_NOT_EXISTS", resultData}
end

if currentOwner ~= expectedOwner then
    local resultData = {
        currentOwner = currentOwner,
        expectedOwner = expectedOwner,
        timestamp = currentTime,
        lockType = lockType
    }
    return {false, "NOT_OWNER", resultData}
end

-- Extend TTL
redis.call('PEXPIRE', lockKey, newTtlMs)
local actualTtl = redis.call('PTTL', lockKey)

local resultData = {
    owner = currentOwner,
    newTtl = actualTtl,
    requestedTtl = newTtlMs,
    timestamp = currentTime,
    lockType = lockType
}

-- Cache result if idempotency key provided
if idempotencyKey and idempotencyTtlMs then
    local cacheData = {
        success = true,
        status = "SUCCESS",
        data = resultData,
        timestamp = currentTime,
        requestUuid = requestUuid
    }
    redis.call('SET', idempotencyKey, cjson.encode(cacheData), 'PX', idempotencyTtlMs)
end

return {true, "SUCCESS", resultData}
```

## 5. Idempotency Integration

All scripts now support idempotency through the use of `requestUuid` and response caching. This ensures that operations are safely retryable without unintended side effects. The idempotency mechanism works as follows:

1. Each request includes a unique `requestUuid`.
2. Before performing the operation, the script checks if a cached result exists for the given `requestUuid`.
3. If a cached result is found, it is returned immediately without re-executing the operation.
4. If no cached result is found, the operation is executed, and the result is cached with the `requestUuid` for future requests.

This approach allows for efficient handling of retried requests, especially in high-concurrency Virtual Thread environments.

## 6. Virtual Thread Compatibility

The Lua scripts are designed to be compatible with Virtual Thread execution patterns:

1. **Non-blocking Design**: All operations within the scripts are designed to be non-blocking, ensuring efficient execution in high-concurrency environments.
2. **Minimal Lock Contention**: The use of atomic Lua scripts minimizes the time spent holding Redis locks, reducing contention in multi-threaded scenarios.
3. **Efficient Resource Usage**: By combining multiple operations into single Lua scripts, we reduce the number of network round-trips, which is particularly beneficial in Virtual Thread contexts.
4. **Idempotency Support**: The integration of `requestUuid` and response caching allows for safe retries, which is crucial in environments where operations may be interrupted or rescheduled across different threads.

These design choices ensure that the locking mechanism performs optimally in modern, high-concurrency application architectures leveraging Virtual Threads.