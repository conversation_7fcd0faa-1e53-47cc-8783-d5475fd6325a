-- KEYS[1] stampedLockDataKey (e.g., prefix:bucket:__locks__:{myStampedLock}:stamped)
-- KEYS[2] unlockChannelBaseName (e.g., <prefix>:<bucketName>:__unlock_channels__)
-- KEYS[3] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] stampPayload (e.g., ownerId:uuid for write, version for read)
-- ARGV[3] lockOwnerId
-- ARGV[4] mode (string "read" or "write")
-- ARGV[5] responseCacheTTLSeconds

-- Helper function to cache and return the result
local function cacheAndReturn(result)
    redis.call('set', KEYS[3], result, 'px', ARGV[5]);
    return result;
end

-- Check if we have a cached response for this request
local cachedResult = redis.call('get', KEYS[3]);
if cachedResult ~= false then
    return tonumber(cachedResult);
end;

-- Extract lock name from the key pattern for the unlock channel
local lockNameSuffix = string.match(KEYS[1], "{([^}]+)}:stamped$"); -- Extract {lockName} part
local unlockChannel = KEYS[2] .. ':' .. lockNameSuffix;

local result = 0;

-- Check if the lock exists
local lockExists = redis.call('exists', KEYS[1]);
if lockExists == 0 then
    return cacheAndReturn(0); -- Lock doesn't exist, can't unlock
end

if (ARGV[4] == 'write') then
    local writeOwnerId = redis.call('hget', KEYS[1], 'write_owner_id');
    if (writeOwnerId ~= false and writeOwnerId == ARGV[3]) then
        local count = tonumber(redis.call('hget', KEYS[1], 'write_reentrancy_count'));
        if (count == nil or count <= 1) then
            -- Fully releasing the write lock
            redis.call('hdel', KEYS[1], 'write_owner_id', 'write_reentrancy_count');
            -- Increment version on write unlock
            redis.call('hincrby', KEYS[1], 'version', 1);
            redis.call('publish', unlockChannel, 'STAMPED_WRITE_RELEASED');
            result = 1;
        else
            -- Decrement reentrancy count
            redis.call('hincrby', KEYS[1], 'write_reentrancy_count', -1);
            result = 0; -- Partially released
        end;
    end;
elseif (ARGV[4] == 'read') then
    local readerCount = tonumber(redis.call('hget', KEYS[1], 'read_holders'));
    if (readerCount ~= nil and readerCount > 0) then
        local newCount = redis.call('hincrby', KEYS[1], 'read_holders', -1);
        if (newCount == 0) then
            -- Last reader is leaving
            redis.call('hdel', KEYS[1], 'read_holders');
            redis.call('publish', unlockChannel, 'STAMPED_READ_RELEASED');
            result = 1;
        else
            result = 0; -- Partially released (other readers still hold the lock)
        end;
    end;
end;

return cacheAndReturn(result);