-- KEYS[1] lockName
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] lockOwnerId
-- ARGV[3] newState
-- ARGV[4] stateKeySuffix
-- ARGV[5] stateExpirationMs (string "nil" if not set)

local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    return tonumber(cachedResult);
end;

local currentOwner = redis.call('hget', KEYS[1], 'owner');
if (currentOwner == false or currentOwner ~= ARGV[2]) then
    redis.call('set', KEYS[2], 0, 'px', 300); -- Cache 0 for not owned
    return 0; -- Not held by this owner
end;

local stateKey = KEYS[1] .. ARGV[4];
redis.call('set', stateKey, ARGV[3]);
if (ARGV[5] ~= 'nil') then
    redis.call('pexpire', state<PERSON><PERSON>, ARGV[5]);
end;

redis.call('set', KEYS[2], 1, 'px', 300); -- Cache 1 for success
return 1;