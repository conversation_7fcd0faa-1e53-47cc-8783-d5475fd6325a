package com.tui.destilink.framework.locking.redis.lock.service;

/**
 * Interface for monitoring lock operations.
 * <p>
 * Implementations of this interface can be used to monitor lock acquisition,
 * release, and extension operations. This is useful for metrics collection,
 * logging, and debugging.
 * </p>
 * <p>
 * This is an optional extension point that allows applications to hook into
 * the lock lifecycle for monitoring purposes.
 * </p>
 */
public interface LockMonitor {

    /**
     * Called when a lock is acquired.
     *
     * @param lockName            The name of the lock
     * @param lockOwner           The owner identifier of the lock
     * @param acquiredAfterMillis Time in milliseconds it took to acquire the lock
     * @param retryCount          Number of retries before successful acquisition
     */
    void onLockAcquired(String lockName, String lockOwner, long acquiredAfterMillis, int retryCount);

    /**
     * Called when a lock acquisition fails.
     *
     * @param lockName          The name of the lock
     * @param lockOwner         The owner identifier of the lock
     * @param failedAfterMillis Time in milliseconds after which acquisition was
     *                          abandoned
     * @param retryCount        Number of retries before giving up
     */
    void onLockAcquisitionFailed(String lockName, String lockOwner, long failedAfter<PERSON>illis, int retryCount);

    /**
     * Called when a lock is released.
     *
     * @param lockName      The name of the lock
     * @param lockOwner     The owner identifier of the lock
     * @param heldForMillis Time in milliseconds the lock was held
     */
    void onLockReleased(String lockName, String lockOwner, long heldForMillis);

    /**
     * Called when a lock is extended.
     *
     * @param lockName          The name of the lock
     * @param lockOwner         The owner identifier of the lock
     * @param extendedForMillis Time in milliseconds the lock was extended for
     */
    void onLockExtended(String lockName, String lockOwner, long extendedForMillis);
}