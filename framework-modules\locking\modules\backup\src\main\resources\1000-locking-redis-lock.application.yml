destilink:
  fw:
    locking:
      redis:
        enabled: true
        lease-time: PT60S
        retry-interval: PT0.1S
        state-key-expiration: PT5M
        pub-sub-wait-timeout: PT5S
        watchdog:
          enabled: true
          min-lease-time-for-activation: PT10S
          schedule-fixed-delay: PT5S
        lock-owner-id-validation-regex: "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"
        max-lock-name-length: 255
        max-bucket-name-length: 100
        max-scope-length: 100