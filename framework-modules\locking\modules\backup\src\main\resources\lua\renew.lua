-- Script to renew a lock (extend lease time)
-- KEYS[1] = Lock key
-- ARGV[1] = Expected lock value (owner ID)
-- ARGV[2] = New lease time in milliseconds

local lockKey = KEYS[1]
local expectedValue = ARGV[1]
local newLeaseTimeMillis = tonumber(ARGV[2])

-- Only renew if the lock is still held by the expected owner
if redis.call("GET", lockKey) == expectedValue then
    return redis.call("PEXPIRE", lockKey, newLeaseTimeMillis)
else
    return 0 -- Lock not held by this owner or does not exist
end