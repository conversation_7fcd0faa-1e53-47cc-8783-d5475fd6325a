package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * Redis-based implementation of a reentrant lock.
 * <p>
 * This class provides a distributed, reentrant lock implementation using Redis.
 * Reentrancy is managed entirely in Redis through the lock value structure and
 * atomic operations, ensuring that all lock state is persisted and shared
 * across
 * application instances.
 * </p>
 * <p>
 * The lock is lightweight and stateless - all locking state including
 * reentrancy
 * count is maintained in Redis. This ensures proper behavior in distributed
 * environments where different application instances may need to work with the
 * same lock.
 * </p>
 * <p>
 * Key characteristics:
 * </p>
 * <ul>
 * <li>Fully reentrant - the same owner can acquire the lock multiple times</li>
 * <li>Distributed - works across multiple application instances</li>
 * <li>Stateless - no local state, all state managed in Redis</li>
 * <li>Async-first - core operations are asynchronous for better resource
 * utilization</li>
 * <li>Compatible with {@link java.util.concurrent.locks.Lock}</li>
 * </ul>
 *
 * @see AbstractRedisLock
 * @see com.tui.destilink.framework.locking.redis.lock.AsyncLock
 */
@Slf4j
public class RedisReentrantLock extends AbstractRedisLock {

    /**
     * Creates a new Redis-based reentrant lock with the specified key and default
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     */
    public RedisReentrantLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey);
    }

    /**
     * Creates a new Redis-based reentrant lock with the specified key and custom
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param lockTtlMillis       Time-to-live for the lock in milliseconds
     * @param retryIntervalMillis Interval between retry attempts in milliseconds
     * @param maxRetries          Maximum number of retry attempts
     */
    public RedisReentrantLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries) {
        super(redisLockOperations, lockOwnerSupplier, properties,
                lockKey, lockTtlMillis, retryIntervalMillis, maxRetries);
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        // Delegate to RedisLockOperations for reentrant lock acquisition
        return redisLockOperations.acquireLock(lockKey, ownerId, Duration.ofMillis(lockTtlMillis));
    }

    @Override
    protected CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout) {
        return doLockWithRetry(ownerId, effectiveTimeout, System.currentTimeMillis(), 0);
    }

    private CompletableFuture<Void> doLockWithRetry(String ownerId, Duration effectiveTimeout, long startTime,
            int attempts) {
        CompletableFuture<Void> result = new CompletableFuture<>();

        doTryLock(ownerId, effectiveTimeout).thenAccept(acquired -> {
            if (acquired) {
                log.debug("Lock acquired: lockKey={}, ownerId={}, attempts={}", lockKey, ownerId, attempts);
                result.complete(null);
                return;
            }

            long elapsedTime = System.currentTimeMillis() - startTime;
            long remainingTime = effectiveTimeout.toMillis() - elapsedTime;

            if (remainingTime <= 0 || attempts >= maxRetries) {
                log.warn("Lock acquisition failed: lockKey={}, ownerId={}, attempts={}, timeout={}", lockKey,
                        ownerId, attempts, effectiveTimeout);
                result.completeExceptionally(
                        new RuntimeException("Lock acquisition failed after timeout or max retries"));
                return;
            }

            // Schedule retry after delay
            CompletableFuture.delayedExecutor(
                    Math.min(retryIntervalMillis, remainingTime), TimeUnit.MILLISECONDS)
                    .execute(() -> {
                        doLockWithRetry(ownerId, effectiveTimeout, startTime, attempts + 1)
                                .thenAccept(result::complete)
                                .exceptionally(ex -> {
                                    result.completeExceptionally(ex);
                                    return null;
                                });
                    });
        }).exceptionally(ex -> {
            if (ex instanceof CompletionException && ex.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                result.completeExceptionally(new RuntimeException("Lock acquisition interrupted", ex.getCause()));
            } else {
                result.completeExceptionally(new RuntimeException("Error during lock acquisition", ex));
            }
            return null;
        });

        return result;
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        // Delegate to RedisLockOperations for lock release
        return redisLockOperations.releaseLock(lockKey, ownerId)
                .thenCompose(released -> {
                    if (released) {
                        return CompletableFuture.completedFuture(null);
                    } else {
                        return CompletableFuture.failedFuture(
                                new RuntimeException("Lock release failed - not owned by " + ownerId));
                    }
                });
    }

    @Override
    protected String getLockType() {
        return "RedisReentrantLock";
    }

    @Override
    protected CompletableFuture<Void> updateLockState(String ownerId) {
        // For reentrant lock, no additional state update is needed
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public boolean isReadLock() {
        return false;
    }

    @Override
    public boolean isWriteLock() {
        return true;
    }
}
