-- try_stamped_lock.lua
-- Attempts to acquire a stamped lock (read, write, or optimistic)
-- KEYS[1]: stampedLockDataKey (e.g., prefix:bucket:__locks__:{myStampedLock}:stamped)
-- KEYS[2]: responseCacheKey
-- ARGV[1]: requestUuid
-- ARGV[2]: leaseTimeMs (for "read" / "write")
-- ARGV[3]: lockOwnerId (for "read" / "write")
-- ARGV[4]: mode ("read", "write", "optimistic")
-- ARGV[5]: expectedStamp (not used in this version, for future CAS operations, pass "nil")
-- ARGV[6]: responseCacheTTLSeconds

local lockDataKey = KEYS[1]
local responseCacheKey = KEYS[2]

local requestUuid = ARGV[1]
local leaseTimeMs = tonumber(ARGV[2])
local ownerId = ARGV[3]
local mode = ARGV[4]
-- local expectedStamp = ARGV[5] -- Not used in this simplified version
local cacheTtl = tonumber(ARGV[6])

local function cache_and_return(value)
    if value == nil then
        redis.call('set', responseCacheKey, 'nil', 'px', cacheTtl * 1000)
    else
        redis.call('set', responseCacheKey, value, 'px', cacheTtl * 1000)
    end
    return value
end

local cached = redis.call('get', responseCacheKey)
if cached then
    if cached == 'nil' then return nil else return cached end
end

local version = redis.call('hget', lockDataKey, 'version')
if not version then -- Initialize if not exists
    redis.call('hmset', lockDataKey, 'version', 0, 'write_owner_id', '', 'write_reentrancy_count', 0, 'read_holders', 0)
    version = 0
else
    version = tonumber(version)
end

if mode == 'optimistic' then
    return cache_and_return('O:' .. version)
end

if mode == 'write' then
    local writeOwner = redis.call('hget', lockDataKey, 'write_owner_id')
    local readHolders = tonumber(redis.call('hget', lockDataKey, 'read_holders'))

    if (writeOwner and writeOwner ~= '' and writeOwner ~= ownerId) or (readHolders and readHolders > 0) then
        -- Held by another writer or readers exist
        return cache_and_return(redis.call('pttl', lockDataKey))
    end

    -- Acquire or re-enter write lock
    local newVersion = version
    if not (writeOwner and writeOwner == ownerId) then -- First time acquiring write by this owner
        newVersion = version + 1
        redis.call('hset', lockDataKey, 'version', newVersion)
        redis.call('hset', lockDataKey, 'write_owner_id', ownerId)
        redis.call('hset', lockDataKey, 'write_reentrancy_count', 1)
    else -- Re-entrant write
        redis.call('hincrby', lockDataKey, 'write_reentrancy_count', 1)
    end
    redis.call('pexpire', lockDataKey, leaseTimeMs)
    return cache_and_return('W:' .. ownerId .. ':' .. newVersion)
end

if mode == 'read' then
    local writeOwner = redis.call('hget', lockDataKey, 'write_owner_id')
    if writeOwner and writeOwner ~= '' and writeOwner ~= ownerId then
        -- Held by another writer (and not this owner)
        return cache_and_return(redis.call('pttl', lockDataKey))
    end

    -- Acquire read lock (even if this owner holds write lock)
    redis.call('hincrby', lockDataKey, 'read_holders', 1)
    redis.call('pexpire', lockDataKey, leaseTimeMs) -- Extend lease for main data key
    return cache_and_return('R:' .. version)
end

return cache_and_return(nil) -- Should not reach here with valid modes