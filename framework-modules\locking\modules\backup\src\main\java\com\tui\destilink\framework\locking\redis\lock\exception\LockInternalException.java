package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when an internal error occurs in the Redis lock
 * implementation.
 * <p>
 * This is a general-purpose exception for internal errors that don't fit into
 * more
 * specific exception categories. It can include implementation bugs, unexpected
 * states,
 * or other internal failures in the Redis lock mechanism.
 * </p>
 */
public class LockInternalException extends AbstractRedisLockException {

    private final String component;
    private final String operation;

    /**
     * Constructs a new LockInternalException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisLock",
     *                    "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param component   The internal component where the error occurred
     * @param operation   The operation that was being performed
     * @param message     Descriptive error message
     */
    public LockInternalException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String component, String operation, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.component = component;
        this.operation = operation;
    }

    /**
     * Constructs a new LockInternalException with the specified details and cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisLock",
     *                    "RedisStateLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param component   The internal component where the error occurred
     * @param operation   The operation that was being performed
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public LockInternalException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String component, String operation, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.component = component;
        this.operation = operation;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (Objects.nonNull(component)) {
            contextMap.put("lock.internal.component", component);
        }
        if (Objects.nonNull(operation)) {
            contextMap.put("lock.internal.operation", operation);
        }
    }
}