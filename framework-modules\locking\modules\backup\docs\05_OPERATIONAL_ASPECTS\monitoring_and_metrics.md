# Redis Locking Module: Monitoring and Metrics

## 1. Introduction

This document covers monitoring, metrics, and observability features of the Redis locking module. It provides guidance on tracking lock health, performance metrics, and operational insights for production environments.

For troubleshooting specific issues, see [Troubleshooting Guide](troubleshooting_guide.md). For performance optimization, see [Performance Tuning](performance_tuning.md).

## 2. Health Indicators

### 2.1. RedisLockHealthIndicator

The module provides a Spring Boot Actuator health indicator that monitors the overall health of the locking system.

**Configuration**:
```yaml
destilink:
  fw:
    locking:
      redis:
        health-indicator-enabled: true

management:
  endpoints:
    web:
      exposure:
        include: health
  endpoint:
    health:
      show-details: always
```

**Health Check Response**:
```json
{
  "status": "UP",
  "components": {
    "redisLock": {
      "status": "UP",
      "details": {
        "redisConnected": true,
        "watchdogActive": true,
        "monitoredLocks": 5,
        "lastWatchdogCycle": "2024-01-15T10:30:45Z",
        "lockBuckets": [
          {
            "bucketName": "user-operations",
            "activeLocks": 3,
            "scope": "APPLICATION_INSTANCE"
          },
          {
            "bucketName": "order-processing",
            "activeLocks": 2,
            "scope": "GLOBAL"
          }
        ]
      }
    }
  }
}
```

**Health Status Conditions**:
- **UP**: Redis connection healthy, watchdog active (if enabled)
- **DOWN**: Redis connection failed, critical watchdog errors
- **OUT_OF_SERVICE**: Module disabled or misconfigured

### 2.2. Custom Health Checks

You can implement custom health checks for specific lock buckets:

```java
@Component
@ConditionalOnProperty(prefix = "app.monitoring", name = "custom-lock-health", havingValue = "true")
public class CustomLockHealthIndicator implements HealthIndicator {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    @Override
    public Health health() {
        try {
            // Test critical lock acquisition
            var lockBucket = lockBucketRegistry.builder("health-check");
            var lock = lockBucket.lockConfig("test-lock")
                .reentrantLock()
                .withLeaseTime(Duration.ofSeconds(5))
                .withAcquireTimeout(Duration.ofSeconds(2))
                .build();
            
            boolean acquired = lock.tryAcquire();
            if (acquired) {
                lock.release();
                return Health.up()
                    .withDetail("lockAcquisition", "successful")
                    .withDetail("testTime", Instant.now())
                    .build();
            } else {
                return Health.down()
                    .withDetail("lockAcquisition", "failed")
                    .withDetail("reason", "unable to acquire test lock")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .withException(e)
                .build();
        }
    }
}
```

## 3. Metrics Integration

### 3.1. Micrometer Metrics

The module integrates with Micrometer to provide comprehensive metrics:

**Configuration**:
```yaml
management:
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      instance: ${spring.application.instance-id:unknown}
```

**Available Metrics**:

#### Lock Acquisition Metrics
```
# Lock acquisition attempts
redis_lock_acquisition_attempts_total{bucket_name, lock_type, result} counter

# Lock acquisition duration
redis_lock_acquisition_duration_seconds{bucket_name, lock_type} timer

# Lock hold duration
redis_lock_hold_duration_seconds{bucket_name, lock_type} timer

# Active locks gauge
redis_lock_active_locks{bucket_name, lock_type, scope} gauge
```

#### Watchdog Metrics
```
# Watchdog renewal attempts
redis_lock_watchdog_renewals_total{result} counter

# Watchdog renewal duration
redis_lock_watchdog_renewal_duration_seconds timer

# Monitored locks count
redis_lock_watchdog_monitored_locks gauge

# Watchdog cycle duration
redis_lock_watchdog_cycle_duration_seconds timer
```

#### Redis Operation Metrics
```
# Redis operation duration
redis_lock_redis_operation_duration_seconds{operation, script} timer

# Redis operation attempts
redis_lock_redis_operations_total{operation, script, result} counter

# Redis connection health
redis_lock_redis_connection_healthy gauge
```

#### Retry Metrics
```
# Retry attempts
redis_lock_retry_attempts_total{bucket_name, lock_type, attempt_number} counter

# Retry success rate
redis_lock_retry_success_rate{bucket_name, lock_type} gauge
```

### 3.2. Custom Metrics

You can add custom metrics for specific business use cases:

```java
@Component
@RequiredArgsConstructor
public class CustomLockMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter businessOperationLocks;
    private final Timer businessOperationDuration;
    
    @PostConstruct
    public void initMetrics() {
        businessOperationLocks = Counter.builder("business_operation_locks_total")
            .description("Total business operation locks acquired")
            .tag("operation_type", "unknown")
            .register(meterRegistry);
            
        businessOperationDuration = Timer.builder("business_operation_duration_seconds")
            .description("Duration of business operations with locks")
            .register(meterRegistry);
    }
    
    public void recordBusinessOperationLock(String operationType) {
        businessOperationLocks.increment(Tags.of("operation_type", operationType));
    }
    
    public Timer.Sample startBusinessOperationTimer() {
        return Timer.start(meterRegistry);
    }
}
```

### 3.3. Prometheus Integration

**Sample Prometheus Configuration** (`prometheus.yml`):
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'spring-boot-app'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
```

**Key Prometheus Queries**:

```promql
# Lock acquisition rate
rate(redis_lock_acquisition_attempts_total[5m])

# Lock acquisition success rate
rate(redis_lock_acquisition_attempts_total{result="success"}[5m]) / 
rate(redis_lock_acquisition_attempts_total[5m])

# Average lock hold time
rate(redis_lock_hold_duration_seconds_sum[5m]) / 
rate(redis_lock_hold_duration_seconds_count[5m])

# Watchdog renewal success rate
rate(redis_lock_watchdog_renewals_total{result="success"}[5m]) / 
rate(redis_lock_watchdog_renewals_total[5m])

# Active locks by bucket
sum by (bucket_name) (redis_lock_active_locks)

# Redis operation latency (95th percentile)
histogram_quantile(0.95, rate(redis_lock_redis_operation_duration_seconds_bucket[5m]))
```

## 4. Logging and Observability

### 4.1. Structured Logging

The module uses structured logging with MDC context:

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockOperationLogger {
    
    public void logLockAcquisition(String lockKey, String ownerId, Duration leaseTime, boolean success) {
        try (MDCCloseable mdc = MDC.putCloseable("lock.key", lockKey)) {
            MDC.put("lock.owner", ownerId);
            MDC.put("lock.lease_time", leaseTime.toString());
            MDC.put("lock.operation", "acquire");
            
            if (success) {
                log.info("Lock acquired successfully");
            } else {
                log.warn("Lock acquisition failed");
            }
        }
    }
    
    public void logLockRelease(String lockKey, String ownerId, Duration holdDuration) {
        try (MDCCloseable mdc = MDC.putCloseable("lock.key", lockKey)) {
            MDC.put("lock.owner", ownerId);
            MDC.put("lock.hold_duration", holdDuration.toString());
            MDC.put("lock.operation", "release");
            
            log.info("Lock released");
        }
    }
    
    public void logWatchdogRenewal(String lockKey, boolean success, int extensionCount) {
        try (MDCCloseable mdc = MDC.putCloseable("lock.key", lockKey)) {
            MDC.put("lock.operation", "watchdog_renewal");
            MDC.put("lock.extension_count", String.valueOf(extensionCount));
            
            if (success) {
                log.debug("Watchdog renewal successful");
            } else {
                log.warn("Watchdog renewal failed");
            }
        }
    }
}
```

### 4.2. Log Configuration

**Logback Configuration** (`logback-spring.xml`):
```xml
<configuration>
    <springProfile name="!local">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
    </springProfile>
    
    <springProfile name="local">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{lock.key:-}] - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>
    
    <!-- Lock-specific logging -->
    <logger name="com.tui.destilink.framework.locking.redis" level="INFO"/>
    <logger name="com.tui.destilink.framework.locking.redis.service.DefaultLockWatchdog" level="DEBUG"/>
    
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

### 4.3. Distributed Tracing

Integration with Spring Cloud Sleuth/Micrometer Tracing:

```java
@Component
@RequiredArgsConstructor
public class TracedLockOperations {
    
    private final Tracer tracer;
    
    @NewSpan("lock.acquire")
    public boolean acquireLockWithTracing(@SpanTag("lock.key") String lockKey,
                                        @SpanTag("lock.type") String lockType,
                                        Supplier<Boolean> lockOperation) {
        Span span = tracer.nextSpan()
            .name("redis-lock-acquire")
            .tag("lock.key", lockKey)
            .tag("lock.type", lockType)
            .start();
            
        try (Tracer.SpanInScope ws = tracer.withSpanInScope(span)) {
            boolean result = lockOperation.get();
            span.tag("lock.acquired", String.valueOf(result));
            return result;
        } catch (Exception e) {
            span.tag("error", e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }
}
```

## 5. Alerting

### 5.1. Prometheus Alerting Rules

**Sample Alerting Rules** (`redis-lock-alerts.yml`):
```yaml
groups:
  - name: redis-lock-alerts
    rules:
      - alert: RedisLockHighFailureRate
        expr: |
          (
            rate(redis_lock_acquisition_attempts_total{result="failure"}[5m]) / 
            rate(redis_lock_acquisition_attempts_total[5m])
          ) > 0.1
        for: 2m
        labels:
          severity: warning
          component: redis-lock
        annotations:
          summary: "High Redis lock acquisition failure rate"
          description: "Lock acquisition failure rate is {{ $value | humanizePercentage }} for bucket {{ $labels.bucket_name }}"
      
      - alert: RedisLockWatchdogDown
        expr: redis_lock_watchdog_monitored_locks > 0 and increase(redis_lock_watchdog_renewals_total[5m]) == 0
        for: 1m
        labels:
          severity: critical
          component: redis-lock
        annotations:
          summary: "Redis lock watchdog appears to be down"
          description: "Watchdog has {{ $value }} monitored locks but no renewal activity detected"
      
      - alert: RedisLockHighLatency
        expr: |
          histogram_quantile(0.95, 
            rate(redis_lock_acquisition_duration_seconds_bucket[5m])
          ) > 1.0
        for: 3m
        labels:
          severity: warning
          component: redis-lock
        annotations:
          summary: "High Redis lock acquisition latency"
          description: "95th percentile lock acquisition time is {{ $value }}s"
      
      - alert: RedisLockConnectionIssues
        expr: redis_lock_redis_connection_healthy == 0
        for: 30s
        labels:
          severity: critical
          component: redis-lock
        annotations:
          summary: "Redis connection issues detected"
          description: "Redis lock module cannot connect to Redis"
```

### 5.2. Application-Level Alerts

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockAlertManager {
    
    private final MeterRegistry meterRegistry;
    private final AlertNotificationService alertService; // Custom service
    
    private final AtomicLong consecutiveFailures = new AtomicLong(0);
    private static final long FAILURE_THRESHOLD = 10;
    
    @EventListener
    public void handleLockAcquisitionFailure(LockAcquisitionFailureEvent event) {
        long failures = consecutiveFailures.incrementAndGet();
        
        if (failures >= FAILURE_THRESHOLD) {
            alertService.sendAlert(Alert.builder()
                .severity(AlertSeverity.HIGH)
                .title("Consecutive Lock Acquisition Failures")
                .description(String.format(
                    "Lock bucket '%s' has failed %d consecutive acquisition attempts",
                    event.getBucketName(), failures))
                .component("redis-lock")
                .build());
        }
    }
    
    @EventListener
    public void handleLockAcquisitionSuccess(LockAcquisitionSuccessEvent event) {
        consecutiveFailures.set(0);
    }
    
    @EventListener
    public void handleWatchdogFailure(WatchdogRenewalFailureEvent event) {
        if (event.isConsecutiveFailure()) {
            alertService.sendAlert(Alert.builder()
                .severity(AlertSeverity.CRITICAL)
                .title("Watchdog Renewal Failures")
                .description(String.format(
                    "Watchdog failed to renew lock '%s' multiple times",
                    event.getLockKey()))
                .component("redis-lock-watchdog")
                .build());
        }
    }
}
```

## 6. Dashboards

### 6.1. Grafana Dashboard

**Sample Grafana Dashboard Configuration**:
```json
{
  "dashboard": {
    "title": "Redis Lock Module Monitoring",
    "panels": [
      {
        "title": "Lock Acquisition Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(redis_lock_acquisition_attempts_total[5m])",
            "legendFormat": "{{bucket_name}} - {{result}}"
          }
        ]
      },
      {
        "title": "Active Locks by Bucket",
        "type": "graph",
        "targets": [
          {
            "expr": "sum by (bucket_name) (redis_lock_active_locks)",
            "legendFormat": "{{bucket_name}}"
          }
        ]
      },
      {
        "title": "Lock Acquisition Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(redis_lock_acquisition_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, rate(redis_lock_acquisition_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Watchdog Activity",
        "type": "graph",
        "targets": [
          {
            "expr": "redis_lock_watchdog_monitored_locks",
            "legendFormat": "Monitored Locks"
          },
          {
            "expr": "rate(redis_lock_watchdog_renewals_total[5m])",
            "legendFormat": "Renewal Rate"
          }
        ]
      }
    ]
  }
}
```

### 6.2. Application Performance Monitoring (APM)

Integration with APM tools like New Relic, Datadog, or Elastic APM:

```java
@Component
@RequiredArgsConstructor
public class APMLockInstrumentation {
    
    private final NewRelicApi newRelic; // Example APM integration
    
    @EventListener
    public void recordLockMetrics(LockOperationEvent event) {
        Map<String, Object> attributes = Map.of(
            "lock.key", event.getLockKey(),
            "lock.bucket", event.getBucketName(),
            "lock.type", event.getLockType().name(),
            "lock.duration", event.getDuration().toMillis(),
            "lock.success", event.isSuccess()
        );
        
        newRelic.recordCustomEvent("RedisLockOperation", attributes);
    }
    
    @EventListener
    public void recordWatchdogMetrics(WatchdogRenewalEvent event) {
        Map<String, Object> attributes = Map.of(
            "lock.key", event.getLockKey(),
            "renewal.success", event.isSuccess(),
            "renewal.extension_count", event.getExtensionCount(),
            "renewal.duration", event.getDuration().toMillis()
        );
        
        newRelic.recordCustomEvent("RedisLockWatchdogRenewal", attributes);
    }
}
```

## 7. Performance Monitoring

### 7.1. Key Performance Indicators (KPIs)

Monitor these critical KPIs for lock performance:

1. **Lock Acquisition Success Rate**: Should be > 99%
2. **Lock Acquisition Latency (P95)**: Should be < 100ms
3. **Lock Hold Duration**: Monitor for locks held too long
4. **Watchdog Renewal Success Rate**: Should be > 99.9%
5. **Redis Operation Latency**: Should be < 10ms
6. **Active Lock Count**: Monitor for lock leaks

### 7.2. Performance Baselines

Establish performance baselines for different scenarios:

```yaml
# Performance baselines (example values)
performance:
  baselines:
    lock_acquisition:
      p50_latency: 5ms
      p95_latency: 20ms
      p99_latency: 50ms
      success_rate: 99.5%
    
    watchdog_renewal:
      cycle_duration: 100ms
      success_rate: 99.9%
      renewal_latency: 10ms
    
    redis_operations:
      script_execution: 5ms
      key_operations: 2ms
      pub_sub_latency: 15ms
```

## 8. Operational Runbooks

### 8.1. High Lock Failure Rate Response

**Symptoms**: Lock acquisition failure rate > 10%

**Investigation Steps**:
1. Check Redis connectivity and health
2. Verify Redis memory usage and eviction policies
3. Check for network issues between application and Redis
4. Review lock configuration (timeouts, retry settings)
5. Analyze lock contention patterns

**Mitigation**:
1. Increase retry attempts and intervals temporarily
2. Scale Redis resources if needed
3. Review application logic for unnecessary lock contention

### 8.2. Watchdog Issues Response

**Symptoms**: Watchdog renewals failing or stopped

**Investigation Steps**:
1. Check watchdog thread pool health
2. Verify Redis connectivity from watchdog
3. Review watchdog configuration settings
4. Check for application resource constraints

**Mitigation**:
1. Restart application if watchdog threads are stuck
2. Temporarily disable watchdog if causing issues
3. Adjust watchdog timing configuration

## 9. Monitoring Best Practices

### 9.1. Monitoring Strategy

1. **Layer Monitoring**: Monitor at application, framework, and infrastructure layers
2. **Proactive Alerting**: Set up alerts before issues impact users
3. **Trend Analysis**: Monitor trends over time, not just current values
4. **Context Correlation**: Correlate lock metrics with business metrics
5. **Regular Review**: Regularly review and adjust monitoring thresholds

### 9.2. Metric Collection Guidelines

1. **High-Cardinality Caution**: Be careful with high-cardinality labels
2. **Sampling Strategy**: Use sampling for high-volume metrics if needed
3. **Retention Policy**: Set appropriate retention periods for different metric types
4. **Cost Optimization**: Balance monitoring detail with storage costs

### 9.3. Dashboard Design

1. **User-Focused**: Design dashboards for specific user roles
2. **Actionable Insights**: Include metrics that lead to actionable insights
3. **Visual Hierarchy**: Use visual hierarchy to highlight important information
4. **Context Switching**: Minimize context switching between dashboards

This comprehensive monitoring and metrics guide provides the foundation for maintaining visibility into your Redis locking module's health and performance in production environments.