# Redis Locking Module: Final Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation, consolidated from previous planning phases and updated to reflect the Async-First design, explicit `redis-core` integration, Virtual Thread adoption, and refined operational semantics as per the Comprehensive Plan (Version 1.0, June 17, 2025).

*   **`AbstractRedisLock`**: Base Java class providing common functionality for all Redis lock implementations in the module. It orchestrates asynchronous `CompletableFuture`-based operations, leveraging Virtual Threads for execution.
*   **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod ID). These locks are typically eligible for watchdog lease renewal if their user-provided lease time exceeds the `safetyBufferMillis`.
*   **Async-First**: A core design principle of this module where all fundamental lock operations are asynchronous, returning `CompletableFuture`s and executed on Virtual Threads. Synchronous `java.util.concurrent.locks.Lock` methods are implemented as blocking wrappers around these asynchronous operations.
*   **`AsyncLock`**: An interface that extends `java.util.concurrent.locks.Lock`. It provides non-blocking, `CompletableFuture`-based asynchronous versions of standard lock operations, suitable for reactive programming models and high-concurrency scenarios to avoid platform thread blocking. All concrete lock implementations in this module must implement this interface.
*   **Atomic Operation**: An operation that is guaranteed to execute fully without interruption or interference from other operations, ensured by executing commands as Lua scripts on the Redis server. All atomic operations are executed via `redis-core`'s `ClusterCommandExecutor`.
*   **Bucket (Lock Bucket)**: A logical grouping or namespace for locks. Buckets allow for applying common default configurations (e.g., `defaults.leaseTime`, `defaults.retryInterval`) to a set of related locks, primarily configured programmatically via builders. The `LockOwnerSupplier` configured at the bucket level is a key factor in determining watchdog eligibility for locks within that bucket.
*   **Builder API**: A fluent API, starting with `LockBucketRegistry.builder(...)`, used to configure and create specific lock instances (e.g., `RedisReentrantLock`, `RedisStateLock`).
*   **`ClusterCommandExecutor`**: A critical component from `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` used for all Redis command executions within this module. It provides asynchronous, cluster-aware command execution capabilities, ensuring compliance with framework guidelines for Redis interaction.
*   **Distributed Lock**: A synchronization primitive used to coordinate access to shared resources among multiple processes or services running in a distributed environment.
*   **ExceptionMarkerProvider**: A Destilink Framework interface (`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`) implemented by custom exceptions to provide detailed contextual information for structured JSON logging, as per framework guidelines.
*   **`expiresAtMillis`**: The absolute Unix timestamp (in milliseconds) when a lock is currently set to expire in Redis, calculated by the Redis server using `redis.call('TIME')` and `PEXPIREAT`. This value is stored in the `lockDataKey` and reflects the actual expiry time of the `lockKey`.
*   **Hash Tag (Redis Cluster)**: A mechanism (`{{...}}`) used in Redis key names to ensure that multiple keys are allocated to the same hash slot, and thus the same node, in a Redis Cluster setup. Crucial for multi-key Lua scripts executed via `ClusterCommandExecutor`. The lock identifier part of the key (e.g., `{order123}`) serves this purpose.
*   **Idempotency Wrapper**: A pattern applied to all mutating Lua scripts. It involves checking a Redis response cache using a `requestUuid` before executing the core logic, and storing the operation's result in the cache (with a TTL defined by `responseCacheTtl`) upon completion. This ensures that retried operations are not executed multiple times.
*   **`Individual Read Lock Timeout Key`**: A Redis String key with an associated TTL, created for each specific instance of an acquired read lock within a `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{myRWLock}:<readerId>:<count>`). It manages the lease for that individual read access, contributing to the overall lifetime management of the main `RedisReadWriteLock` key. The key includes the `readwrite` lock-type segment.
*   **Lease Time (`defaults.leaseTime`)**: The default duration a developer *intends* for a lock to be held, configured globally or at bucket/instance level. This is a `relativeLeaseTimeMillis`. If the watchdog is active for the lock, it uses the `originalLeaseTimeMillis` (derived from this) to manage renewals. If the watchdog is not active, `relativeLeaseTimeMillis` directly determines the TTL of the lock key in Redis. For a `RedisReadWriteLock`, each individual read lock acquisition also establishes its own lease managed by an `Individual Read Lock Timeout Key`; the main `RedisReadWriteLock` key's TTL is then maintained as the maximum of its configured lease (potentially watchdog-extended to `safetyBufferMillis` or `userIntendedExpireTimeMillis`) and the maximum remaining TTL of any active individual read lock leases.
*   **`lockDataKey`**: A secondary Redis key, typically a Redis Hash, used to store additional metadata associated with a `lockKey`. This metadata includes `ownerId`, `originalLeaseTimeMillis` (the user's intended full lease duration), and `expiresAtMillis` (the current absolute expiry timestamp). Its key format includes the mandatory lock-type segment (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}:data`).
*   **`lockKey`**: The primary Redis key for the distributed lock itself (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}`). This key holds the `ownerId` as its value (or is a Hash for reentrant/complex locks) and has an associated expiry managed by `PEXPIREAT`. It includes a mandatory lock-type segment (e.g., `reentrant`, `stamped`).
*   **Lock-Type Segment**: A mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`, `readwrite`) that semantically isolates different lock types, preventing cross-type interference. It is part of `lockKey` and `lockDataKey`.
*   **`LockBucketConfig`**: A Java class (non-Spring managed) that holds the resolved, effective configuration settings for a specific lock bucket, after merging global defaults (`RedisLockProperties.Defaults`) and programmatic builder overrides.
*   **`LockComponentRegistry`**: A Spring-managed bean that acts as a central holder for shared, stateless locking services (like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`), making them available to lock instances and other internal components.
*   **`LockOwnerSupplier`**: An interface responsible for providing a unique identifier (`ownerId`) for the current lock owner (typically combining an application instance ID and a thread ID). A `DefaultLockOwnerSupplier` bean is provided. It also determines watchdog eligibility via `canUseWatchdog()`.
*   **`LockSemaphoreHolder`**: A helper class, managed per `lockKey` (likely via Guava Cache with weak values), used by `UnlockMessageListener` to manage waiting `CompletableFuture`s for that specific `lockKey`. It is integrated with `CompletableFuture` for asynchronous waits on Virtual Threads.
*   **`LockWatchdog`**: A Spring-managed bean that periodically extends the lease (TTL using `PEXPIREAT`) of active, application-instance-bound locks in Redis to prevent premature expiration. It is always active if the module is enabled, but only monitors locks whose `userProvidedLeaseTime > safetyBufferMillis` and are instance-bound. Its operations are performed asynchronously via `ClusterCommandExecutor`. It uses `watchdog.interval` and `watchdog.factor` for its scheduling and `safetyBufferMillis` calculation. It **never** modifies the `originalLeaseTimeMillis` stored in `lockDataKey`.
*   **Lua Scripts**: Scripts written in the Lua programming language that are executed atomically on the Redis server to perform complex lock operations (e.g., acquire, release, extend, state manipulation). All Lua script executions are handled by `RedisLockOperations` which in turn uses `ClusterCommandExecutor`. All mutating scripts implement the Idempotency Wrapper. They return structured responses including status, `expiresAtMillis`, and `originalLeaseTimeMillis`.
*   **MDC (Mapped Diagnostic Context)**: A logging mechanism used with SLF4J to enrich log messages with contextual data (e.g., `lock.name`, `lock.operation`). Managed via `LockContextDecorator`, adhering to framework logging guidelines, and propagated to Virtual Threads.
*   **Non-Polling Wait**: A mechanism where Virtual Threads waiting for a lock do not continuously check its status. Instead, their associated `CompletableFuture` waits to be signaled when the lock is released (via Redis Pub/Sub and `LockSemaphoreHolder`). This is the primary waiting strategy. Registration with `UnlockMessageListenerManager` occurs *before* the first Redis acquisition attempt.
*   **`originalLeaseTimeMillis`**: The `relativeLeaseTimeMillis` value that was *last explicitly set by a user-initiated lock acquisition or extension*. This value is stored persistently in Redis (in `lockDataKey`) and is used by the `LockWatchdog` to determine the intended full lease duration for renewal cycles (i.e., `userIntendedExpireTimeMillis`). It is **never** modified by the watchdog itself.
*   **Override Precedence**: The order in which configuration settings are applied: Instance-specific settings (via builder methods) > Programmatic Bucket Configuration (via `LockBucketBuilder`) > Global Configuration (`RedisLockProperties.Defaults`).
*   **`ownerId`**: A unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). This identifies which application instance and thread/process holds the lock.
*   **Pub/Sub (Publish/Subscribe)**: A Redis messaging paradigm used by `UnlockMessageListener` instances to receive notifications when locks are released, enabling efficient, non-polling waits. Messages contain only `UnlockType`; `lockName` is derived from the channel.
*   **RedisCoreProperties**: The configuration properties class (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java`) from the `redis-core` module. This module uses these properties to ensure consistent Redis client configuration across the framework.
*   **Reentrant Lock**: A lock that can be acquired multiple times by the same owner (e.g., the same thread) without deadlocking. Each `lock()` call must be matched by an `unlock()` call. Reentrancy data (owner, count) is stored in a Redis Hash (`lockKey` for reentrant type).
*   **`RedisLockAutoConfiguration`**: The Spring Boot `@AutoConfiguration` class responsible for setting up all the necessary beans for the locking module, strictly adhering to framework guidelines (e.g., no `@ComponentScan`, explicit `@Bean` definitions).
*   **`RedisLockErrorHandler`**: A Spring-managed bean responsible for translating low-level Redis exceptions (often from `ClusterCommandExecutor`) into specific, contextual `AbstractRedisLockException` subtypes.
*   **`RedisLockOperations`**: A Spring-managed bean that abstracts direct Redis communication for lock-specific commands. **Crucially, it delegates all Redis command executions to `ClusterCommandExecutor` from `redis-core`, ensuring an asynchronous interaction model.** It is also responsible for generating the `requestUuid` for idempotency and managing internal retries for individual Redis operations.
*   **`RedisLockProperties`**: A Spring `@ConfigurationProperties` class that binds global settings from YAML files (e.g., `destilink.fw.locking.redis.*`). Contains nested `WatchdogProperties` and `Defaults` classes.
*   **`relativeLeaseTimeMillis`**: The duration (in milliseconds) requested by the user for a lock's lease (e.g., from `defaults.leaseTime`). This is a relative value (e.g., "30 seconds from now"). It is the input to lock acquisition and extension operations.
*   **`requestUuid`**: A unique identifier (UUID) generated centrally by `RedisLockOperationsImpl` for each logical lock operation (e.g., a single `tryLock` call, including its internal application-level retries). This UUID serves as the key for the centralized idempotency mechanism (Response Cache), ensuring that if the same logical operation is retried, it will not be executed multiple times. The same `requestUuid` is used for all retry attempts of a single logical operation.
*   **Response Cache**: A centralized Redis-based cache system that stores the results of mutating lock operations, keyed by `requestUuid`. This cache is managed entirely within Lua scripts (Idempotency Wrapper) and provides the foundation for the idempotency mechanism. The TTL for these cache entries is `responseCacheTtl`.
*   **Retry Interval (`defaults.retryInterval`)**: The duration `AbstractRedisLock`'s internal logic waits (via `Thread.sleep()` on a Virtual Thread) after a semaphore wait (which might have timed out based on the current lock holder's TTL) before re-attempting to acquire the lock via Lua script. This also applies to retries of individual Redis operations within `RedisLockOperationsImpl`.
*   **`safetyBufferMillis`**: A calculated duration (`watchdog.interval * watchdog.factor`) used by the `LockWatchdog`. It determines the minimum `userLeaseTime` for watchdog eligibility. For eligible locks, the watchdog aims to maintain a TTL of at least `safetyBufferMillis` until the "final leg" of the user's intended lease.
*   **`ScriptLoader`**: A Spring-managed bean that loads and caches all Lua scripts from the classpath at application startup.
*   **StateLock (`RedisStateLock`)**: A type of lock that, in addition to standard locking, also manages and potentially gates access based on an associated "state" value stored in Redis. Its keys include the `state` lock-type segment.
*   **StampedLock (`RedisStampedLock`)**: A lock type that provides optimistic read locking and pessimistic write locking, using a "stamp" or version number to detect intervening writes. Its keys include the `stamped` lock-type segment.
*   **TTL (Time-To-Live)**: The actual expiration time set on a key in Redis, managed via `PEXPIREAT`. For locks managed by the watchdog, this is typically `safetyBufferMillis` or `userIntendedExpireTimeMillis` during the final leg. For locks not managed by the watchdog, this is based on `relativeLeaseTimeMillis`.
*   **`UnlockMessageListener`**: A component, managed by `UnlockMessageListenerManager` and instantiated per lock bucket. It subscribes to a Redis Pub/Sub channel pattern specific to its designated bucket (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`). Upon receiving a message on a specific channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`), it derives the `lockName` and `lockType` from the channel and parses the `UnlockType` from the message payload (which contains only the `UnlockType` string). It then signals the appropriate `LockSemaphoreHolder` or completes a `CompletableFuture` for asynchronous waiters.
*   **`UnlockMessageListenerManager`**: A Spring-managed bean that creates and manages the lifecycle of `UnlockMessageListener` instances (typically one per configured lock bucket).
*   **`UnlockType`**: A string constant published as the message payload to a specific lock's Pub/Sub unlock channel. It indicates the nature of the unlock event (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED_WAKEN_ALL"). The `UnlockMessageListener` uses this type, along with the `lockName` and `lockType` derived from the channel, to apply optimized waking strategies for waiting `CompletableFuture`s.
*   **Virtual Threads**: Lightweight threads provided by the JVM (Project Loom) used by this module to execute all lock operations asynchronously, preventing platform thread blockage and improving scalability. MDC context is propagated to these threads.
*   **Watchdog Factor (`watchdog.factor`)**: A multiplier used with `watchdog.interval` to calculate `safetyBufferMillis`.
*   **Watchdog Interval (`watchdog.interval`)**: The independent duration at which the `LockWatchdog`'s scheduled task runs to check and potentially renew monitored locks.