package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when a Redis command or script execution fails.
 * <p>
 * This exception is thrown when a Redis command or Lua script execution
 * fails during lock operations.
 * </p>
 */
public class LockCommandException extends AbstractRedisLockException {

    private final String commandName;
    private final String arguments;

    /**
     * Constructs a new LockCommandException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param commandName The name of the Redis command or script that failed
     * @param arguments   String representation of the arguments passed to the
     *                    command
     * @param message     Descriptive error message
     */
    public LockCommandException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String commandName, String arguments, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.commandName = commandName;
        this.arguments = arguments;
    }

    /**
     * Constructs a new LockCommandException with the specified details and cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param commandName The name of the Redis command or script that failed
     * @param arguments   String representation of the arguments passed to the
     *                    command
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public LockCommandException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String commandName, String arguments, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.commandName = commandName;
        this.arguments = arguments;
    }

    /**
     * Gets the name of the Redis command or script that failed.
     *
     * @return The command or script name
     */
    public String getCommandName() {
        return commandName;
    }

    /**
     * Gets the string representation of the arguments passed to the command.
     *
     * @return The command arguments
     */
    public String getArguments() {
        return arguments;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (commandName != null) {
            if (commandName.contains("script")) {
                contextMap.put("redis.scriptName", commandName);
            } else {
                contextMap.put("redis.commandName", commandName);
            }
        }

        if (arguments != null) {
            contextMap.put("redis.arguments", arguments);
        }
    }
}