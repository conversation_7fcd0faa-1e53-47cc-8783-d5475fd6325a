package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.HashMap;
import java.util.Map;
import io.lettuce.core.RedisCommandExecutionException;

/**
 * Exception thrown when a lock acquisition fails (not due to timeout or
 * interruption).
 * <p>
 * This exception typically wraps exceptions from the ClusterCommandExecutor or
 * Lua script errors
 * that occur during the lock acquisition process.
 * </p>
 */
public class LockAcquisitionException extends AbstractRedisLockException {

    private final String redisError;

    /**
     * Constructs a new LockAcquisitionException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param redisError  The specific Redis error message or Lua script error
     * @param message     Descriptive error message
     */
    public LockAcquisitionException(String lockName, String lockType, String lockOwnerId, String redisError,
            String message) {
        super(lockName, lockType, lockOwnerId, null, message); // requestUuid is null
        this.redisError = redisError;
    }

    /**
     * Constructs a new LockAcquisitionException with the specified details and
     * cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param redisError  The specific Redis error message or Lua script error
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    public LockAcquisitionException(String lockName, String lockType, String lockOwnerId, String redisError,
            String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, null, message, cause); // requestUuid is null
        this.redisError = redisError;
    }

    /**
     * Constructs a new LockAcquisitionException with a message and cause.
     *
     * @param lockName    The full Redis key of the lock involved.
     * @param lockType    The specific type of lock.
     * @param lockOwnerId The ID of the owner attempting the operation.
     * @param message     Descriptive error message.
     * @param cause       The underlying cause of this exception.
     */
    public LockAcquisitionException(String lockName, String lockType, String lockOwnerId, String message,
            Throwable cause) {
        super(lockName, lockType, lockOwnerId, null, message, cause); // requestUuid is null
        this.redisError = (cause instanceof RedisCommandExecutionException) ? cause.getMessage() : null;
    }

    /**
     * Constructs a new LockAcquisitionException with just a message.
     *
     * @param lockName    The full Redis key of the lock involved.
     * @param lockType    The specific type of lock.
     * @param lockOwnerId The ID of the owner attempting the operation.
     * @param message     Descriptive error message.
     */
    public LockAcquisitionException(String lockName, String lockType, String lockOwnerId, String message) {
        super(lockName, lockType, lockOwnerId, null, message); // requestUuid is null
        this.redisError = null;
    }

    /**
     * Returns the specific Redis error message or Lua script error, if available.
     *
     * @return The Redis error message, or null if not applicable.
     */
    public String getRedisError() {
        return redisError;
    }

    public Map<String, Object> getAdditionalContext() {
        Map<String, Object> context = new HashMap<>();
        context.put("lockName", getLockName());
        context.put("lockType", getLockType());
        if (getLockOwnerId() != null) {
            context.put("lockOwnerId", getLockOwnerId());
        }
        if (redisError != null) {
            context.put("redisError", redisError);
        }
        return context;
    }
}