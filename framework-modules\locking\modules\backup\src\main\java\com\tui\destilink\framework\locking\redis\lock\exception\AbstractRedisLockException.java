package com.tui.destilink.framework.locking.redis.lock.exception;

import com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException;
import net.logstash.logback.marker.Markers;
import org.slf4j.Marker;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Base exception class for all Redis lock-related exceptions.
 * <p>
 * This class provides common contextual information for all lock exceptions and
 * integrates
 * with the Destilink Framework's structured logging capabilities via the
 * ExceptionMarkerProvider interface.
 * </p>
 */
public abstract class AbstractRedisLockException extends MarkerNestedRuntimeException {

    private final String lockName;
    private final String lockType;
    private final String lockOwnerId;
    private final String requestUuid;

    /**
     * Constructs a new AbstractRedisLockException with the specified details.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     */
    protected AbstractRedisLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message) {
        super(createMarker(lockName, lockType, lockOwnerId, requestUuid), message);
        this.lockName = lockName;
        this.lockType = lockType;
        this.lockOwnerId = lockOwnerId;
        this.requestUuid = requestUuid;
    }

    /**
     * Constructs a new AbstractRedisLockException with the specified details and
     * cause.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId The ID of the owner attempting the operation (can be null)
     * @param requestUuid The unique ID for the lock operation attempt (can be null)
     * @param message     Descriptive error message
     * @param cause       The underlying cause of this exception
     */
    protected AbstractRedisLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String message, Throwable cause) {
        super(createMarker(lockName, lockType, lockOwnerId, requestUuid), message, cause);
        this.lockName = lockName;
        this.lockType = lockType;
        this.lockOwnerId = lockOwnerId;
        this.requestUuid = requestUuid;
    }

    /**
     * Creates a marker with the common lock context fields.
     *
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock
     * @param lockOwnerId The ID of the owner attempting the operation
     * @param requestUuid The unique ID for the lock operation attempt
     * @return A marker with the common lock context fields
     */
    private static Marker createMarker(String lockName, String lockType, String lockOwnerId, String requestUuid) {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("lock.name", lockName);
        contextMap.put("lock.type", lockType);

        if (lockOwnerId != null) {
            contextMap.put("lock.ownerId", lockOwnerId);
        }

        if (requestUuid != null) {
            contextMap.put("lock.requestUuid", requestUuid);
        }

        // Remove null values to avoid issues with Markers.appendEntries
        contextMap.values().removeIf(Objects::isNull);

        return Markers.appendEntries(contextMap);
    }

    /**
     * Gets the lock name.
     *
     * @return The full Redis key of the lock involved
     */
    public String getLockName() {
        return lockName;
    }

    /**
     * Gets the lock type.
     *
     * @return The specific type of lock
     */
    public String getLockType() {
        return lockType;
    }

    /**
     * Gets the lock owner ID.
     *
     * @return The ID of the owner attempting the operation
     */
    public String getLockOwnerId() {
        return lockOwnerId;
    }

    /**
     * Gets the request UUID.
     *
     * @return The unique ID for the lock operation attempt
     */
    public String getRequestUuid() {
        return requestUuid;
    }

    /**
     * Populates specific markers for subclasses.
     * This method is called by the getMarker() method to allow subclasses to add
     * their specific context.
     *
     * @param contextMap The map to populate with specific context
     */
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // Base implementation does nothing; overridden by subclasses
    }

    @Override
    public Marker getMarker() {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("lock.name", this.lockName);
        contextMap.put("lock.type", this.lockType);

        if (this.lockOwnerId != null) {
            contextMap.put("lock.ownerId", this.lockOwnerId);
        }

        if (this.requestUuid != null) {
            contextMap.put("lock.requestUuid", this.requestUuid);
        }

        populateSpecificMarkers(contextMap);

        // Remove null values to avoid issues with Markers.appendEntries
        contextMap.values().removeIf(Objects::isNull);

        return Markers.appendEntries(contextMap);
    }
}