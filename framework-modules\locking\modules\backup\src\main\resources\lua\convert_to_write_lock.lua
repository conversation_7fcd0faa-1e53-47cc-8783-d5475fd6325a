-- KEYS[1] stampedLockDataKey (e.g., prefix:bucket:__locks__:{myStampedLock}:stamped)
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] readStampPayload (the version string from the read stamp being converted)
-- ARGV[3] lockOwnerId
-- ARGV[4] newLeaseTimeMs
-- ARGV[5] responseCacheTTLSeconds

local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    if cachedResult == 'nil' then return nil else return cachedResult end;
end;

local currentVersion = redis.call('hget', KEYS[1], 'version');
local readHolders = tonumber(redis.call('hget', KEYS[1], 'read_holders'));
local writeOwnerId = redis.call('hget', KEYS[1], 'write_owner_id');

if (currentVersion == false or currentVersion ~= ARGV[2] or writeOwnerId ~= false) then
    -- Version mismatch, or write lock already held
    redis.call('set', KEYS[2], '0', 'px', ARGV[5]); -- Cache 0 for failure
    return '0';
end;

if (readHolders == nil or readHolders == 0) then
    -- No read locks held, or invalid state for conversion
    redis.call('set', KEYS[2], '0', 'px', ARGV[5]); -- Cache 0 for failure
    return '0';
end;

-- Check if this owner holds the read lock (simplified: assumes single reader for conversion)
-- In a more complex scenario, you might need to track individual reader IDs in the hash.
-- For now, if readHolders is 1, and no writer, we assume this is the reader.
if (readHolders == 1) then
    redis.call('hincrby', KEYS[1], 'version', 1); -- Increment version for write lock
    redis.call('hset', KEYS[1], 'write_owner_id', ARGV[3]);
    redis.call('hset', KEYS[1], 'write_reentrancy_count', 1);
    redis.call('hdel', KEYS[1], 'read_holders'); -- Remove read holders count
    redis.call('pexpire', KEYS[1], ARGV[4]); -- Set new lease for write lock

    local newStamp = 'W:' .. ARGV[3] .. ':' .. redis.call('hget', KEYS[1], 'version');
    redis.call('set', KEYS[2], newStamp, 'px', ARGV[5]);
    return newStamp;
else
    -- Multiple readers, cannot convert
    redis.call('set', KEYS[2], '0', 'px', ARGV[5]); -- Cache 0 for failure
    return '0';
end;