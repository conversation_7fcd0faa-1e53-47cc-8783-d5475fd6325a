# Redis Locking Module: Usage Examples

## 1. Introduction

This document provides comprehensive usage examples for the `locking-redis-lock` module, demonstrating how to use different lock types in various scenarios. These examples build upon the configuration principles outlined in [Configuration Guide](configuration.md) and the architectural concepts described in [Architecture Overview](../01_OVERVIEW_AND_CONCEPTS/architecture.md).

For detailed information about lock types and their behaviors, see [Lock Types Deep Dive](../01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md).

## 2. Basic Setup and Dependency Injection

### 2.1. Spring Boot Application Setup

```java
@SpringBootApplication
public class MyApplication {
    public static void main(String[] args) {
        SpringApplication.run(MyApplication.class, args);
    }
}
```

### 2.2. Service Class with Lock Registry Injection

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderProcessingService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    // Service methods using locks will be shown in subsequent examples
}
```

### 2.3. Configuration Properties (application.yml)

```yaml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        defaults:
          lease-time: PT60S
          retry-interval: PT0.1S
          max-retries: 3
          acquire-timeout: PT5S
        watchdog:
          enabled: true
          interval: PT5S
          min-lease-time-for-activation: PT10S
```

## 3. Reentrant Lock Examples

### 3.1. Basic Reentrant Lock Usage

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class UserAccountService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void updateUserBalance(String userId, BigDecimal amount) {
        // Create a reentrant lock for the specific user account
        RedisReentrantLock accountLock = lockBucketRegistry
            .builder("user-accounts", "payment-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("account-" + userId)
            .reentrantLock()
            .withLeaseTime(Duration.ofSeconds(30))
            .build();
        
        try {
            // Acquire the lock with timeout
            if (accountLock.tryLock(5, TimeUnit.SECONDS)) {
                try {
                    log.info("Acquired lock for user account: {}", userId);
                    
                    // Critical section - update user balance
                    UserAccount account = getUserAccount(userId);
                    account.setBalance(account.getBalance().add(amount));
                    saveUserAccount(account);
                    
                    log.info("Successfully updated balance for user: {}", userId);
                } finally {
                    accountLock.unlock();
                    log.info("Released lock for user account: {}", userId);
                }
            } else {
                log.warn("Failed to acquire lock for user account: {} within timeout", userId);
                throw new LockAcquisitionException("Could not acquire account lock");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Lock acquisition interrupted", e);
        }
    }
    
    // Reentrant behavior example
    public void transferFunds(String fromUserId, String toUserId, BigDecimal amount) {
        RedisReentrantLock fromAccountLock = lockBucketRegistry
            .builder("user-accounts", "payment-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("account-" + fromUserId)
            .reentrantLock()
            .build();
        
        try {
            if (fromAccountLock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    log.info("Acquired lock for source account: {}", fromUserId);
                    
                    // This call will reenter the same lock if processing the same user
                    updateUserBalance(fromUserId, amount.negate()); // Debit
                    updateUserBalance(toUserId, amount); // Credit (different lock)
                    
                } finally {
                    fromAccountLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Transfer interrupted", e);
        }
    }
    
    private UserAccount getUserAccount(String userId) { /* implementation */ return null; }
    private void saveUserAccount(UserAccount account) { /* implementation */ }
}
```

### 3.2. Reentrant Lock with Custom Configuration

```java
@Service
@RequiredArgsConstructor
public class InventoryService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void reserveInventory(String productId, int quantity) {
        // Custom configuration for inventory locks
        RedisReentrantLock inventoryLock = lockBucketRegistry
            .builder("inventory", "order-service-v2")
            .withScope(LockScope.GLOBAL) // Global scope for inventory
            .withDefaultLeaseTime(Duration.ofMinutes(2)) // Longer lease for inventory operations
            .withDefaultRetryInterval(Duration.ofMillis(50)) // Faster retries
            .withDefaultMaxRetries(10) // More retries for high contention
            .lockConfig("product-" + productId)
            .reentrantLock()
            .withLeaseTime(Duration.ofMinutes(1)) // Override bucket default
            .build();
        
        inventoryLock.lock(); // Block until acquired
        try {
            // Critical inventory operations
            InventoryItem item = getInventoryItem(productId);
            if (item.getAvailableQuantity() >= quantity) {
                item.setReservedQuantity(item.getReservedQuantity() + quantity);
                item.setAvailableQuantity(item.getAvailableQuantity() - quantity);
                saveInventoryItem(item);
            } else {
                throw new InsufficientInventoryException("Not enough inventory");
            }
        } finally {
            inventoryLock.unlock();
        }
    }
    
    private InventoryItem getInventoryItem(String productId) { /* implementation */ return null; }
    private void saveInventoryItem(InventoryItem item) { /* implementation */ }
}
```

## 4. State Lock Examples

### 4.1. Basic State Lock Usage

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderProcessingService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void processOrder(String orderId) {
        // State lock for order processing workflow
        RedisStateLock orderStateLock = lockBucketRegistry
            .builder("order-processing", "order-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("order-" + orderId)
            .stateLock()
            .withLeaseTime(Duration.ofMinutes(5)) // Long-running process
            .build();
        
        try {
            // Try to transition from PENDING to PROCESSING
            if (orderStateLock.tryLockWithState("PENDING", "PROCESSING", 30, TimeUnit.SECONDS)) {
                try {
                    log.info("Started processing order: {}", orderId);
                    
                    // Process the order
                    processOrderInternal(orderId);
                    
                    // Transition to COMPLETED
                    orderStateLock.transitionState("COMPLETED");
                    log.info("Order processing completed: {}", orderId);
                    
                } catch (Exception e) {
                    // Transition to FAILED on error
                    orderStateLock.transitionState("FAILED");
                    log.error("Order processing failed: {}", orderId, e);
                    throw e;
                } finally {
                    orderStateLock.unlock();
                }
            } else {
                log.warn("Order {} is not in PENDING state or lock acquisition failed", orderId);
                throw new OrderProcessingException("Cannot process order in current state");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Order processing interrupted", e);
        }
    }
    
    public String getOrderState(String orderId) {
        RedisStateLock orderStateLock = lockBucketRegistry
            .builder("order-processing", "order-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("order-" + orderId)
            .stateLock()
            .build();
        
        // Read current state without acquiring lock
        return orderStateLock.getCurrentState().orElse("UNKNOWN");
    }
    
    private void processOrderInternal(String orderId) {
        // Simulate order processing
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }
}
```

### 4.2. State Lock with Complex Workflow

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentProcessingService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void processPayment(String paymentId) {
        RedisStateLock paymentLock = lockBucketRegistry
            .builder("payments", "payment-service-v1")
            .withScope(LockScope.GLOBAL) // Global scope for payments
            .withDefaultStateKeyExpiration(Duration.ofHours(24)) // Keep state for 24 hours
            .lockConfig("payment-" + paymentId)
            .stateLock()
            .withLeaseTime(Duration.ofMinutes(10))
            .build();
        
        try {
            // Multi-step payment processing with state transitions
            if (paymentLock.tryLockWithState("PENDING", "VALIDATING", 10, TimeUnit.SECONDS)) {
                try {
                    validatePayment(paymentId);
                    paymentLock.transitionState("VALIDATED");
                    
                    processPaymentWithProvider(paymentId);
                    paymentLock.transitionState("PROCESSING");
                    
                    confirmPayment(paymentId);
                    paymentLock.transitionState("COMPLETED");
                    
                } catch (PaymentValidationException e) {
                    paymentLock.transitionState("VALIDATION_FAILED");
                    throw e;
                } catch (PaymentProcessingException e) {
                    paymentLock.transitionState("PROCESSING_FAILED");
                    throw e;
                } catch (Exception e) {
                    paymentLock.transitionState("FAILED");
                    throw e;
                } finally {
                    paymentLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Payment processing interrupted", e);
        }
    }
    
    public boolean canRetryPayment(String paymentId) {
        RedisStateLock paymentLock = lockBucketRegistry
            .builder("payments", "payment-service-v1")
            .withScope(LockScope.GLOBAL)
            .lockConfig("payment-" + paymentId)
            .stateLock()
            .build();
        
        String currentState = paymentLock.getCurrentState().orElse("UNKNOWN");
        return "VALIDATION_FAILED".equals(currentState) || "PROCESSING_FAILED".equals(currentState);
    }
    
    private void validatePayment(String paymentId) { /* implementation */ }
    private void processPaymentWithProvider(String paymentId) { /* implementation */ }
    private void confirmPayment(String paymentId) { /* implementation */ }
}
```

## 5. Semaphore Lock Examples

### 5.1. Basic Semaphore Usage

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class ApiRateLimitService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void processApiRequest(String clientId, String endpoint) {
        // Semaphore to limit concurrent API calls per client
        RedisSemaphoreLock rateLimitSemaphore = lockBucketRegistry
            .builder("api-rate-limits", "api-gateway-v1")
            .withScope(LockScope.GLOBAL)
            .lockConfig("client-" + clientId + "-" + endpoint)
            .semaphoreLock()
            .withPermits(10) // Allow 10 concurrent requests per client per endpoint
            .withLeaseTime(Duration.ofSeconds(30)) // Request processing timeout
            .build();
        
        try {
            if (rateLimitSemaphore.tryAcquire(1, TimeUnit.SECONDS)) {
                try {
                    log.info("Processing API request for client: {}, endpoint: {}", clientId, endpoint);
                    
                    // Process the API request
                    processApiRequestInternal(clientId, endpoint);
                    
                } finally {
                    rateLimitSemaphore.release();
                    log.debug("Released semaphore permit for client: {}", clientId);
                }
            } else {
                log.warn("Rate limit exceeded for client: {}, endpoint: {}", clientId, endpoint);
                throw new RateLimitExceededException("Too many concurrent requests");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("API request interrupted", e);
        }
    }
    
    private void processApiRequestInternal(String clientId, String endpoint) {
        // Simulate API processing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }
}
```

### 5.2. Resource Pool Management with Semaphore

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class DatabaseConnectionPoolService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void executeWithConnection(String poolName, Runnable task) {
        // Semaphore to manage database connection pool
        RedisSemaphoreLock connectionPoolSemaphore = lockBucketRegistry
            .builder("db-connection-pools", "data-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("pool-" + poolName)
            .semaphoreLock()
            .withPermits(20) // 20 concurrent connections
            .withLeaseTime(Duration.ofMinutes(5)) // Long-running queries
            .build();
        
        try {
            if (connectionPoolSemaphore.tryAcquire(30, TimeUnit.SECONDS)) {
                try {
                    log.debug("Acquired database connection from pool: {}", poolName);
                    
                    // Execute task with database connection
                    task.run();
                    
                } finally {
                    connectionPoolSemaphore.release();
                    log.debug("Released database connection to pool: {}", poolName);
                }
            } else {
                throw new ResourcePoolExhaustedException("No available database connections");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Database operation interrupted", e);
        }
    }
    
    public int getAvailableConnections(String poolName) {
        RedisSemaphoreLock connectionPoolSemaphore = lockBucketRegistry
            .builder("db-connection-pools", "data-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("pool-" + poolName)
            .semaphoreLock()
            .withPermits(20)
            .build();
        
        return connectionPoolSemaphore.availablePermits();
    }
}
```

## 6. Advanced Usage Patterns

### 6.1. Lock Composition and Hierarchical Locking

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class ComplexBusinessService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void complexBusinessOperation(String customerId, String orderId, String productId) {
        // Hierarchical locking: customer -> order -> product
        RedisReentrantLock customerLock = lockBucketRegistry
            .builder("customers", "business-service-v1")
            .withScope(LockScope.GLOBAL)
            .lockConfig("customer-" + customerId)
            .reentrantLock()
            .withLeaseTime(Duration.ofMinutes(2))
            .build();
        
        try {
            if (customerLock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    log.info("Acquired customer lock: {}", customerId);
                    
                    RedisReentrantLock orderLock = lockBucketRegistry
                        .builder("orders", "business-service-v1")
                        .withScope(LockScope.GLOBAL)
                        .lockConfig("order-" + orderId)
                        .reentrantLock()
                        .withLeaseTime(Duration.ofMinutes(1))
                        .build();
                    
                    try {
                        if (orderLock.tryLock(5, TimeUnit.SECONDS)) {
                            try {
                                log.info("Acquired order lock: {}", orderId);
                                
                                // Use semaphore for product inventory
                                RedisSemaphoreLock productSemaphore = lockBucketRegistry
                                    .builder("product-inventory", "business-service-v1")
                                    .withScope(LockScope.GLOBAL)
                                    .lockConfig("product-" + productId)
                                    .semaphoreLock()
                                    .withPermits(100) // Available inventory
                                    .build();
                                
                                try {
                                    if (productSemaphore.tryAcquire(1, TimeUnit.SECONDS)) {
                                        try {
                                            // Perform complex business operation
                                            performComplexOperation(customerId, orderId, productId);
                                        } finally {
                                            productSemaphore.release();
                                        }
                                    }
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException(e);
                                }
                            } finally {
                                orderLock.unlock();
                            }
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException(e);
                    }
                } finally {
                    customerLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }
    
    private void performComplexOperation(String customerId, String orderId, String productId) {
        // Complex business logic here
        log.info("Performing complex operation for customer: {}, order: {}, product: {}", 
                customerId, orderId, productId);
    }
}
```

### 6.2. Lock with Retry and Backoff Strategy

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class ResilientLockService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void performOperationWithRetry(String resourceId) {
        RedisReentrantLock resourceLock = lockBucketRegistry
            .builder("resilient-operations", "resilient-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .withDefaultRetryInterval(Duration.ofMillis(100)) // Start with 100ms
            .withDefaultMaxRetries(5)
            .lockConfig("resource-" + resourceId)
            .reentrantLock()
            .withLeaseTime(Duration.ofSeconds(30))
            .build();
        
        int attempt = 0;
        int maxAttempts = 3;
        long baseDelayMs = 100;
        
        while (attempt < maxAttempts) {
            try {
                if (resourceLock.tryLock(5, TimeUnit.SECONDS)) {
                    try {
                        log.info("Successfully acquired lock for resource: {} on attempt: {}", 
                                resourceId, attempt + 1);
                        
                        // Perform the operation
                        performOperation(resourceId);
                        return; // Success, exit retry loop
                        
                    } finally {
                        resourceLock.unlock();
                    }
                } else {
                    attempt++;
                    if (attempt < maxAttempts) {
                        long delayMs = baseDelayMs * (1L << (attempt - 1)); // Exponential backoff
                        log.warn("Failed to acquire lock for resource: {} on attempt: {}. Retrying in {}ms", 
                                resourceId, attempt, delayMs);
                        Thread.sleep(delayMs);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Operation interrupted", e);
            }
        }
        
        throw new LockAcquisitionException("Failed to acquire lock after " + maxAttempts + " attempts");
    }
    
    private void performOperation(String resourceId) {
        // Simulate operation
        log.info("Performing operation on resource: {}", resourceId);
    }
}
```

## 7. Error Handling and Best Practices

### 7.1. Comprehensive Error Handling

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class RobustLockService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void robustOperation(String resourceId) {
        RedisReentrantLock lock = lockBucketRegistry
            .builder("robust-operations", "robust-service-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig("resource-" + resourceId)
            .reentrantLock()
            .withLeaseTime(Duration.ofSeconds(60))
            .build();
        
        boolean lockAcquired = false;
        
        try {
            log.debug("Attempting to acquire lock for resource: {}", resourceId);
            
            lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                log.warn("Failed to acquire lock for resource: {} within timeout", resourceId);
                throw new LockAcquisitionException("Could not acquire lock within timeout");
            }
            
            log.info("Successfully acquired lock for resource: {}", resourceId);
            
            try {
                // Perform the critical operation
                performCriticalOperation(resourceId);
                log.info("Successfully completed operation for resource: {}", resourceId);
                
            } catch (BusinessException e) {
                log.error("Business logic error for resource: {}", resourceId, e);
                throw e; // Re-throw business exceptions
                
            } catch (Exception e) {
                log.error("Unexpected error during operation for resource: {}", resourceId, e);
                throw new OperationException("Operation failed unexpectedly", e);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Lock acquisition interrupted for resource: {}", resourceId);
            throw new RuntimeException("Operation interrupted", e);
            
        } catch (LockException e) {
            log.error("Lock-related error for resource: {}", resourceId, e);
            throw new OperationException("Lock operation failed", e);
            
        } finally {
            if (lockAcquired) {
                try {
                    lock.unlock();
                    log.debug("Successfully released lock for resource: {}", resourceId);
                } catch (Exception e) {
                    log.error("Error releasing lock for resource: {}", resourceId, e);
                    // Don't throw here to avoid masking original exception
                }
            }
        }
    }
    
    private void performCriticalOperation(String resourceId) {
        // Critical business logic
        log.info("Executing critical operation for resource: {}", resourceId);
    }
}
```

### 7.2. Lock Health Monitoring

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockHealthMonitor {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    @EventListener
    @Async
    public void handleLockEvent(LockEvent event) {
        log.info("Lock event: {} for key: {}, owner: {}", 
                event.getType(), event.getLockKey(), event.getOwnerId());
        
        // Custom monitoring logic
        switch (event.getType()) {
            case ACQUIRED:
                recordLockAcquisition(event);
                break;
            case RELEASED:
                recordLockRelease(event);
                break;
            case ACQUISITION_FAILED:
                recordLockFailure(event);
                break;
            case WATCHDOG_EXTENDED:
                recordWatchdogExtension(event);
                break;
        }
    }
    
    @Scheduled(fixedDelay = 30000) // Every 30 seconds
    public void monitorLockHealth() {
        // Monitor lock statistics and health
        log.debug("Performing lock health check");
        
        // Example: Check for locks held too long
        // This would require additional monitoring infrastructure
    }
    
    private void recordLockAcquisition(LockEvent event) {
        // Record metrics, send to monitoring system
    }
    
    private void recordLockRelease(LockEvent event) {
        // Record metrics
    }
    
    private void recordLockFailure(LockEvent event) {
        // Alert on lock failures
    }
    
    private void recordWatchdogExtension(LockEvent event) {
        // Monitor watchdog activity
    }
}
```

## 8. Testing Examples

### 8.1. Unit Testing with Lock Mocks

```java
@ExtendWith(MockitoExtension.class)
class OrderProcessingServiceTest {
    
    @Mock
    private LockBucketRegistry lockBucketRegistry;
    
    @Mock
    private LockBucketBuilder lockBucketBuilder;
    
    @Mock
    private LockConfigBuilder lockConfigBuilder;
    
    @Mock
    private ReentrantLockConfigBuilder reentrantLockConfigBuilder;
    
    @Mock
    private RedisReentrantLock lock;
    
    @InjectMocks
    private OrderProcessingService orderProcessingService;
    
    @Test
    void shouldProcessOrderSuccessfully() throws InterruptedException {
        // Given
        String orderId = "order-123";
        
        when(lockBucketRegistry.builder("orders", "order-service-v1"))
            .thenReturn(lockBucketBuilder);
        when(lockBucketBuilder.withScope(LockScope.APPLICATION_INSTANCE))
            .thenReturn(lockBucketBuilder);
        when(lockBucketBuilder.lockConfig("order-" + orderId))
            .thenReturn(lockConfigBuilder);
        when(lockConfigBuilder.reentrantLock())
            .thenReturn(reentrantLockConfigBuilder);
        when(reentrantLockConfigBuilder.build())
            .thenReturn(lock);
        when(lock.tryLock(30, TimeUnit.SECONDS))
            .thenReturn(true);
        
        // When
        orderProcessingService.processOrder(orderId);
        
        // Then
        verify(lock).tryLock(30, TimeUnit.SECONDS);
        verify(lock).unlock();
    }
    
    @Test
    void shouldHandleLockAcquisitionFailure() throws InterruptedException {
        // Given
        String orderId = "order-123";
        
        when(lockBucketRegistry.builder("orders", "order-service-v1"))
            .thenReturn(lockBucketBuilder);
        when(lockBucketBuilder.withScope(LockScope.APPLICATION_INSTANCE))
            .thenReturn(lockBucketBuilder);
        when(lockBucketBuilder.lockConfig("order-" + orderId))
            .thenReturn(lockConfigBuilder);
        when(lockConfigBuilder.reentrantLock())
            .thenReturn(reentrantLockConfigBuilder);
        when(reentrantLockConfigBuilder.build())
            .thenReturn(lock);
        when(lock.tryLock(30, TimeUnit.SECONDS))
            .thenReturn(false);
        
        // When & Then
        assertThatThrownBy(() -> orderProcessingService.processOrder(orderId))
            .isInstanceOf(OrderProcessingException.class)
            .hasMessageContaining("Cannot process order in current state");
        
        verify(lock).tryLock(30, TimeUnit.SECONDS);
        verify(lock, never()).unlock();
    }
}
```

### 8.2. Integration Testing

```java
@SpringBootTest
@TestPropertySource(properties = {
    "destilink.fw.locking.redis.enabled=true",
    "destilink.fw.redis.core.enabled=true"
})
class LockingIntegrationTest {
    
    @Autowired
    private LockBucketRegistry lockBucketRegistry;
    
    @Test
    void shouldAcquireAndReleaseLockSuccessfully() throws InterruptedException {
        // Given
        String lockKey = "integration-test-lock";
        
        RedisReentrantLock lock = lockBucketRegistry
            .builder("integration-tests", "test-app-v1")
            .withScope(LockScope.APPLICATION_INSTANCE)
            .lockConfig(lockKey)
            .reentrantLock()
            .withLeaseTime(Duration.ofSeconds(10))
            .build();
        
        // When
        boolean acquired = lock.tryLock(5, TimeUnit.SECONDS);
        
        // Then
        assertThat(acquired).isTrue();
        
        // Cleanup
        lock.unlock();
    }
    
    @Test
    void shouldPreventConcurrentAccess() throws InterruptedException {
        // Given
        String lockKey = "concurrent-test-lock";
        CountDownLatch startLatch = new CountDownLatch(2);
        CountDownLatch completeLatch = new CountDownLatch(2);
        AtomicInteger successCount = new AtomicInteger(0);
        
        // When
        for (int i = 0; i < 2; i++) {
            new Thread(() -> {
                try {
                    RedisReentrantLock lock = lockBucketRegistry
                        .builder("concurrent-tests", "test-app-v1")
                        .withScope(LockScope.APPLICATION_INSTANCE)
                        .lockConfig(lockKey)
                        .reentrantLock()
                        .withLeaseTime(Duration.ofSeconds(5))
                        .build();
                    
                    startLatch.countDown();
                    startLatch.await();
                    
                    if (lock.tryLock(1, TimeUnit.SECONDS)) {
                        try {
                            successCount.incrementAndGet();
                            Thread.sleep(100); // Hold lock briefly
                        } finally {
                            lock.unlock();
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    completeLatch.countDown();
                }
            }).start();
        }
        
        // Then
        completeLatch.await(10, TimeUnit.SECONDS);
        assertThat(successCount.get()).isEqualTo(1); // Only one thread should succeed
    }
}
```

## 9. Performance Considerations

### 9.1. Optimal Lock Configuration

```java
@Configuration