-- KEYS[1] mainLockKey (hash, e.g., myApp:__lock_buckets__:resource:__locks__:{myRWLock})
-- KEYS[2] responseCacheKey (optional)

-- ARGV[1] requestUuid (used if KEYS[2] is provided)
-- ARGV[2] leaseTimeMs
-- ARGV[3] writerId (current thread's write lock name, used as field in KEYS[1])
-- ARGV[4] responseCacheTTLSeconds (optional, in seconds)
-- ARGV[5] modeFieldNameInMainHash (e.g., "mode")
-- ARGV[6] writerHolderFieldNameInMainHash (e.g., "write_owner_id")
-- ARGV[7] writeMode (e.g., "WRITE")

if ARGV[4] ~= nil and KEYS[2] ~= nil and redis.call('exists', KEYS[2]) == 1 then
    local cachedValue = redis.call('get', KEYS[2]);
    if cachedValue ~= false then
      if cachedValue == 'nil' then return nil else return tonumber(cachedValue) end;
    end;
end;

local mode = redis.call('hget', KEYS[1], ARGV[5]);
if (mode == false) then
    -- Lock doesn't exist yet, create it with write mode
    redis.call('hset', KEYS[1], ARGV[5], ARGV[7]);
    redis.call('hset', KEYS[1], ARGV[6], ARGV[3]);
    redis.call('pexpire', KEYS[1], ARGV[2]);
    
    if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], '1', 'px', tonumber(ARGV[4]) * 1000); end;
    return 1;
end;

-- Check if this writer already holds the lock (reentrant case)
if redis.call('hget', KEYS[1], ARGV[6]) == ARGV[3] then
    -- Extend TTL for reentrant lock
    redis.call('pexpire', KEYS[1], ARGV[2]);
    
    if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], '1', 'px', tonumber(ARGV[4]) * 1000); end;
    return 1;
end;

-- Lock exists but is held by someone else
if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], '0', 'px', tonumber(ARGV[4]) * 1000); end;
return 0;