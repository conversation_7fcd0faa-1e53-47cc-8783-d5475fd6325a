package com.tui.destilink.framework.locking.redis.lock.model;

/**
 * Enum representing the different types of unlock events that can be published
 * to Redis Pub/Sub channels.
 * <p>
 * These values are used in the message payload when a lock is released,
 * providing
 * information to the {@code UnlockMessageListener} about how to handle the
 * unlock
 * event, particularly for optimizing wake-up strategies for different lock
 * types.
 * </p>
 */
public enum UnlockType {
    /**
     * Published when a reentrant lock is fully released (reentrancy count becomes
     * zero).
     */
    REENTRANT_FULLY_RELEASED,

    /**
     * Published when a non-reentrant exclusive lock is released.
     */
    NON_REENTRANT_RELEASED,

    /**
     * Published when a state lock is released, and the associated state value was
     * not changed
     * during the unlock operation.
     */
    STATE_LOCK_RELEASED_STATE_UNCHANGED,

    /**
     * Published when a state lock is released, and the associated state value was
     * changed
     * during the unlock operation.
     */
    STATE_LOCK_RELEASED_STATE_UPDATED,

    /**
     * Published when a read lock is released, and the listener should prioritize
     * waking up
     * other waiting read lock requestors.
     */
    RW_READ_RELEASED_WAKEN_READERS,

    /**
     * Published when a read lock is released (potentially the last reader), and
     * there are
     * writers waiting.
     */
    RW_READ_RELEASED_WAKEN_SINGLE_WRITER,

    /**
     * Published when a write lock is released.
     */
    RW_WRITE_RELEASED_WAKEN_ALL,

    /**
     * Published when a read stamp is released.
     */
    STAMPED_READ_RELEASED,

    /**
     * Published when a write stamp is released.
     */
    STAMPED_WRITE_RELEASED,

    /**
     * Published when a write lock is successfully converted to a read lock.
     */
    STAMPED_CONVERTED_TO_READ,

    /**
     * Published when a read lock is successfully upgraded to a write lock.
     */
    STAMPED_CONVERTED_TO_WRITE
}