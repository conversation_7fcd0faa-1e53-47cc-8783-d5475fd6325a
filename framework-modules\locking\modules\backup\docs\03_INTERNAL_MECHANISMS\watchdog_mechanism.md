# Watchdog Mechanism

The watchdog mechanism is a critical component of the Redis locking system, ensuring lock reliability and preventing orphaned locks. This document outlines the key concepts and implementation details of the watchdog mechanism.

## Safety Buffer Mechanism

The safety buffer is a crucial element in preventing premature lock expiration. It is calculated using the following formula:

```
safetyBufferMillis = watchdogInterval * watchdogSafetyFactor
```

Where:
- `watchdogInterval` is the time between watchdog operations
- `watchdogSafetyFactor` is a configurable multiplier to ensure adequate buffer

Example:
If `watchdogInterval` is 5000ms and `watchdogSafetyFactor` is 2, then:
```
safetyBufferMillis = 5000 * 2 = 10000ms (10 seconds)
```

This safety buffer ensures that locks are refreshed well before their expiration, accounting for network latency and other potential delays.

## Lease Time Preservation

To maintain the original lease time while allowing for dynamic refreshing, the system tracks two time values:

1. `originalLeaseTimeMillis`: The initial lease time set when the lock was acquired
2. Relative time: The remaining time until the lock expires, which is updated with each refresh operation

This approach allows the system to maintain the intended lock duration while still providing the flexibility to extend the lock as needed.

## Heartbeat Strategy and Refresh Timing

The watchdog employs a heartbeat strategy to maintain lock validity:

1. Calculate the next refresh time: `nextRefreshTime = currentTime + watchdogInterval - safetyBufferMillis`
2. Schedule a refresh operation to occur at `nextRefreshTime`
3. When the refresh operation executes:
   a. Extend the lock's expiration time
   b. Recalculate the next refresh time
   c. Schedule the next refresh operation

This strategy ensures that locks are consistently refreshed before they expire, maintaining the safety buffer margin.

## Virtual Thread Integration

The watchdog mechanism leverages Java's Virtual Thread architecture for efficient and scalable concurrent operations:

- Non-blocking Operations: Watchdog refresh operations are executed using Virtual Threads, allowing for high concurrency without the overhead of traditional thread pools.
- Scalable Monitoring: Virtual Threads enable the system to efficiently manage a large number of concurrent watchdog operations, adapting to varying load conditions.

Note: The `asyncExecutorName` property has been removed, as it is no longer needed with the adoption of Virtual Threads.

## Idempotency Mechanisms

To prevent duplicate refresh operations and ensure consistency, the watchdog implements the following idempotency measures:

1. Request Tracking: Each watchdog operation is assigned a unique `requestUuid`.
2. Response Caching: The results of watchdog operations are cached for a short duration (`responseCacheTtl`).
3. Deduplication: Before executing a refresh operation, the system checks if an operation with the same `requestUuid` has been recently processed.

These mechanisms ensure that even if multiple refresh attempts are made for the same lock (e.g., due to network issues), only one operation will take effect.

## Lock-Type Awareness and Semantic Isolation

The watchdog mechanism is designed to respect the semantic isolation of different lock types:

1. Type-Specific Monitoring: Watchdog operations are tailored to the specific requirements of each lock type (e.g., read locks vs. write locks).
2. Semantic Isolation: Watchdog operations for one lock type cannot interfere with or affect locks of a different type.
3. Cross-Type Prevention: The system actively prevents watchdog operations from mixing across different lock types, maintaining the integrity of the locking system.

This lock-type awareness ensures that the watchdog mechanism maintains the intended behavior and isolation guarantees of each lock type.

## Conclusion

The updated watchdog mechanism provides a robust and efficient solution for maintaining lock validity in distributed systems. By leveraging safety buffers, Virtual Threads, idempotency mechanisms, and lock-type awareness, it ensures reliable and scalable lock management while preventing issues such as premature expiration or orphaned locks.