# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the class hierarchy and interface structure of the `locking-redis-lock` module. Understanding this hierarchy is crucial for developers working with or extending the module, as it demonstrates the relationships between different lock types and their common abstractions.

## 2. Core Interface Hierarchy

```mermaid
classDiagram
    class Lock {
        <<interface>>
        +lock()
        +lockInterruptibly()
        +tryLock() boolean
        +tryLock(time, unit) boolean
        +unlock()
        +newCondition() Condition
    }

    class AsyncLock {
        <<interface>>
        +lockAsync() CompletableFuture~Void~
        +tryLockAsync(time, unit) CompletableFuture~Boolean~
        +unlockAsync() CompletableFuture~Void~
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }

    class StampedLock {
        <<interface>>
        +readLock() long
        +writeLock() long
        +tryOptimisticRead() long
        +validate(stamp) boolean
        +unlock(stamp)
        +tryConvertToWriteLock(stamp) long
        +tryConvertToReadLock(stamp) long
    }

    class AsyncStampedLock {
        <<interface>>
        +readLockAsync() CompletableFuture~Long~
        +writeLockAsync() CompletableFuture~Long~
        +tryOptimisticReadAsync() CompletableFuture~Long~
        +validateAsync(stamp) CompletableFuture~Boolean~
        +unlockAsync(stamp) CompletableFuture~Void~
        +tryConvertToWriteLockAsync(stamp) CompletableFuture~Long~
        +tryConvertToReadLockAsync(stamp) CompletableFuture~Long~
    }

    AsyncLock --|> Lock : extends
    AsyncReadWriteLock --|> ReadWriteLock : extends
    AsyncStampedLock --|> StampedLock : extends
```

## 3. Abstract Base Classes

### 3.1. AbstractRedisLock

```mermaid
classDiagram
    class AbstractRedisLock {
        <<abstract>>
        -String lockName
        -String ownerId
        -LockComponentRegistry componentRegistry
        -LockBucketConfig config
        +lock()
        +tryLock() boolean
        +tryLock(time, unit) boolean
        +unlock()
        +lockAsync() CompletableFuture~Void~
        +tryLockAsync(time, unit) CompletableFuture~Boolean~
        +unlockAsync() CompletableFuture~Void~
        #tryAcquireLockInternalAsync() CompletableFuture~Boolean~*
        #releaseLockInternalAsync() CompletableFuture~Boolean~*
        #registerWithWatchdog()
        #unregisterFromWatchdog()
    }

    class AsyncLock {
        <<interface>>
    }

    AbstractRedisLock ..|> AsyncLock : implements
```

**Key Responsibilities:**
- Provides common functionality for all Redis-based locks
- Implements synchronous wrapper methods around asynchronous core logic
- Manages watchdog registration/unregistration
- Handles common error scenarios and exception translation
- Coordinates with `LockSemaphoreHolder` for waiting logic

**Abstract Methods:**
- `tryAcquireLockInternalAsync()`: Lock-specific acquisition logic
- `releaseLockInternalAsync()`: Lock-specific release logic

## 4. Concrete Lock Implementations

### 4.1. RedisReentrantLock

```mermaid
classDiagram
    class RedisReentrantLock {
        +RedisReentrantLock(lockName, componentRegistry, config)
        +isHeldByCurrentThread() boolean
        +getHoldCount() int
        +isLocked() boolean
        #tryAcquireLockInternalAsync() CompletableFuture~Boolean~
        #releaseLockInternalAsync() CompletableFuture~Boolean~
    }

    class AbstractRedisLock {
        <<abstract>>
    }

    RedisReentrantLock --|> AbstractRedisLock : extends
```

**Characteristics:**
- Supports reentrant acquisition by the same owner
- Maintains reentrancy count in Redis Hash
- Uses `try_lock.lua` and `unlock.lua` scripts

### 4.2. RedisStateLock

```mermaid
classDiagram
    class RedisStateLock {
        -String expectedState
        +RedisStateLock(lockName, expectedState, componentRegistry, config)
        +getStateAsync() CompletableFuture~String~
        +updateStateAsync(newState) CompletableFuture~Boolean~
        +unlockWithStateAsync(newState) CompletableFuture~Boolean~
        #tryAcquireLockInternalAsync() CompletableFuture~Boolean~
        #releaseLockInternalAsync() CompletableFuture~Boolean~
    }

    class AbstractRedisLock {
        <<abstract>>
    }

    RedisStateLock --|> AbstractRedisLock : extends
```

**Characteristics:**
- Conditional lock acquisition based on state value
- Maintains separate state key alongside lock key
- Uses `try_state_lock.lua`, `update_state.lua`, and `unlock_state_lock.lua` scripts

### 4.3. RedisReadWriteLock

```mermaid
classDiagram
    class RedisReadWriteLock {
        -RedisReadLock readLock
        -RedisWriteLock writeLock
        +RedisReadWriteLock(lockName, componentRegistry, config)
        +readLock() RedisReadLock
        +writeLock() RedisWriteLock
    }

    class RedisReadLock {
        +RedisReadLock(parent, lockName, componentRegistry, config)
        +isHeldByCurrentThread() boolean
        +getHoldCount() int
        #tryAcquireLockInternalAsync() CompletableFuture~Boolean~
        #releaseLockInternalAsync() CompletableFuture~Boolean~
    }

    class RedisWriteLock {
        +RedisWriteLock(parent, lockName, componentRegistry, config)
        +isHeldByCurrentThread() boolean
        +getHoldCount() int
        #tryAcquireLockInternalAsync() CompletableFuture~Boolean~
        #releaseLockInternalAsync() CompletableFuture~Boolean~
    }

    class AsyncReadWriteLock {
        <<interface>>
    }

    class AbstractRedisLock {
        <<abstract>>
    }

    RedisReadWriteLock ..|> AsyncReadWriteLock : implements
    RedisReadLock --|> AbstractRedisLock : extends
    RedisWriteLock --|> AbstractRedisLock : extends
    RedisReadWriteLock *-- RedisReadLock : contains
    RedisReadWriteLock *-- RedisWriteLock : contains
```

**Characteristics:**
- Supports concurrent read access, exclusive write access
- Manages complex TTL coordination between main hash and individual read timeout keys
- Uses `try_read_lock.lua`, `unlock_read_lock.lua`, `try_write_lock.lua`, and `unlock_write_lock.lua` scripts

### 4.4. RedisStampedLock

```mermaid
classDiagram
    class RedisStampedLock {
        +RedisStampedLock(lockName, componentRegistry, config)
        +readLockAsync() CompletableFuture~Long~
        +writeLockAsync() CompletableFuture~Long~
        +tryOptimisticReadAsync() CompletableFuture~Long~
        +validateAsync(stamp) CompletableFuture~Boolean~
        +unlockAsync(stamp) CompletableFuture~Void~
        +tryConvertToWriteLockAsync(stamp) CompletableFuture~Long~
        +tryConvertToReadLockAsync(stamp) CompletableFuture~Long~
        -parseStamp(stamp) StampInfo
        -generateStamp(type, version, ownerId) long
    }

    class AsyncStampedLock {
        <<interface>>
    }

    RedisStampedLock ..|> AsyncStampedLock : implements
```

**Characteristics:**
- Provides optimistic reading with stamp validation
- Supports lock conversion between read and write modes
- Maintains version information for optimistic concurrency control
- Uses `try_stamped_lock.lua`, `unlock_stamped_lock.lua`, `validate_stamp.lua`, and conversion scripts

## 5. Builder Hierarchy

```mermaid
classDiagram
    class LockBucketBuilder {
        +withScope(scope) LockBucketBuilder
        +withLockOwnerSupplier(supplier) LockBucketBuilder
        +withDefaultLeaseTime(duration) LockBucketBuilder
        +withDefaultRetryInterval(duration) LockBucketBuilder
        +lock() LockConfigBuilder
    }

    class LockConfigBuilder {
        +reentrant() ReentrantLockConfigBuilder
        +state(expectedState) StateLockConfigBuilder
        +readWrite() ReadWriteLockConfigBuilder
        +stamped() StampedLockConfigBuilder
    }

    class AbstractLockTypeConfigBuilder {
        <<abstract>>
        +withLeaseTime(duration) T
        +withRetryInterval(duration) T
        +withMaxRetries(count) T
        +withAcquireTimeout(duration) T
        +build() Lock*
    }

    class ReentrantLockConfigBuilder {
        +build() RedisReentrantLock
    }

    class StateLockConfigBuilder {
        +withInitIfNotExist(init) StateLockConfigBuilder
        +withStateExpiration(duration) StateLockConfigBuilder
        +build() RedisStateLock
    }

    class ReadWriteLockConfigBuilder {
        +build() RedisReadWriteLock
    }

    class StampedLockConfigBuilder {
        +build() RedisStampedLock
    }

    LockBucketBuilder --> LockConfigBuilder : transitions to
    LockConfigBuilder --> AbstractLockTypeConfigBuilder : transitions to
    AbstractLockTypeConfigBuilder <|-- ReentrantLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- StateLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- ReadWriteLockConfigBuilder
    AbstractLockTypeConfigBuilder <|-- StampedLockConfigBuilder
```

## 6. Supporting Classes

### 6.1. Configuration Classes

```mermaid
classDiagram
    class LockBucketConfig {
        -String bucketName
        -Duration leaseTime
        -Duration retryInterval
        -int maxRetries
        -Duration acquireTimeout
        -LockOwnerSupplier lockOwnerSupplier
        +getBucketName() String
        +getLeaseTime() Duration
        +getRetryInterval() Duration
        // ... other getters
    }

    class RedisLockProperties {
        -boolean enabled
        -WatchdogProperties watchdog
        -Defaults defaults
        +isEnabled() boolean
        +getWatchdog() WatchdogProperties
        +getDefaults() Defaults
    }

    class WatchdogProperties {
        -boolean enabled
        -Duration interval
        -Duration minLeaseTimeForActivation
        // ... other properties
    }

    class Defaults {
        -Duration leaseTime
        -Duration retryInterval
        -int maxRetries
        -Duration acquireTimeout
        // ... other defaults
    }

    RedisLockProperties *-- WatchdogProperties : contains
    RedisLockProperties *-- Defaults : contains
```

### 6.2. Exception Hierarchy

```mermaid
classDiagram
    class AbstractRedisLockException {
        <<abstract>>
        -String lockName
        -String lockType
        -String lockOwnerId
        -String requestUuid
        +getMarker() Marker
        #populateSpecificMarkers(contextMap)*
    }

    class LockAcquisitionException {
        +populateSpecificMarkers(contextMap)
    }

    class LockTimeoutException {
        -long timeoutMillis
        -int attempts
        +populateSpecificMarkers(contextMap)
    }

    class LockReleaseException {
        -String attemptedOwnerId
        -String actualOwnerId
        +populateSpecificMarkers(contextMap)
    }

    class IdempotencyViolationException {
        -String cacheKey
        -String violationType
        +populateSpecificMarkers(contextMap)
    }

    AbstractRedisLockException <|-- LockAcquisitionException
    AbstractRedisLockException <|-- LockTimeoutException
    AbstractRedisLockException <|-- LockReleaseException
    AbstractRedisLockException <|-- IdempotencyViolationException
```

## 7. Key Design Patterns

### 7.1. Template Method Pattern
- `AbstractRedisLock` defines the algorithm structure
- Concrete implementations provide specific behavior via abstract methods

### 7.2. Builder Pattern
- Fluent API for lock configuration
- Progressive refinement from bucket to specific lock type

### 7.3. Strategy Pattern
- `LockOwnerSupplier` for different owner ID generation strategies
- Different lock types implementing common interfaces

### 7.4. Composite Pattern
- `RedisReadWriteLock` composed of separate read and write locks
- Coordinated behavior through shared state

This class hierarchy provides a clean separation of concerns while maintaining flexibility for different locking semantics and use cases.