package com.tui.destilink.framework.locking.redis.lock.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

/**
 * Utility class for propagating MDC context to Virtual Threads.
 * This ensures that logging context is maintained when lock operations
 * are executed asynchronously in Virtual Threads.
 * 
 * <p>This utility leverages the existing framework patterns and integrates
 * with the Virtual Thread executor configured in RedisLockAutoConfiguration.</p>
 * 
 * @since 1.0.0
 */
@Slf4j
@UtilityClass
public class VirtualThreadContextUtils {

    /**
     * Wraps a Runnable with MDC context propagation for Virtual Thread execution.
     * The context from the calling thread is captured and restored in the Virtual Thread.
     * 
     * @param task the task to execute
     * @return a Runnable that will execute with proper MDC context
     */
    public static Runnable withContext(Runnable task) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                task.run();
            } finally {
                // Restore previous context or clear if none existed
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                } else {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * Wraps a Callable with MDC context propagation for Virtual Thread execution.
     * The context from the calling thread is captured and restored in the Virtual Thread.
     * 
     * @param <T> the return type of the callable
     * @param task the task to execute
     * @return a Callable that will execute with proper MDC context
     */
    public static <T> Callable<T> withContext(Callable<T> task) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                return task.call();
            } finally {
                // Restore previous context or clear if none existed
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                } else {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * Wraps a Supplier with MDC context propagation for Virtual Thread execution.
     * The context from the calling thread is captured and restored in the Virtual Thread.
     * 
     * @param <T> the return type of the supplier
     * @param supplier the supplier to execute
     * @return a Supplier that will execute with proper MDC context
     */
    public static <T> Supplier<T> withContext(Supplier<T> supplier) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                return supplier.get();
            } finally {
                // Restore previous context or clear if none existed
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                } else {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * Executes a Runnable asynchronously in a Virtual Thread with MDC context propagation.
     * 
     * @param executor the Virtual Thread executor service
     * @param task the task to execute
     * @return a CompletableFuture representing the asynchronous execution
     */
    public static CompletableFuture<Void> executeAsync(ExecutorService executor, Runnable task) {
        return CompletableFuture.runAsync(withContext(task), executor);
    }

    /**
     * Executes a Callable asynchronously in a Virtual Thread with MDC context propagation.
     * 
     * @param <T> the return type of the callable
     * @param executor the Virtual Thread executor service
     * @param task the task to execute
     * @return a CompletableFuture representing the asynchronous execution result
     */
    public static <T> CompletableFuture<T> executeAsync(ExecutorService executor, Callable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return withContext(task).call();
            } catch (Exception e) {
                throw new RuntimeException("Error executing task in Virtual Thread", e);
            }
        }, executor);
    }

    /**
     * Executes a Supplier asynchronously in a Virtual Thread with MDC context propagation.
     * 
     * @param <T> the return type of the supplier
     * @param executor the Virtual Thread executor service
     * @param supplier the supplier to execute
     * @return a CompletableFuture representing the asynchronous execution result
     */
    public static <T> CompletableFuture<T> executeAsync(ExecutorService executor, Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(withContext(supplier), executor);
    }

    /**
     * Creates a context-aware CompletableFuture that will execute with the current MDC context.
     * This is useful for chaining operations that need to maintain logging context.
     * 
     * @param <T> the return type
     * @param executor the Virtual Thread executor service
     * @param supplier the supplier to execute
     * @return a CompletableFuture that maintains MDC context
     */
    public static <T> CompletableFuture<T> supplyAsync(ExecutorService executor, Supplier<T> supplier) {
        return executeAsync(executor, supplier);
    }

    /**
     * Creates a context-aware CompletableFuture for Runnable tasks.
     * 
     * @param executor the Virtual Thread executor service
     * @param task the task to execute
     * @return a CompletableFuture that maintains MDC context
     */
    public static CompletableFuture<Void> runAsync(ExecutorService executor, Runnable task) {
        return executeAsync(executor, task);
    }

    /**
     * Captures the current MDC context for manual propagation.
     * This is useful when you need to store context for later use.
     * 
     * @return a copy of the current MDC context map, or null if no context exists
     */
    public static Map<String, String> captureContext() {
        return MDC.getCopyOfContextMap();
    }

    /**
     * Restores MDC context from a previously captured context map.
     * 
     * @param contextMap the context map to restore
     * @return an AutoCloseable that will restore the previous context when closed
     */
    public static AutoCloseable restoreContext(Map<String, String> contextMap) {
        Map<String, String> previousContext = MDC.getCopyOfContextMap();
        
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        } else {
            MDC.clear();
        }
        
        return () -> {
            if (previousContext != null) {
                MDC.setContextMap(previousContext);
            } else {
                MDC.clear();
            }
        };
    }
}