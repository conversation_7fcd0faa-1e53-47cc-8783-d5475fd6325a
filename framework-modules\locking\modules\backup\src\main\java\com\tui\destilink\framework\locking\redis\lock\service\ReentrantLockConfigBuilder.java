package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.lock.impl.RedisReentrantLock;

/**
 * Builder for configuring and creating Redis-based reentrant locks.
 * <p>
 * This builder extends the abstract lock type builder to provide
 * specific configuration for reentrant locks. It allows for fluent
 * configuration of lock parameters like lease time and retry interval.
 * </p>
 */
public class ReentrantLockConfigBuilder
        extends AbstractLockTypeConfigBuilder<ReentrantLockConfigBuilder, RedisReentrantLock> {

    /**
     * Creates a new reentrant lock config builder.
     *
     * @param componentRegistry The registry of shared lock components
     * @param bucketName        The name of the lock bucket
     * @param lockName          The name of the lock
     * @param bucketConfig      The bucket-level configuration
     */
    public ReentrantLockConfigBuilder(LockComponentRegistry componentRegistry, String bucketName, String lockName,
            LockBucketConfig bucketConfig) {
        super(componentRegistry, bucketName, lockName, bucketConfig);
    }

    /**
     * Builds and returns a new Redis reentrant lock with the configured settings.
     *
     * @return A new RedisReentrantLock instance
     */
    @Override
    public RedisReentrantLock build() {
        return new RedisReentrantLock(
                getComponentRegistry().getRedisLockOperations(),
                getComponentRegistry().getDefaultLockOwnerSupplier(),
                getComponentRegistry().getGlobalRedisLockProperties(),
                getLockName(),
                getEffectiveLeaseTime().toMillis(),
                getEffectiveRetryInterval().toMillis(),
                getComponentRegistry().getGlobalRedisLockProperties().getMaxRetries());
    }
}
