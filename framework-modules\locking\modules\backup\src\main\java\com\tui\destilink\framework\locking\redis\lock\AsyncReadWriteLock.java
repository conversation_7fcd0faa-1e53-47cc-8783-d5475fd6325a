package com.tui.destilink.framework.locking.redis.lock;

import java.util.concurrent.locks.ReadWriteLock;

/**
 * Asynchronous version of a {@link ReadWriteLock}.
 * <p>
 * Provides asynchronous {@link AsyncLock} instances for reading and writing.
 * All lock operations are non-blocking and return
 * {@link java.util.concurrent.CompletableFuture}.
 * </p>
 */
public interface AsyncReadWriteLock extends ReadWriteLock {

    /**
     * Returns the lock used for reading.
     *
     * @return the lock used for reading, as an {@link AsyncLock}
     */
    @Override
    AsyncLock readLock();

    /**
     * Returns the lock used for writing.
     *
     * @return the lock used for writing, as an {@link AsyncLock}
     */
    @Override
    AsyncLock writeLock();
}
