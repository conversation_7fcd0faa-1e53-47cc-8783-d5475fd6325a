-- update_state_if_equals.lua
-- This script atomically updates a state lock's state only if it matches the expected state.
-- It verifies lock ownership and checks the current state before updating.
--
-- KEYS[1] - The lock key
-- KEYS[2] - The state key
-- ARGV[1] - The lock owner ID
-- ARGV[2] - The expected current state
-- ARGV[3] - The new state to set
-- ARGV[4] - The lock TTL in milliseconds
--
-- Returns:
-- 1 - Success: state was updated
-- 0 - Failure: lock not owned by the specified owner
-- -1 - Failure: current state does not match expected state

-- Check if the lock exists and is owned by the specified owner
local lockExists = redis.call('EXISTS', KEYS[1])
if lockExists == 0 then
    return 0  -- Lock does not exist
end

local lockOwner = redis.call('GET', KEYS[1])
if lockOwner ~= ARGV[1] then
    return 0  -- Lock is owned by someone else
end

-- Check if the current state matches the expected state
local currentState = redis.call('GET', KEYS[2])
if currentState ~= ARGV[2] then
    return -1  -- Current state does not match expected state
end

-- Update the state
redis.call('SET', KEYS[2], ARGV[3])

-- Ensure state TTL is aligned with lock TTL
redis.call('PEXPIRE', KEYS[2], ARGV[4])
-- Also refresh the lock TTL
redis.call('PEXPIRE', KEYS[1], ARGV[4])

return 1  -- Success