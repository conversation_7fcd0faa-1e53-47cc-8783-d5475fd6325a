# Redis Locking Module: Final Architecture Overview

## 1. Introduction

This document outlines the consolidated architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, performant, and developer-friendly distributed locking mechanism using Redis. The architecture emphasizes shared, Spring-managed components, efficient non-polling lock acquisition, clear configuration hierarchies, atomicity through Lua scripts, centralized idempotency, and strict adherence to **Destilink Framework guidelines (`/.amazonq/rules/guidelines.md`)**.

This architecture adopts an **Async-First approach, leveraging Virtual Threads for execution**, where all core lock operations are fundamentally asynchronous using `CompletableFuture`s, and synchronous `java.util.concurrent.Lock` implementations act as wrappers. It mandates the use of the internal `redis-core` module, particularly `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis interactions.

## 2. Core Architectural Principles

*   **Strict Guideline Adherence**: Implementation follows all rules from `/.amazonq/rules/guidelines.md`.
*   **Async-First Design with Virtual Threads**: All lock operations are primarily implemented asynchronously using `CompletableFuture`s and executed on Virtual Threads. Synchronous `Lock` methods are blocking wrappers.
*   **Mandatory `redis-core` Usage**: All Redis interactions via `ClusterCommandExecutor`. Configuration uses `RedisCoreProperties`.
*   **Atomicity via Lua Scripts**: Critical operations use Lua for atomicity. All mutating scripts are idempotent.
*   **Spring-Managed Shared Components**: Services like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations` are explicit Spring beans.
*   **Centralized Component Access**: `LockComponentRegistry` for shared service DI.
*   **Efficient Non-Polling Lock Acquisition**: Relies on Redis Pub/Sub (`UnlockMessageListener`, `LockSemaphoreHolder`). `LockSemaphoreHolder` registered *before* first Redis attempt.
*   **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` per logical operation (same for its internal retries). Lua scripts use this with `responseCacheTtl` for a response cache. Mandatory for all mutating scripts.
*   **Contextual and Structured Exception Handling**: `AbstractRedisLockException` hierarchy with `ExceptionMarkerProvider`.
*   **Explicit Configuration & Dependency Injection**: No `@ComponentScan`, explicit `@Bean`s, constructor injection.
*   **Standardized Key Construction with Lock-Type Segments**: Keys via `redis-core` utilities, MUST include a lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation (e.g., `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`).
*   **Clear Configuration Hierarchy**: `RedisLockProperties` (with nested `WatchdogProperties`, `Defaults`) for global YAML settings. Programmatic builders for bucket/instance overrides.
*   **Lightweight Lock Instances & Resource Sharing**: Lock objects are lightweight, delegating to shared services.
*   **Distributed Reentrancy**: Managed using Redis Hashes.
*   **Enhanced Logging & Observability**: SLF4J, MDC (propagated to Virtual Threads), `ExceptionMarkerProvider`, Micrometer metrics.
*   **Refined Watchdog Mechanism**: Always-active service, conditional monitoring based on `safetyBufferMillis` (`watchdog.interval * watchdog.factor`), `PEXPIREAT` usage, preserves `originalLeaseTimeMillis` in Redis.
*   **Lua-Only Redis Operations**: All lock state/TTL/metadata changes exclusively via Lua scripts.

## 3. Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code (Uses Virtual Threads implicitly via Lock API)"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["<code>LockBucketRegistry</code> (Bean)"];
        LBB["<code>LockBucketBuilder</code>"];
        LCB["<code>LockConfigBuilder</code>"];
        ALTCB["<code>AbstractLockTypeConfigBuilder</code> & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic (Async-First on Virtual Threads)"]
        direction LR;
        ARL["<code>AbstractRedisLock</code> (Base Class, uses Virtual Thread Executor)"];
        RLI["Concrete Lock Implementations<br>(e.g., `RedisReentrantLock`)<br>ALL Implement `AsyncLock`."];
        LSH["<code>LockSemaphoreHolder</code> (Per Lock Key, manages CompletableFutures)"];
    end
    
    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        VTExec["Virtual Thread Executor (Bean)"]
        LCR["<code>LockComponentRegistry</code> (Bean)"];
        SL["<code>ScriptLoader</code> (Bean)"];
        UMLM["<code>UnlockMessageListenerManager</code> (Bean)"];
        UML["<code>UnlockMessageListener</code> (Per Bucket, uses VT for onMessage)"];
        LW["<code>LockWatchdog</code> (Bean, always active, uses VT)"];
        ROps["<code>RedisLockOperations</code> (Bean, uses VT, central idempotency)"];
        LMP["<code>LockMonitor</code> (Bean, Optional)"];
        LOS["<code>DefaultLockOwnerSupplier</code> (Bean)"];
        LERRH["<code>RedisLockErrorHandler</code> (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["<code>RedisLockProperties</code> (Global YAML-backed with WatchdogProperties & Defaults)"];
        LBC["<code>LockBucketConfig</code> (Programmatically Resolved)"];
        RAutoConfig["<code>RedisLockAutoConfiguration</code> (@AutoConfiguration)"];
        RCProps["<code>RedisCoreProperties</code> (from redis-core)"];
    end

    subgraph ExternalSystems ["External Systems"]
        Redis["Redis (Cluster)"];
        CCExec["<code>ClusterCommandExecutor</code> (from redis-core)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;
    
    ARL -- "uses" --> VTExec;
    ARL -- "uses services from" --> LCR;
    ARL -- "uses for waiting" --> LSH;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;
    LCR -- "provides" --> VTExec;

    UMLM -- "manages & provides" --> UML;
    UML -- "manages `LockSemaphoreHolder` map" --> LSH;
    UML -- "listens to Pub/Sub from" --> Redis;
    UML -- "signals" --> LSH;
    
    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> VTExec;
    RAutoConfig -- "defines bean" --> LW; % LW is always active
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "enables" --> RLP;
    RAutoConfig -- "uses" --> RCProps;

    RLP -- "provides global defaults for" --> LBC;
    LBC -- "provides config to" --> LBB;

    ROps -- "uses" --> SL;
    ROps -- "interacts with" --> CCExec;
    ROps -- "uses" --> LERRH;
    LW -- "extends lease via" --> ROps; % Watchdog uses ROps
    
    CCExec -- "communicates with" --> Redis;

    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style ARL fill:#adebad;
    style LCR fill:#ccffcc;
    style VTExec fill:#ccffcc;
    style RLP fill:#ffcc99;
    style Redis fill:#ffcccc;
```

## 4. Key Components and Responsibilities

*   **Configuration Components**: `RedisLockProperties` (with nested `WatchdogProperties` for `interval`, `factor`, and `Defaults` for `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`), `LockBucketConfig`, `RedisLockAutoConfiguration`.
*   **Shared Spring-Managed Services**: `LockComponentRegistry`, `ScriptLoader`, `UnlockMessageListenerManager`, `UnlockMessageListener` (subscribes to `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`), `LockWatchdog` (always active, conditional monitoring), `RedisLockOperations` (central `requestUuid` generation, idempotency, internal retries for Redis ops), `DefaultLockOwnerSupplier` (with `canUseWatchdog()`), `RedisLockErrorHandler`, `LockMonitor`, Virtual Thread `ExecutorService`.
*   **Core Locking Logic & Instantiation**: `LockBucketRegistry`, Builders, `AbstractRedisLock` (uses Virtual Threads, registers `LockSemaphoreHolder` first), Concrete Lock Implementations (all implement `AsyncLock`, use lock-type specific keys), `LockSemaphoreHolder`.

## 5. Redis Key Schema

Keys include mandatory lock-type segments (e.g., `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`). `lockDataKey` (Redis Hash) stores `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`. (Details in `redis_key_schema.md`).

## 6. Data Flow and Interactions

*   **Lock Acquisition**: Asynchronous on Virtual Threads. `LockSemaphoreHolder` registered *before* first Redis attempt. `RedisLockOperations` handles `requestUuid`, Lua execution, and retries for individual Redis commands. `acquireTimeout` governs overall attempt with non-interruption of in-flight Redis ops.
*   **Lock Release & Unlock Notification**: Lua script releases lock, publishes `UnlockType`. Listener derives `lockName` and `lockType` from channel.
*   **Lease Extension (Watchdog)**: Watchdog service is always active. Monitors eligible locks (`userLeaseTime > safetyBufferMillis` & instance-bound). Uses `PEXPIREAT` via `watchdog_refresh_lock.lua`. Preserves `originalLeaseTimeMillis` from `lockDataKey`.
*   **Idempotency**: All mutating Lua scripts use `requestUuid` and `responseCacheTtl` to check/store results in a response cache.

## 7. Adherence to Destilink Framework Guidelines

Strict compliance with all guidelines, including no `@ComponentScan`, explicit dependencies, standard configuration, and MDC propagation to Virtual Threads.