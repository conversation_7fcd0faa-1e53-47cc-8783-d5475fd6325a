package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;
import java.util.Objects;

/**
 * Exception thrown when there is an issue with the lock configuration.
 * <p>
 * This exception indicates that a configuration property for the Redis lock
 * is invalid, missing, or incompatible with other settings.
 * </p>
 */
public class LockConfigurationException extends AbstractRedisLockException {

    private final String configProperty;
    private final String configValue;

    /**
     * Constructs a new LockConfigurationException with the specified details.
     *
     * @param lockName       The full Redis key of the lock involved (can be null if
     *                       not applicable)
     * @param lockType       The specific type of lock (e.g., "RedisLock",
     *                       "RedisStateLock")
     * @param lockOwnerId    The ID of the owner attempting the operation (can be
     *                       null)
     * @param requestUuid    The unique ID for the lock operation attempt (can be
     *                       null)
     * @param configProperty The name of the configuration property that is invalid
     * @param configValue    The invalid value of the configuration property
     * @param message        Descriptive error message
     */
    public LockConfigurationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String configProperty, String configValue, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.configProperty = configProperty;
        this.configValue = configValue;
    }

    /**
     * Constructs a new LockConfigurationException with the specified details and
     * cause.
     *
     * @param lockName       The full Redis key of the lock involved (can be null if
     *                       not applicable)
     * @param lockType       The specific type of lock (e.g., "RedisLock",
     *                       "RedisStateLock")
     * @param lockOwnerId    The ID of the owner attempting the operation (can be
     *                       null)
     * @param requestUuid    The unique ID for the lock operation attempt (can be
     *                       null)
     * @param configProperty The name of the configuration property that is invalid
     * @param configValue    The invalid value of the configuration property
     * @param message        Descriptive error message
     * @param cause          The underlying cause of this exception
     */
    public LockConfigurationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String configProperty, String configValue, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.configProperty = configProperty;
        this.configValue = configValue;
    }

    /**
     * Gets the name of the configuration property that is invalid.
     *
     * @return The configuration property name
     */
    public String getConfigProperty() {
        return configProperty;
    }

    /**
     * Gets the invalid value of the configuration property.
     *
     * @return The configuration property value
     */
    public String getConfigValue() {
        return configValue;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (configProperty != null) {
            contextMap.put("config.property", configProperty);
        }

        if (configValue != null) {
            contextMap.put("config.value", configValue);
        }
    }
}