# Redis Locking Module Documentation

## Overview

This documentation provides comprehensive guidance for the Redis-based distributed locking module within the Destilink Framework. The module enables reliable distributed coordination using Redis as the backing store, with support for multiple lock types, automatic lease management, and comprehensive monitoring.

## Quick Start

For immediate usage, see:
- [Configuration Guide](02_USAGE_AND_CONFIGURATION/configuration.md) - Setup and configuration
- [Usage Examples](02_USAGE_AND_CONFIGURATION/usage_examples.md) - Code examples and patterns

## Documentation Structure

### 📋 01. Overview and Concepts
Foundational knowledge and architectural understanding.

- **[Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md)** - System design and component relationships
- **[Class Hierarchy](01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md)** - Object model and inheritance structure  
- **[Lock Types Deep Dive](01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md)** - Detailed explanation of reentrant, state, and semaphore locks
- **[Glossary](01_OVERVIEW_AND_CONCEPTS/glossary.md)** - Terminology and definitions

### 🔧 02. Usage and Configuration
Practical guidance for implementation and setup.

- **[Configuration Guide](02_USAGE_AND_CONFIGURATION/configuration.md)** - Properties, auto-configuration, and customization
- **[Usage Examples](02_USAGE_AND_CONFIGURATION/usage_examples.md)** - Real-world code examples and best practices

### ⚙️ 03. Core Mechanisms (Internal)
Deep technical details of internal operations.

- **[Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)** - Duplicate operation prevention using request UUIDs and response caching
- **[Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)** - Automatic lease extension system with safety buffer calculations
- **[Unlock Notification System](03_CORE_MECHANISMS_INTERNAL/pubsub_notification_system.md)** - Lock release notifications via Redis Pub/Sub, serving as the primary waiting mechanism with TTL-based fallbacks
- **[Retry Mechanisms](03_CORE_MECHANISMS_INTERNAL/retry_mechanisms.md)** - Failure handling and recovery strategies for individual Redis operations

### 📚 04. Implementation Reference (Internal)
Technical reference for developers and maintainers.

- **[Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)** - Redis Lua script implementations for all atomic lock operations
- **[Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)** - Key naming conventions and structure, including lock-type segments
- **[Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md)** - Detailed API and class documentation

### 🔍 05. Operational Aspects
Production deployment, monitoring, and maintenance.

- **[Monitoring and Metrics](05_OPERATIONAL_ASPECTS/monitoring_and_metrics.md)** - Health checks, metrics, and observability
- **[Troubleshooting Guide](05_OPERATIONAL_ASPECTS/troubleshooting_guide.md)** - Common issues and diagnostic procedures
- **[Performance Tuning](05_OPERATIONAL_ASPECTS/performance_tuning.md)** - Optimization strategies and benchmarking

### 📈 06. History and Migration
Version management and upgrade procedures.

- **[Changelog](06_HISTORY_AND_MIGRATION/changelog.md)** - Version history and release notes
- **[Migration Guide](06_HISTORY_AND_MIGRATION/migration_guide.md)** - Step-by-step upgrade instructions

## Key Features

### 🔒 Lock Types
- **Reentrant Locks**: Traditional mutual exclusion with reentrancy support. Redis keys for reentrant locks include a `reentrant` segment for isolation.
- **State Locks**: Application state management with atomic transitions. Redis keys for state locks include a `state` segment for isolation.
- **Semaphore Locks**: Resource counting and rate limiting. Redis keys for semaphore locks include a `semaphore` segment for isolation.

### 🐕 Watchdog Service
- Automatic lease extension for long-running operations, leveraging `safetyBuffer` calculations.
- Configurable renewal `interval` and `factor` properties.
- Preserves `originalLeaseTimeMillis` during renewals.
- Graceful handling of application failures.

### 📊 Monitoring & Observability
- Comprehensive metrics via Micrometer.
- Health indicators for Spring Boot Actuator.
- Structured logging with MDC context.
- Performance monitoring and alerting.

### 🔄 Reliability Features
- Asynchronous-first operations leveraging Java Virtual Threads for enhanced scalability.
- Centralized idempotency protection against duplicate operations using `requestUuid` and `responseCacheTtl`.
- All Redis operations for lock state, TTL, and metadata are exclusively performed via Lua scripts for atomicity and consistency.
- Lua scripts adhere to standardized return conventions with status codes.
- Retry mechanisms with exponential backoff for transient Redis operation failures.
- Unlock notifications via Redis Pub/Sub for efficient lock coordination.
- Graceful degradation under failure conditions.

## Getting Started

### 1. Add Dependency

```xml
<dependency>
    <groupId>com.tui.destilink.framework</groupId>
    <artifactId>locking-redis-lock</artifactId>
    <version>2.1.0</version>
</dependency>
```

### 2. Configure Properties

```yaml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        stateKeyExpiration: PT5M # TTL for auxiliary state keys, e.g., for StateLock
        responseCacheTtl: PT10S # TTL for idempotency records (requestUuid cache)
        watchdog:
          interval: PT10S # Interval for watchdog checks
          factor: 0.5 # Multiplier for safetyBufferMillis (interval * factor)
          corePoolSize: 1 # Core thread pool size for watchdog's ScheduledExecutorService
          threadNamePrefix: redis-lock-watchdog-
          shutdownAwaitTermination: PT5S
        defaults:
          leaseTime: PT60S # Default lock lease duration
          retryInterval: PT0.1S # Default wait time between retries for individual Redis operations
          maxRetries: 3 # Default max retry attempts for individual Redis operations
          acquireTimeout: PT30S # Default overall timeout for lock acquisition
```

### 3. Use in Code

```java
@Service
@RequiredArgsConstructor
public class UserService {
    
    private final LockBucketRegistry lockBucketRegistry;
    
    public void updateUser(String userId) {
        var lock = lockBucketRegistry.builder("user-operations")
            .lockConfig("user-" + userId)
            .reentrantLock()
            .build();
            
        if (lock.tryAcquire()) {
            try {
                // Critical section
            } finally {
                lock.release();
            }
        }
    }
}
```

## Common Use Cases

All Redis keys for locks now include a lock-type segment (e.g., `reentrant`, `state`, `semaphore`) to ensure semantic isolation and prevent cross-type interference.

### Mutual Exclusion
Prevent concurrent access to shared resources:
```java
var lock = lockBucketRegistry.builder("resource-access")
    .lockConfig("shared-resource")
    .reentrantLock()
    .build();
```

### Application State Management
Coordinate application state transitions:
```java
var stateLock = lockBucketRegistry.builder("app-state")
    .lockConfig("deployment-status")
    .stateLock()
    .build();
```

### Rate Limiting
Control access to limited resources:
```java
var semaphore = lockBucketRegistry.builder("api-limits")
    .lockConfig("external-api")
    .semaphoreLock()
    .withPermits(10)
    .build();
```

## Best Practices

### 🎯 Lock Design
- Use fine-grained locking to minimize contention
- Keep critical sections as short as possible
- Choose appropriate lease times for your use case
- Use meaningful lock identifiers

### 🛡️ Error Handling
- Always use try-finally blocks for lock release
- Handle lock acquisition failures gracefully
- Implement proper timeout strategies
- Monitor lock metrics for operational insights

### 📈 Performance
- Leverage asynchronous operations with Virtual Threads for non-blocking I/O.
- Configure appropriate retry intervals and counts for individual Redis operations.
- The watchdog automatically extends leases for long-running operations, maintaining a `safetyBuffer`.
- Monitor lock acquisition latency and success rates.
- Optimize Redis configuration for your workload.

## Support and Maintenance

### Documentation Updates
This documentation is maintained alongside the codebase. For corrections or improvements:
1. Create an issue describing the documentation problem
2. Submit a pull request with proposed changes
3. Follow the established documentation standards

### Version Compatibility
- **Current Version**: 2.1.0
- **Minimum Java**: 17+
- **Spring Boot**: 3.1.x+
- **Redis**: 6.0+

### Getting Help
- **Framework Documentation**: Internal wiki and guides
- **Issue Tracking**: Project issue tracker
- **Team Support**: Framework team Slack channels

---

**Last Updated**: June 2025  
**Version**: 2.1.0  
**Maintainers**: Destilink Framework Team