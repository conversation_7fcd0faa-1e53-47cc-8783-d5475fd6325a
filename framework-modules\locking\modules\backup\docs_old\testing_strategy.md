# Redis Locking Module: Final Testing Strategy

## 1. Introduction

This document outlines the comprehensive testing strategy for the `locking-redis-lock` module, updated to reflect changes from the **Comprehensive Plan (Version 1.0, June 17, 2025)**, including **Virtual Thread adoption**, **centralized idempotency**, **refined watchdog**, and **lock-type specific keys**.

## 2. Testing Principles

*   **Layered Testing**: Unit, integration, performance.
*   **Correctness**: Core logic, reentrancy, state, R/W locks, especially with new `lockDataKey` and `originalLeaseTimeMillis` / `expiresAtMillis` handling.
*   **Atomicity**: Lua script atomicity, including idempotency wrapper.
*   **Distributed Simulation**: Concurrent clients (simulated via Virtual Threads).
*   **Error Handling**: Correct exceptions, `RedisLockOperationsImpl` retry logic for individual Redis ops.
*   **Configuration Validation**: New `RedisLockProperties` structure, watchdog `interval`/`factor`.
*   **Performance**: Validate benefits of Virtual Threads and non-polling.

## 3. Testing Layers

### 3.1. Unit Tests

*   **Focus**: Isolated components. Mock `RedisLockOperations` for `AbstractRedisLock` tests not hitting Redis.
*   **Examples**: `LockSemaphoreHolder` (CompletableFuture management), `RedisLockProperties` parsing, `RedisLockErrorHandler` mapping, parts of `AbstractRedisLock` (parameter prep, VT dispatch logic, MDC propagation).

### 3.2. Integration Tests (with Testcontainers/Embedded Redis)

*   **Focus**: Interactions with Redis, end-to-end lock behavior, concurrency.
*   **Redis Interaction**:
    *   Lua scripts load and execute correctly.
    *   `lockKey` (with `lockType` segment) and `lockDataKey` (Hash with `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`) are managed as expected.
    *   Idempotency: `responseCacheKey` (with `lockType`) created/used, `responseCacheTtl` respected. Operations are not re-executed on retry with same `requestUuid`.
*   **End-to-End Lock Behavior**:
    *   All lock types, acquisition methods (`lockAsync`, `tryLockAsync` on VTs).
    *   `RedisReadWriteLock`: Correct management of main R/W Hash, `Individual Read Lock Timeout Key`s, and overall TTL based on `safetyBufferMillis` / `userIntendedExpireTimeMillis` and active read leases.
*   **Concurrency (Simulated with many Virtual Threads)**:
    *   Multiple VTs concurrently acquiring/releasing same/different locks.
*   **Watchdog Mechanism**:
    *   Correct registration based on `userLeaseTime > safetyBufferMillis` and `canUseWatchdog()`.
    *   Correct initial TTL setting (`safetyBufferMillis` if monitored, else full lease).
    *   Watchdog extends leases using `PEXPIREAT` via `watchdog_refresh_lock.lua`, targeting `safetyBufferMillis` or `userIntendedExpireTimeMillis` (final leg).
    *   `originalLeaseTimeMillis` in `lockDataKey` is NOT changed by watchdog.
    *   User-initiated `extendLease` updates `originalLeaseTimeMillis` and watchdog registration.
*   **Pub/Sub Messaging & Non-Polling Wait**:
    *   Unlock notifications published to channels with `lockType` segment.
    *   `UnlockMessageListener` derives `lockName`/`lockType`, signals `LockSemaphoreHolder` (completes Future).
    *   `LockSemaphoreHolder` registered *before* first Redis attempt.
    *   Fallback re-attempt using `min(TTL, defaults.retryInterval)`.
*   **Error Handling & `RedisLockOperationsImpl` Retries**:
    *   Simulate Redis errors. Verify `RedisLockErrorHandler` translation.
    *   Test `RedisLockOperationsImpl` internal retry logic for *individual Redis ops* (`defaults.maxRetries`, `defaults.retryInterval`) for `RetryableLockException`.
    *   Test `acquireTimeout` nuances (non-interruption of in-flight Redis, result precedence).
*   **Configuration Scenarios**: Test with various `watchdog.interval`, `watchdog.factor`, `defaults.leaseTime`, `defaults.acquireTimeout`.

### 3.3. Performance Tests

*   **Focus**: Throughput, latency, resource consumption (client CPU minimal due to VTs, Redis CPU/memory).
*   **Scenarios**: Varying contention, lock types, with/without watchdog.
*   **Metrics**: Use `LockMonitor` (Micrometer).

## 4. Specific Testing Considerations

*   **Idempotency**: Test scenarios with simulated network failures causing client retries (with same `requestUuid`) to ensure operations are not duplicated. Verify response cache usage.
*   **Watchdog Logic**:
    *   Test eligibility (`userLeaseTime` vs `safetyBufferMillis`).
    *   Verify initial TTL setting.
    *   Verify `PEXPIREAT` usage and correct `targetAbsoluteExpiresAtMillis` calculation in standard and "final leg" scenarios.
    *   Confirm `originalLeaseTimeMillis` in `lockDataKey` is untouched by watchdog refreshes.
    *   Test user `extendLease` interaction with watchdog.
*   **Virtual Threads & MDC**: Ensure MDC context is correctly propagated and available in logs from VTs.
*   **`acquireTimeout` vs. In-flight Redis Ops**: Create scenarios where `acquireTimeout` might expire *during* a Redis call to verify precedence rules.
*   **Lock-Type Key Isolation**: Negative tests attempting to operate on a lock using the wrong type's operations or keys.
*   **`lockDataKey` Management**: Ensure `lockDataKey` is created, updated (with `ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`), and deleted along with `lockKey`.

## 5. Test Coverage
*   High code coverage. Focus on observable behavior.
*   Cover all public API methods, edge cases, error conditions.