# Redis Locking Module: Component Reference

## 1. Introduction

This document provides a comprehensive reference for all major components in the Redis locking module. It serves as a technical reference for developers working with or extending the module, covering configuration classes, service implementations, builders, and utility components.

For architectural context, see [Architecture Overview](../01_OVERVIEW_AND_CONCEPTS/architecture.md). For class relationships, see [Class Hierarchy](../01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md).

## 2. Configuration Components

### 2.1. RedisLockProperties

**Package**: `com.tui.destilink.framework.locking.redis.config`

**Purpose**: Main configuration properties class for the Redis locking module.

```java
@Data
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
@Validated
public class RedisLockProperties {
    
    /**
     * Enable or disable the Redis locking module
     */
    private boolean enabled = true;
    
    /**
     * Enable or disable unlock message listener (Pub/Sub)
     */
    private boolean unlockMessageListenerEnabled = true;
    
    /**
     * Default expiration time for state keys
     */
    @DurationMin(seconds = 1)
    private Duration stateKeyExpiration = Duration.ofMinutes(5);
    
    /**
     * TTL for idempotency cache entries in seconds
     */
    @Min(1)
    private int uuidCacheTtlSeconds = 300;
    
    /**
     * Enable health indicator for the locking module
     */
    private boolean healthIndicatorEnabled = true;
    
    /**
     * Custom lock owner supplier bean name
     */
    private String lockOwnerSupplierBeanName;
    
    /**
     * Watchdog configuration
     */
    private WatchdogProperties watchdog = new WatchdogProperties();
    
    /**
     * Default lock implementation settings
     */
    private Defaults defaults = new Defaults();
    
    @Data
    @Validated
    public static class WatchdogProperties {
        
        /**
         * Enable or disable the watchdog service
         */
        private boolean enabled = true;
        
        /**
         * Interval between watchdog renewal cycles
         */
        @DurationMin(seconds = 1)
        private Duration interval = Duration.ofSeconds(5);
        
        /**
         * Minimum lease time required for watchdog activation
         */
        @DurationMin(seconds = 5)
        private Duration minLeaseTimeForActivation = Duration.ofSeconds(10);
        
        /**
         * Skip renewal if lock TTL is above this threshold
         */
        @DurationMin(seconds = 1)
        private Duration maxTtlForRenewalCheck = Duration.ofSeconds(15);
        
        /**
         * Core pool size for watchdog executor
         */
        @Min(1)
        private int corePoolSize = 2;
        
        /**
         * Thread name prefix for watchdog threads
         */
        @NotBlank
        private String threadNamePrefix = "dl-lock-watchdog-";
        
        /**
         * Maximum time to wait for executor termination during shutdown
         */
        @DurationMin(seconds = 1)
        private Duration shutdownAwaitTermination = Duration.ofSeconds(30);
        
        /**
         * Timeout for Redis operations performed by watchdog
         */
        @DurationMin(millis = 100)
        private Duration redisOperationTimeout = Duration.ofSeconds(1);
    }
    
    @Data
    @Validated
    public static class Defaults {
        
        /**
         * Default lease time for locks
         */
        @DurationMin(seconds = 1)
        private Duration leaseTime = Duration.ofSeconds(60);
        
        /**
         * Default retry interval between lock acquisition attempts
         */
        @DurationMin(millis = 10)
        private Duration retryInterval = Duration.ofMillis(100);
        
        /**
         * Default maximum number of retry attempts
         */
        @Min(0)
        private int maxRetries = 3;
        
        /**
         * Default timeout for lock acquisition operations
         */
        @DurationMin(seconds = 1)
        private Duration acquireTimeout = Duration.ofSeconds(5);
        
        /**
         * Default timeout for individual Redis operations
         */
        @DurationMin(millis = 100)
        private Duration redisOperationTimeout = Duration.ofMillis(500);
    }
}
```

### 2.2. RedisLockAutoConfiguration

**Package**: `com.tui.destilink.framework.locking.redis.config`

**Purpose**: Main auto-configuration class that sets up all Redis locking components.

```java
@AutoConfiguration
@EnableConfigurationProperties({RedisLockProperties.class})
@ConditionalOnProperty(
    prefix = "destilink.fw.locking.redis", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
@ConditionalOnClass({RedisTemplate.class, RedisConnectionFactory.class})
@RequiredArgsConstructor
@Slf4j
public class RedisLockAutoConfiguration {
    
    private final RedisLockProperties properties;
    
    @Bean
    @ConditionalOnMissingBean
    public LockOwnerSupplier lockOwnerSupplier() {
        return new DefaultLockOwnerSupplier();
    }
    
    @Bean
    @ConditionalOnMissingBean
    public LockBucketRegistry lockBucketRegistry(
            RedisTemplate<String, String> redisTemplate,
            LockOwnerSupplier lockOwnerSupplier,
            @Lazy LockWatchdog lockWatchdog) {
        return new DefaultLockBucketRegistry(redisTemplate, properties, lockOwnerSupplier, lockWatchdog);
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(
        prefix = "destilink.fw.locking.redis.watchdog", 
        name = "enabled", 
        havingValue = "true", 
        matchIfMissing = true
    )
    static class WatchdogConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public LockWatchdog lockWatchdog(
                RedisTemplate<String, String> redisTemplate,
                RedisLockProperties properties,
                LockOwnerSupplier lockOwnerSupplier) {
            return new DefaultLockWatchdog(redisTemplate, properties, lockOwnerSupplier);
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(
        prefix = "destilink.fw.locking.redis", 
        name = "unlock-message-listener-enabled", 
        havingValue = "true", 
        matchIfMissing = true
    )
    static class PubSubConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public LockNotificationRegistry lockNotificationRegistry() {
            return new DefaultLockNotificationRegistry();
        }
        
        @Bean
        @ConditionalOnMissingBean
        public LockUnlockMessageListener lockUnlockMessageListener(
                LockNotificationRegistry notificationRegistry) {
            return new LockUnlockMessageListener(notificationRegistry);
        }
        
        @Bean
        @ConditionalOnMissingBean
        public LockUnlockPublisher lockUnlockPublisher(
                RedisTemplate<String, String> redisTemplate,
                RedisLockProperties properties) {
            return new LockUnlockPublisher(redisTemplate, properties);
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(
        prefix = "destilink.fw.locking.redis", 
        name = "health-indicator-enabled", 
        havingValue = "true", 
        matchIfMissing = true
    )
    static class HealthConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public RedisLockHealthIndicator redisLockHealthIndicator(
                RedisTemplate<String, String> redisTemplate,
                LockBucketRegistry lockBucketRegistry) {
            return new RedisLockHealthIndicator(redisTemplate, lockBucketRegistry);
        }
    }
    
    @Configuration(proxyBeanMethods = false)
    @Import({
        LuaScriptRegistry.class,
        IdempotentOperationExecutor.class,
        LockRetryExecutor.class
    })
    static class UtilityConfiguration {
        // Utility components imported via @Import
    }
}
```

### 2.3. LockBucketConfig

**Package**: `com.tui.destilink.framework.locking.redis.config`

**Purpose**: Configuration object for individual lock buckets, containing resolved settings.

```java
@Data
@Builder
@AllArgsConstructor
public class LockBucketConfig {
    
    /**
     * Bucket name
     */
    @NonNull
    private final String bucketName;
    
    /**
     * Application instance ID for lock ownership
     */
    @NonNull
    private final String applicationInstanceId;
    
    /**
     * Lock scope (APPLICATION_INSTANCE or GLOBAL)
     */
    @NonNull
    @Builder.Default
    private final LockScope scope = LockScope.APPLICATION_INSTANCE;
    
    /**
     * Lock owner supplier for this bucket
     */
    @NonNull
    private final LockOwnerSupplier lockOwnerSupplier;
    
    /**
     * Default lease time for locks in this bucket
     */
    @NonNull
    @Builder.Default
    private final Duration defaultLeaseTime = Duration.ofSeconds(60);
    
    /**
     * Default retry interval for lock acquisition
     */
    @NonNull
    @Builder.Default
    private final Duration defaultRetryInterval = Duration.ofMillis(100);
    
    /**
     * Default maximum retry attempts
     */
    @Builder.Default
    private final int defaultMaxRetries = 3;
    
    /**
     * Default acquisition timeout
     */
    @NonNull
    @Builder.Default
    private final Duration defaultAcquireTimeout = Duration.ofSeconds(5);
    
    /**
     * Default Redis operation timeout
     */
    @NonNull
    @Builder.Default
    private final Duration defaultRedisOperationTimeout = Duration.ofMillis(500);
    
    /**
     * Default state key expiration for state locks
     */
    @NonNull
    @Builder.Default
    private final Duration defaultStateKeyExpiration = Duration.ofMinutes(5);
    
    /**
     * Create a copy with modified lease time
     */
    public LockBucketConfig withDefaultLeaseTime(Duration leaseTime) {
        return toBuilder().defaultLeaseTime(leaseTime).build();
    }
    
    /**
     * Create a copy with modified retry interval
     */
    public LockBucketConfig withDefaultRetryInterval(Duration retryInterval) {
        return toBuilder().defaultRetryInterval(retryInterval).build();
    }
    
    /**
     * Create a copy with modified max retries
     */
    public LockBucketConfig withDefaultMaxRetries(int maxRetries) {
        return toBuilder().defaultMaxRetries(maxRetries).build();
    }
    
    /**
     * Create a copy with modified acquire timeout
     */
    public LockBucketConfig withDefaultAcquireTimeout(Duration acquireTimeout) {
        return toBuilder().defaultAcquireTimeout(acquireTimeout).build();
    }
    
    /**
     * Create a copy with modified Redis operation timeout
     */
    public LockBucketConfig withDefaultRedisOperationTimeout(Duration redisOperationTimeout) {
        return toBuilder().defaultRedisOperationTimeout(redisOperationTimeout).build();
    }
    
    /**
     * Create a copy with modified state key expiration
     */
    public LockBucketConfig withDefaultStateKeyExpiration(Duration stateKeyExpiration) {
        return toBuilder().defaultStateKeyExpiration(stateKeyExpiration).build();
    }
}
```

## 3. Core Service Components

### 3.1. LockBucketRegistry

**Package**: `com.tui.destilink.framework.locking.redis.service`

**Purpose**: Main entry point for creating locks, provides builder pattern interface.

```java
public interface LockBucketRegistry {
    
    /**
     * Create a lock bucket builder with default application instance ID
     */
    LockBucketBuilder builder(String bucketName);
    
    /**
     * Create a lock bucket builder with custom application instance ID
     */
    LockBucketBuilder builder(String bucketName, String applicationInstanceId);
    
    /**
     * Get statistics about active locks
     */
    LockStatistics getStatistics();
    
    /**
     * Get health information
     */
    LockHealthInfo getHealthInfo();
}
```

**Default Implementation**:

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class DefaultLockBucketRegistry implements LockBucketRegistry {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisLockProperties properties;
    private final LockOwnerSupplier lockOwnerSupplier;
    private final LockWatchdog lockWatchdog;
    private final ApplicationInstanceIdProvider instanceIdProvider;
    
    @Override
    public LockBucketBuilder builder(String bucketName) {
        String instanceId = instanceIdProvider.getApplicationInstanceId();
        return builder(bucketName, instanceId);
    }
    
    @Override
    public LockBucketBuilder builder(String bucketName, String applicationInstanceId) {
        validateBucketName(bucketName);
        validateApplicationInstanceId(applicationInstanceId);
        
        LockBucketConfig config = createBucketConfig(bucketName, applicationInstanceId);
        return new DefaultLockBucketBuilder(redisTemplate, config, lockWatchdog);
    }
    
    private LockBucketConfig createBucketConfig(String bucketName, String applicationInstanceId) {
        return LockBucketConfig.builder()
            .bucketName(bucketName)
            .applicationInstanceId(applicationInstanceId)
            .lockOwnerSupplier(lockOwnerSupplier)
            .defaultLeaseTime(properties.getDefaults().getLeaseTime())
            .defaultRetryInterval(properties.getDefaults().getRetryInterval())
            .defaultMaxRetries(properties.getDefaults().getMaxRetries())
            .defaultAcquireTimeout(properties.getDefaults().getAcquireTimeout())
            .defaultRedisOperationTimeout(properties.getDefaults().getRedisOperationTimeout())
            .defaultStateKeyExpiration(properties.getStateKeyExpiration())
            .build();
    }
    
    private void validateBucketName(String bucketName) {
        if (bucketName == null || bucketName.trim().isEmpty()) {
            throw new IllegalArgumentException("Bucket name cannot be null or empty");
        }
        if (bucketName.contains(":")) {
            throw new IllegalArgumentException("Bucket name cannot contain colons");
        }
    }
    
    private void validateApplicationInstanceId(String applicationInstanceId) {
        if (applicationInstanceId == null || applicationInstanceId.trim().isEmpty()) {
            throw new IllegalArgumentException("Application instance ID cannot be null or empty");
        }
    }
    
    @Override
    public LockStatistics getStatistics() {
        // Implementation for collecting lock statistics
        return LockStatistics.builder()
            .totalActiveLocks(0) // Placeholder
            .totalWatchdogManagedLocks(lockWatchdog.getMonitoredLocksCount())
            .build();
    }
    
    @Override
    public LockHealthInfo getHealthInfo() {
        // Implementation for health information
        return LockHealthInfo.builder()
            .redisConnected(checkRedisConnection())
            .watchdogActive(lockWatchdog.isActive())
            .build();
    }
    
    private boolean checkRedisConnection() {
        try {
            redisTemplate.hasKey("health-check");
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 3.2. LockWatchdog

**Package**: `com.tui.destilink.framework.locking.redis.service`

**Purpose**: Manages automatic lease extension for long-running locks.

```java
public interface LockWatchdog {
    
    /**
     * Register a lock for watchdog monitoring
     */
    void registerLock(String lockKey, String ownerId, Duration leaseTime, 
                     Supplier<Boolean> lockValidator);
    
    /**
     * Deregister a lock from watchdog monitoring
     */
    void deregisterLock(String lockKey);
    
    /**
     * Check if watchdog should monitor a specific lock
     */
    boolean shouldMonitor(String lockKey, String ownerId, Duration leaseTime);
    
    /**
     * Get count of currently monitored locks
     */
    int getMonitoredLocksCount();
    
    /**
     * Check if watchdog is active
     */
    boolean isActive();
    
    /**
     * Get last renewal cycle time
     */
    Instant getLastRenewalCycleTime();
}
```

**Default Implementation**:

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class DefaultLockWatchdog implements LockWatchdog, DisposableBean {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisLockProperties properties;
    private final LockOwnerSupplier lockOwnerSupplier;
    
    private final Map<String, WatchdogEntry> monitoredLocks = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduledExecutor;
    private final AtomicBoolean active = new AtomicBoolean(false);
    private volatile Instant lastRenewalCycleTime = Instant.now();
    
    @PostConstruct
    public void start() {
        if (properties.getWatchdog().isEnabled()) {
            scheduledExecutor.scheduleWithFixedDelay(
                this::renewLocks,
                properties.getWatchdog().getInterval().toMillis(),
                properties.getWatchdog().getInterval().toMillis(),
                TimeUnit.MILLISECONDS
            );
            active.set(true);
            log.info("Lock watchdog started with interval: {}", properties.getWatchdog().getInterval());
        }
    }
    
    @Override
    public void registerLock(String lockKey, String ownerId, Duration leaseTime, 
                           Supplier<Boolean> lockValidator) {
        if (!shouldMonitor(lockKey, ownerId, leaseTime)) {
            log.debug("Lock not eligible for watchdog monitoring: {}", lockKey);
            return;
        }
        
        WatchdogEntry entry = WatchdogEntry.builder()
            .lockKey(lockKey)
            .ownerId(ownerId)
            .leaseTime(leaseTime)
            .lockValidator(lockValidator)
            .registrationTime(Instant.now())
            .lastExtensionTime(Instant.now())
            .build();
        
        monitoredLocks.put(lockKey, entry);
        log.debug("Registered lock for watchdog monitoring: {} (owner: {})", lockKey, ownerId);
    }
    
    @Override
    public void deregisterLock(String lockKey) {
        WatchdogEntry removed = monitoredLocks.remove(lockKey);
        if (removed != null) {
            log.debug("Deregistered lock from watchdog: {} (extensions: {})", 
                     lockKey, removed.getExtensionCount());
        }
    }
    
    @Override
    public boolean shouldMonitor(String lockKey, String ownerId, Duration leaseTime) {
        return properties.getWatchdog().isEnabled() &&
               lockOwnerSupplier.canUseWatchdog(lockKey, ownerId) &&
               leaseTime.compareTo(properties.getWatchdog().getMinLeaseTimeForActivation()) >= 0;
    }
    
    @Scheduled(fixedDelayString = "#{@redisLockProperties.watchdog.interval}")
    public void renewLocks() {
        if (!active.get()) {
            return;
        }
        
        lastRenewalCycleTime = Instant.now();
        
        List<CompletableFuture<Void>> renewalTasks = monitoredLocks.values().stream()
            .map(this::renewLockAsync)
            .collect(Collectors.toList());
        
        CompletableFuture.allOf(renewalTasks.toArray(new CompletableFuture[0]))
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("Error during watchdog renewal cycle", throwable);
                }
            });
    }
    
    private CompletableFuture<Void> renewLockAsync(WatchdogEntry entry) {
        return CompletableFuture.runAsync(() -> renewLock(entry), scheduledExecutor);
    }
    
    private void renewLock(WatchdogEntry entry) {
        try {
            // Validate lock is still active and owned by us
            if (!entry.getLockValidator().get()) {
                log.debug("Lock validation failed, removing from watchdog: {}", entry.getLockKey());
                monitoredLocks.remove(entry.getLockKey());
                return;
            }
            
            // Check if renewal is needed
            Long remainingTtl = redisTemplate.getExpire(entry.getLockKey(), TimeUnit.MILLISECONDS);
            if (remainingTtl == null || remainingTtl <= 0) {
                log.warn("Lock no longer exists in Redis, removing from watchdog: {}", entry.getLockKey());
                monitoredLocks.remove(entry.getLockKey());
                return;
            }
            
            Duration remainingDuration = Duration.ofMillis(remainingTtl);
            Duration maxTtlForRenewal = properties.getWatchdog().getMaxTtlForRenewalCheck();
            
            if (remainingDuration.compareTo(maxTtlForRenewal) > 0) {
                log.debug("Lock TTL still high, skipping renewal: {} (remaining: {})", 
                         entry.getLockKey(), remainingDuration);
                return;
            }
            
            // Perform renewal
            boolean renewed = extendLockTtl(entry);
            if (renewed) {
                entry.setLastExtensionTime(Instant.now());
                entry.setExtensionCount(entry.getExtensionCount() + 1);
                log.debug("Successfully renewed lock: {} (extensions: {})", 
                         entry.getLockKey(), entry.getExtensionCount());
            } else {
                log.warn("Failed to renew lock, removing from watchdog: {}", entry.getLockKey());
                monitoredLocks.remove(entry.getLockKey());
            }
            
        } catch (Exception e) {
            log.error("Error renewing lock: {}", entry.getLockKey(), e);
        }
    }
    
    private boolean extendLockTtl(WatchdogEntry entry) {
        // Implementation using Lua script for atomic TTL extension
        // See lua_scripts.md for script details
        return true; // Placeholder
    }
    
    @Override
    public int getMonitoredLocksCount() {
        return monitoredLocks.size();
    }
    
    @Override
    public boolean isActive() {
        return active.get();
    }
    
    @Override
    public Instant getLastRenewalCycleTime() {
        return lastRenewalCycleTime;
    }
    
    @Override
    public void destroy() throws Exception {
        active.set(false);
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(
                        properties.getWatchdog().getShutdownAwaitTermination().toSeconds(), 
                        TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduledExecutor.shutdownNow();
            }
        }
        monitoredLocks.clear();
        log.info("Lock watchdog stopped");
    }
}
```

### 3.3. LockOwnerSupplier

**Package**: `com.tui.destilink.framework.locking.redis.service`

**Purpose**: Provides lock owner identification and watchdog eligibility logic.

```java
public interface LockOwnerSupplier {
    
    /**
     * Generate a unique owner ID for the current context
     */
    String generateOwnerId();
    
    /**
     * Check if the watchdog can manage locks for this owner ID
     */
    boolean canUseWatchdog(String lockKey, String ownerId);
    
    /**
     * Get the application instance ID component of an owner ID
     */
    Optional<String> extractApplicationInstanceId(String ownerId);
}
```

**Default Implementation**:

```java
@Component
@RequiredArgsConstructor
public class DefaultLockOwnerSupplier implements LockOwnerSupplier {
    
    private final ApplicationInstanceIdProvider instanceIdProvider;
    
    @Override
    public String generateOwnerId() {
        String instanceId = instanceIdProvider.getApplicationInstanceId();
        String threadId = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();
        
        return String.format("%s:%s:%d", instanceId, threadId, timestamp);
    }
    
    @Override
    public boolean canUseWatchdog(String lockKey, String ownerId) {
        Optional<String> ownerInstanceId = extractApplicationInstanceId(ownerId);
        if (ownerInstanceId.isEmpty()) {
            return false;
        }
        
        String currentInstanceId = instanceIdProvider.getApplicationInstanceId();
        return currentInstanceId.equals(ownerInstanceId.get());
    }
    
    @Override
    public Optional<String> extractApplicationInstanceId(String ownerId) {
        if (ownerId == null || !ownerId.contains(":")) {
            return Optional.empty();
        }
        
        String[] parts = ownerId.split(":", 2);
        return Optional.of(parts[0]);
    }
}
```

## 4. Builder Components

### 4.1. LockBucketBuilder

**Package**: `com.tui.destilink.framework.locking.redis.builder`

**Purpose**: Builder for configuring lock buckets and transitioning to lock configuration.

```java
public interface LockBucketBuilder {
    
    /**
     * Set the lock scope for this bucket
     */
    LockBucketBuilder withScope(LockScope scope);
    
    /**
     * Set default lease time for locks in this bucket
     */
    LockBucketBuilder withDefaultLeaseTime(Duration leaseTime);
    
    /**
     * Set default retry interval for locks in this bucket
     */
    LockBucketBuilder withDefaultRetryInterval(Duration retryInterval);
    
    /**
     * Set default maximum retries for locks in this bucket
     */
    LockBucketBuilder withDefaultMaxRetries(int maxRetries);
    
    /**
     * Set default acquire timeout for locks in this bucket
     */
    LockBucketBuilder withDefaultAcquireTimeout(Duration acquireTimeout);
    
    /**
     * Set default Redis operation timeout for locks in this bucket
     */
    LockBucketBuilder withDefaultRedisOperationTimeout(Duration redisOperationTimeout);
    
    /**
     * Set default state key expiration for state locks in this bucket
     */
    LockBucketBuilder withDefaultStateKeyExpiration(Duration stateKeyExpiration);
    
    /**
     * Begin configuration for a specific lock
     */
    LockConfigBuilder lockConfig(String lockIdentifier);
}
```

### 4.2. LockConfigBuilder

**Package**: `com.tui.destilink.framework.locking.redis.builder`

**Purpose**: Builder for selecting lock type and transitioning to type-specific configuration.

```java
public interface LockConfigBuilder {
    
    /**
     * Configure a reentrant lock
     */
    ReentrantLockConfigBuilder reentrantLock();
    
    /**
     * Configure a state lock
     */
    StateLockConfigBuilder stateLock();
    
    /**
     * Configure a semaphore lock
     */
    SemaphoreLockConfigBuilder semaphoreLock();
}
```

### 4.3. AbstractLockTypeConfigBuilder

**Package**: `com.tui.destilink.framework.locking.redis.builder`

**Purpose**: Base builder for common lock configuration options.

```java
public abstract class AbstractLockTypeConfigBuilder<T extends AbstractLockTypeConfigBuilder<T>> {
    
    protected final RedisTemplate<String, String> redisTemplate;
    protected final LockBucketConfig bucketConfig;
    protected final String lockKey;
    protected final LockWatchdog lockWatchdog;
    
    // Configuration overrides
    protected Duration leaseTime;
    protected Duration retryInterval;
    protected Integer maxRetries;
    protected Duration acquireTimeout;
    protected Duration redisOperationTimeout;
    
    protected AbstractLockTypeConfigBuilder(RedisTemplate<String, String> redisTemplate,
                                          LockBucketConfig bucketConfig,
                                          String lockKey,
                                          LockWatchdog lockWatchdog) {
        this.redisTemplate = redisTemplate;
        this.bucketConfig = bucketConfig;
        this.lockKey = lockKey;
        this.lockWatchdog = lockWatchdog;
    }
    
    /**
     * Override lease time for this specific lock instance
     */
    @SuppressWarnings("unchecked")
    public T withLeaseTime(Duration leaseTime) {
        this.leaseTime = leaseTime;
        return (T) this;
    }
    
    /**
     * Override retry interval for this specific lock instance
     */
    @SuppressWarnings("unchecked")
    public T withRetryInterval(Duration retryInterval) {
        this.retryInterval = retryInterval;
        return (T) this;
    }
    
    /**
     * Override max retries for this specific lock instance
     */
    @SuppressWarnings("unchecked")
    public T withMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
        return (T) this;
    }
    
    /**
     * Override acquire timeout for this specific lock instance
     */
    @SuppressWarnings("unchecked")
    public T withAcquireTimeout(Duration acquireTimeout) {
        this.acquireTimeout = acquireTimeout;
        return (T) this;
    }
    
    /**
     * Override Redis operation timeout for this specific lock instance
     */
    @SuppressWarnings("unchecked")
    public T withRedisOperationTimeout(Duration redisOperationTimeout) {
        this.redisOperationTimeout = redisOperationTimeout;
        return (T) this;
    }
    
    /**
     * Get effective lease time (instance override or bucket default)
     */
    protected Duration getEffectiveLeaseTime() {
        return leaseTime != null ? leaseTime : bucketConfig.getDefaultLeaseTime();
    }
    
    /**
     * Get effective retry interval (instance override or bucket default)
     */
    protected Duration getEffectiveRetryInterval() {
        return retryInterval != null ? retryInterval : bucketConfig.getDefaultRetryInterval();
    }
    
    /**
     * Get effective max retries (instance override or bucket default)
     */
    protected int getEffectiveMaxRetries() {
        return maxRetries != null ? maxRetries : bucketConfig.getDefaultMaxRetries();