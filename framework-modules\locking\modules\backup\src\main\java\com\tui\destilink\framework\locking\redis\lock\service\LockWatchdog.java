package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.annotation.PreDestroy;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service that extends lock leases automatically to prevent premature
 * expiration.
 * <p>
 * The LockWatchdog monitors active locks and periodically extends their leases
 * to ensure they don't expire while still in use. This is critical for
 * long-running
 * operations that exceed the initial lock timeout.
 * </p>
 * <p>
 * Each lock is registered with the watchdog when acquired, and unregistered
 * when
 * released. The watchdog uses a scheduled executor to periodically check and
 * extend
 * locks that are still active.
 * </p>
 */
public class LockWatchdog {

    private static final Logger log = LoggerFactory.getLogger(LockWatchdog.class);

    private final RedisLockOperations lockOperations;
    private final RedisLockErrorHandler errorHandler;
    private final RedisLockProperties.WatchdogProperties watchdogProperties;
    private final ScheduledExecutorService scheduler;
    private final Map<String, WatchedLock> watchedLocks = new ConcurrentHashMap<>();

    /**
     * Creates a new LockWatchdog instance.
     *
     * @param lockOperations     The Redis lock operations service
     * @param errorHandler       The error handler for lock-related errors
     * @param watchdogProperties The configuration properties for the watchdog.
     */
    public LockWatchdog(RedisLockOperations lockOperations,
            RedisLockErrorHandler errorHandler,
            RedisLockProperties.WatchdogProperties watchdogProperties) {
        this.lockOperations = lockOperations;
        this.errorHandler = errorHandler;
        this.watchdogProperties = watchdogProperties;
        // Initialize scheduler here, so watchdogProperties is available
        this.scheduler = Executors.newScheduledThreadPool(
                this.watchdogProperties.getCorePoolSize(),
                r -> {
                    // Use a more robust way to make thread names unique if necessary, e.g.,
                    // AtomicInteger
                    Thread t = new Thread(r,
                            this.watchdogProperties.getThreadNamePrefix() + "pool-" + this.hashCode() + "-thread-");
                    t.setDaemon(true);
                    return t;
                });

        log.info("LockWatchdog initialized with corePoolSize: {}, threadNamePrefix: {}, interval: {}",
                this.watchdogProperties.getCorePoolSize(), this.watchdogProperties.getThreadNamePrefix(),
                this.watchdogProperties.getInterval());
    }

    /**
     * Registers a lock to be monitored by the watchdog.
     *
     * @param lockName      The name of the lock
     * @param lockOwner     The owner identifier of the lock
     * @param leaseDuration The duration of the lock lease
     * @return A handle that can be used to unregister the lock
     */
    public WatchdogHandle registerLock(String lockName, String lockOwner,
            Duration leaseDuration) {
        // The extension interval is now directly from watchdogProperties.getInterval()
        Duration extensionInterval = watchdogProperties.getInterval();

        if (extensionInterval.compareTo(leaseDuration) >= 0) {
            // This check might still be relevant if the configured interval is too large
            // for a short lease
            log.warn(
                    "Watchdog extension interval ({}) is greater than or equal to lease duration ({}) for lock '{}'. Watchdog might not be effective.",
                    extensionInterval, leaseDuration, lockName);
        }

        log.debug("Registering lock '{}' with owner '{}' for watchdog monitoring with extension interval: {}",
                lockName, lockOwner, extensionInterval);

        WatchedLock watchedLock = new WatchedLock(lockName, lockOwner, leaseDuration);

        // Schedule periodic extension using the interval from WatchdogProperties
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
                () -> extendLock(watchedLock),
                extensionInterval.toMillis(), // Initial delay can also be the interval
                extensionInterval.toMillis(),
                TimeUnit.MILLISECONDS);

        watchedLock.setFuture(future);
        watchedLocks.put(createKey(lockName, lockOwner), watchedLock);

        return () -> unregisterLock(lockName, lockOwner);
    }

    /**
     * Unregisters a lock from watchdog monitoring.
     *
     * @param lockName  The name of the lock
     * @param lockOwner The owner identifier of the lock
     */
    public void unregisterLock(String lockName, String lockOwner) {
        String key = createKey(lockName, lockOwner);
        WatchedLock watchedLock = watchedLocks.remove(key);

        if (watchedLock != null && watchedLock.getFuture() != null) {
            watchedLock.getFuture().cancel(false);
            log.debug("Unregistered lock '{}' with owner '{}' from watchdog monitoring",
                    lockName, lockOwner);
        }
    }

    /**
     * Extends the lease of a watched lock.
     *
     * @param watchedLock The lock to extend
     */
    private void extendLock(WatchedLock watchedLock) {
        // Simple retry mechanism (can be made more sophisticated)
        int attempt = 0;
        boolean extendedSuccessfully = false;
        Exception lastException = null;

        while (attempt <= watchdogProperties.getOperationMaxRetries() && !extendedSuccessfully) {
            if (attempt > 0) {
                log.debug("Retrying lock extension for '{}', owner '{}', attempt {}/{}",
                        watchedLock.getLockName(), watchedLock.getLockOwner(), attempt,
                        watchdogProperties.getOperationMaxRetries());
                // Optional: add a small delay between retries
                try {
                    Thread.sleep(Math.min(attempt * 100L, 1000L)); // Basic backoff
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    errorHandler.handleLockExtensionError(watchedLock.getLockName(), e, watchedLock.getLockOwner());
                    return; // Stop if interrupted
                }
            }
            try {
                boolean extended = lockOperations.extendLock(
                        watchedLock.getLockName(),
                        watchedLock.getLockOwner(),
                        watchedLock.getLeaseDuration()) // The lease duration to set on extension
                        .orTimeout(watchdogProperties.getOperationTimeout().toMillis(), TimeUnit.MILLISECONDS)
                        .join(); // Using join for simplicity in this synchronous retry loop

                if (extended) {
                    log.debug("Extended lock '{}' with owner '{}'",
                            watchedLock.getLockName(), watchedLock.getLockOwner());
                    extendedSuccessfully = true;
                } else {
                    // If not extended, it might mean the lock was lost or owner changed.
                    // No need to retry in this case as per typical watchdog logic.
                    log.warn(
                            "Failed to extend lock '{}' with owner '{}' - lock may have been lost or owner changed. Not retrying.",
                            watchedLock.getLockName(), watchedLock.getLockOwner());
                    unregisterLock(watchedLock.getLockName(), watchedLock.getLockOwner());
                    return; // Exit after unregistering
                }
            } catch (Exception e) {
                lastException = e;
                log.warn("Exception during lock extension for '{}', owner '{}', attempt {}/{}: {}",
                        watchedLock.getLockName(), watchedLock.getLockOwner(), attempt,
                        watchdogProperties.getOperationMaxRetries(), e.getMessage());
            }
            attempt++;
        }

        if (!extendedSuccessfully) {
            log.error("Failed to extend lock '{}' with owner '{}' after {} retries. Last error: {}",
                    watchedLock.getLockName(), watchedLock.getLockOwner(), watchdogProperties.getOperationMaxRetries(),
                    lastException != null ? lastException.getMessage() : "N/A");
            errorHandler.handleLockExtensionError(
                    watchedLock.getLockName(),
                    lastException != null ? lastException
                            : new RuntimeException("Max retries reached for lock extension"),
                    watchedLock.getLockOwner());
            // Remove from watched locks since extension failed definitively
            unregisterLock(watchedLock.getLockName(), watchedLock.getLockOwner());
        }
    }

    private String createKey(String lockName, String lockOwner) {
        return lockName + ":" + lockOwner;
    }

    /**
     * Shuts down the watchdog, canceling all scheduled tasks.
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down LockWatchdog");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(watchdogProperties.getShutdownAwaitTermination().toMillis(),
                    TimeUnit.MILLISECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Functional interface for watchdog handle that can be used to unregister a
     * lock.
     */
    @FunctionalInterface
    public interface WatchdogHandle {
        /**
         * Unregisters the lock from watchdog monitoring.
         */
        void unregister();
    }

    /**
     * Internal class representing a watched lock.
     */
    private static class WatchedLock {
        private final String lockName;
        private final String lockOwner;
        private final Duration leaseDuration;
        private ScheduledFuture<?> future;

        public WatchedLock(String lockName, String lockOwner, Duration leaseDuration) {
            this.lockName = lockName;
            this.lockOwner = lockOwner;
            this.leaseDuration = leaseDuration;
        }

        public String getLockName() {
            return lockName;
        }

        public String getLockOwner() {
            return lockOwner;
        }

        public Duration getLeaseDuration() {
            return leaseDuration;
        }

        public ScheduledFuture<?> getFuture() {
            return future;
        }

        public void setFuture(ScheduledFuture<?> future) {
            this.future = future;
        }
    }
}