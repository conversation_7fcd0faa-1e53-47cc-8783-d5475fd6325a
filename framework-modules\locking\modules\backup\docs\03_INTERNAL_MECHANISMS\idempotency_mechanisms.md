# Idempotency Mechanisms

## Table of Contents
1. [Introduction](#introduction)
2. [Centralized Idempotency System](#centralized-idempotency-system)
3. [Idempotency Architecture](#idempotency-architecture)
4. [Integration Points](#integration-points)
5. [Implementation Details](#implementation-details)
6. [Configuration](#configuration)
7. [Performance Characteristics](#performance-characteristics)
8. [<PERSON><PERSON><PERSON> and <PERSON> Cases](#error-handling-and-edge-cases)

## Introduction

This document describes the comprehensive idempotency mechanisms implemented in the modernized Redis locking framework. Idempotency ensures that operations are performed exactly once, even if the same request is received multiple times. This is crucial for maintaining data consistency and preventing duplicate operations in distributed systems.

## Centralized Idempotency System

The centralized idempotency system is a core architectural component of the Redis locking framework. It provides a robust mechanism to handle duplicate requests and ensure exactly-once semantics for lock operations.

### Key Components

1. **requestUuid**: A unique identifier for each operation request.
2. **responseCacheTtl**: Time-to-live (TTL) for cached idempotent responses.

### Request Deduplication

The system prevents duplicate operations by tracking requests using UUID-based identifiers. When a request is received, the system checks if an operation with the same `requestUuid` has already been processed or is in progress.

### Response Caching

Successful operation responses are cached for a configurable duration (`responseCacheTtl`). This allows the system to quickly return the same result for duplicate requests without re-executing the operation.

## Idempotency Architecture

### UUID Generation

- **Automatic Generation**: The system can automatically generate a `requestUuid` for each incoming request if not provided.
- **Manual Provision**: Clients can optionally provide their own `requestUuid` for fine-grained control over idempotency.

### Redis-Based Storage

Idempotency state is stored in Redis with a configurable TTL. This ensures:
- High performance for lookups
- Automatic cleanup of stale idempotency data
- Distributed consistency across multiple instances of the locking service

### Response Replay

For duplicate requests (identified by `requestUuid`):
1. If a cached response exists, it is immediately returned.
2. If the operation is in progress, the request waits for the original operation to complete.

### Operation Safety

The idempotency system guarantees that operations are performed exactly once by:
1. Checking for existing operations before execution
2. Atomically marking operations as in-progress
3. Storing operation results for future replay

## Integration Points

The idempotency mechanisms are integrated with various components of the Redis locking framework:

### Lock Operations

All lock acquire and release operations support idempotency:
- [`LockService.acquire()`](framework-modules/locking/modules/locking-redis-lock/docs/02_USAGE_AND_CONFIGURATION/configuration.md:150)
- [`LockService.release()`](framework-modules/locking/modules/locking-redis-lock/docs/02_USAGE_AND_CONFIGURATION/configuration.md:180)

### Watchdog Operations

Watchdog heartbeat operations are idempotent to prevent duplicate lock extensions:
- [`WatchdogService.extend()`](framework-modules/locking/modules/locking-redis-lock/docs/03_INTERNAL_MECHANISMS/watchdog_mechanism.md:100)

### Lua Script Integration

Lua scripts used for atomic operations validate and store idempotency state:
- [Acquire Lock Script](framework-modules/locking/modules/locking-redis-lock/docs/04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md:50)
- [Release Lock Script](framework-modules/locking/modules/locking-redis-lock/docs/04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md:150)

### Virtual Thread Compatibility

Idempotency processing is designed to work seamlessly with Virtual Threads:
- [Virtual Threads Usage](framework-modules/locking/modules/locking-redis-lock/docs/03_INTERNAL_MECHANISMS/virtual_threads_usage.md:75)

## Implementation Details

### Key Structure

Redis keys for idempotency tracking follow this pattern:
```
idempotency:{requestUuid}
```

The value stored contains:
- Operation status (pending, completed, failed)
- Result payload (for completed operations)
- Timestamp of the initial request

### State Management

Request states are tracked as follows:
1. **Pending**: Set when an operation begins processing
2. **Completed**: Set when an operation successfully finishes
3. **Failed**: Set when an operation encounters an error

### Performance Impact

The idempotency system is designed for minimal overhead:
- Efficient Redis operations (O(1) complexity for most operations)
- Optimized caching of responses
- Asynchronous cleanup of expired idempotency data

## Configuration

### Property Settings

The following properties can be configured:

- `destilink.fw.locking.redis.idempotency.enabled`: Enable/disable idempotency (default: true)
- `destilink.fw.locking.redis.idempotency.responseCacheTtl`: TTL for cached responses (default: 24 hours)

### Optional vs Required

- Idempotency is optional by default but can be made mandatory for specific operations or globally.
- When mandatory, requests without a valid `requestUuid` will be rejected.

### Cleanup Mechanisms

- Automatic cleanup of expired idempotency data is handled by Redis TTL mechanisms.
- Additional background jobs can be configured for proactive cleanup of stale data.

## Performance Characteristics

The idempotency system is designed for high performance and scalability:

- **Low Latency**: Typical overhead is &lt;1ms per operation
- **High Throughput**: Capable of handling thousands of requests per second
- **Scalability**: Performance scales linearly with Redis cluster size

## Error Handling and Edge Cases

### Conflict Resolution

In case of concurrent requests with the same `requestUuid`:
1. The first request is processed normally
2. Subsequent requests wait for the result of the first request
3. If the first request fails, the next request is allowed to proceed

### Network Partitions

In case of network partitions:
1. Requests may timeout while waiting for idempotency checks
2. The system uses a "fail-open" approach, allowing operations to proceed if idempotency cannot be guaranteed
3. Reconciliation processes handle potential duplicates after network recovery

### Data Consistency

To maintain data consistency:
1. Idempotency state updates are performed atomically with the main operation
2. In case of partial failures, the system always errs on the side of safety (e.g., treating uncertain states as "locked")

### Monitoring and Alerting

The system provides metrics and logs for monitoring idempotency-related issues:
- Rate of duplicate requests
- Idempotency cache hit/miss ratios
- Errors in idempotency state management

Alerts can be configured for anomalies in these metrics to quickly identify potential issues.