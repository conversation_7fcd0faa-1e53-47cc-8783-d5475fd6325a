# Redis Locking Module: Notes for Users (v1.0 - Post Detailed Plan Refinements)

## 1. Introduction

This document highlights key behavioral aspects and design choices of the `locking-redis-lock` module following the **Comprehensive Plan (Version 1.0, June 17, 2025)**. These notes clarify its functionality, especially regarding changes to configuration, watchdog behavior, idempotency, key schema, and Virtual Thread adoption.

For detailed information, refer to the respective core documentation, which has been updated for consistency with this plan.

## 2. Key Behavioral and Design Points to Note

*   **Asynchronous-First with Virtual Threads**: All core lock operations are asynchronous and execute on Virtual Threads, minimizing platform thread blocking. Synchronous `Lock` methods are blocking wrappers. MDC is propagated.
*   **Efficient Lock Acquisition (Non-Polling by Default)**: Primary wait mechanism is Redis Pub/Sub. `LockSemaphoreHolder` (managing `CompletableFuture`s) is registered with `UnlockMessageListenerManager` *before* the first Redis acquisition attempt to prevent race conditions.
*   **`lock()` Method Blocks Indefinitely**: Standard `lock()` blocks until acquired or interrupted. For time-bounded attempts, use `tryLock(long timeout, TimeUnit unit)`. `defaults.acquireTimeout` governs this, with nuances for in-flight Redis operations (they are not interrupted; their result takes precedence).
*   **Centralized Idempotency**: All mutating Redis operations are idempotent. `RedisLockOperationsImpl` generates a `requestUuid` per logical operation (used for all its internal retries). Lua scripts use this `requestUuid` and `responseCacheTtl` to check/store results in a response cache. This is mandatory for all mutating scripts.
*   **Lua-Only Redis Operations**: All lock state, TTL (`PEXPIREAT`), and metadata (`lockDataKey`) changes are exclusively via Lua scripts.
*   **Refined Watchdog Mechanism**:
    *   The `LockWatchdog` service is **always active** if the module is enabled.
    *   It conditionally monitors locks if they are application-instance-bound (`LockOwnerSupplier.canUseWatchdog()`) AND their `userProvidedLeaseTime > safetyBufferMillis` (where `safetyBufferMillis = watchdog.interval * watchdog.factor`).
    *   Initial TTL for monitored locks is `safetyBufferMillis`. For non-monitored, it's the full `userProvidedLeaseTime`.
    *   Watchdog uses `PEXPIREAT` and aims to maintain `safetyBufferMillis` TTL, or expires the lock at `userIntendedExpireTimeMillis` during the "final leg."
    *   Crucially, the watchdog **never** modifies the `originalLeaseTimeMillis` (user's intended full lease) stored in the `lockDataKey` in Redis. It only reads it to determine renewal strategy.
*   **Lock-Type Specific Redis Keys**: All `lockKey`s and `lockDataKey`s now include a mandatory lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`.
*   **`lockDataKey`**: A secondary Redis Hash key (e.g., `...:<lockType>:{<lockName>}:data`) stores `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`.
*   **Standardized Lua Script Returns**: Scripts return a structured array: `{status_code, expiresAtMillis, originalLeaseTimeMillis}`.
*   **Programmatic Bucket Configuration**: Bucket configuration is programmatic via builders. Global defaults from `RedisLockProperties.Defaults`.
*   **Configuration Property Changes**:
    *   **Removed**: `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `maxLockNameLength`, `maxBucketNameLength`, `maxScopeLength`. From `RedisLockProperties.Defaults`: `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout`. From `RedisLockProperties.WatchdogProperties`: `operationMaxRetries`, `operationTimeout`. `unlockMessageListenerEnabled` is also removed (listener is mandatory).
    *   **Added/Refined**: `RedisLockProperties.responseCacheTtl`. `WatchdogProperties` now has `interval` and `factor`. `Defaults` has `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`.
*   **`maxRetries` Scope**: `defaults.maxRetries` applies to retrying *individual failing Redis operations* within `RedisLockOperationsImpl`, not to the overall loop for acquiring a busy lock.

## 3. Notes for Users Familiar with Earlier Drafts

*   **No `pubSubWaitTimeout`**: Use `defaults.acquireTimeout` for timed `tryLock` attempts.
*   **Watchdog Always Active (Service)**: Monitoring per lock is conditional. No global `watchdog.enabled` toggle; use module's main `enabled` flag.
*   **Idempotency is Core**: `requestUuid` and `responseCacheTtl` are central.
*   **Keys Include Lock Type**: Be aware of the new key structure if inspecting Redis directly.

## 4. Compatibility & Future Migrations

*   **Source Compatibility**: Core `java.util.concurrent.Lock` methods are maintained.
*   **Behavioral Differences**: Non-polling, Virtual Thread execution, explicit `acquireTimeout` requirements, new watchdog logic, and idempotency are key.
*   **Redis Data**: New locks will use the new key format with lock-type segments and `lockDataKey`. Existing locks (if any from pre-release versions) will not be automatically migrated but will also not conflict due to the new key structure.