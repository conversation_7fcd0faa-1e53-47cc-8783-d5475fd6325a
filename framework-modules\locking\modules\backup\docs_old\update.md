An extensive refactoring has been performed on the entire documentation set to align it with the final, consolidated design as specified in `detailed-plan.md`. All 17 markdown files have been updated to be semantically consistent with the new architecture.

Key changes integrated across all documents include:
*   **Centralized Idempotency**: `RedisLockOperationsImpl` is now the single source for `requestUuid` generation, and all mutating Lua scripts implement the idempotency wrapper.
*   **Overhauled Watchdog Mechanism**: The watchdog is always active, uses a `safetyBuffer` (`interval * factor`) for eligibility, and leverages `PEXPIREAT` to manage lock lifetimes, carefully distinguishing between `originalLeaseTimeMillis` and the current `expiresAtMillis`.
*   **Async-First with Virtual Threads**: All lock operations are dispatched to a Virtual Thread executor for improved scalability.
*   **Standardized Key Schema**: All Redis keys now include a mandatory `<lockType>` segment for semantic isolation (e.g., `...:__locks__:reentrant:{lockName}`).
*   **Lua-Only Redis Operations**: A strict principle that all lock state/TTL modifications must occur within Lua scripts.
*   **Refined Configuration**: All property changes from the plan have been applied, including the removal of obsolete properties (`pubSubWaitTimeout`, `max*Length`, etc.) and the addition/refinement of others (`watchdog.factor`, `responseCacheTtl`).
*   **Refined Lock Acquisition Flow**: The race condition is prevented by registering the `LockSemaphoreHolder` *before* the first Redis acquisition attempt.
*   **Standardized Lua Script Semantics**: Scripts now use a consistent return value convention and a structured response format.

The following is the complete, updated content for `all.md`.

--- START OF FILE all.md ---

Okay, this is a significant refactoring task. I will generate the content for each new markdown file based on the proposed structure, ensuring semantic consistency with the original documents.

Due to the length and complexity, I will generate the files in batches.

**Let's start with the `docs/index.md` and the `01_OVERVIEW_AND_CONCEPTS/` directory.**

---
**`docs/index.md`**
---
```markdown
# Destilink Framework: Redis Locking Module - Final Architecture & Plan

This document outlines the final consolidated architecture and plan for the `locking-redis-lock` module, derived from the analysis and synthesis of previous planning phases and updated to reflect the **Async-First design**, **mandatory `redis-core` integration**, and **strict adherence to Destilink Framework guidelines**.

## Core Documentation

### 1. Overview and Concepts
*   [Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md) - High-level components, interactions, design principles (Async-First).
*   [Class Hierarchy](01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md) - Interfaces, abstract classes, inheritance (API structure).
*   [Lock Types Deep Dive](01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md) - Detailed behavior of each concrete lock (Reentrant, State, ReadWrite, Stamped).
*   [Glossary](01_OVERVIEW_AND_CONCEPTS/glossary.md) - Definitions of all key terms.

### 2. Usage and Configuration
*   [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md) - How to configure the module (YAML, programmatic builders).
*   [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md) - Expected exceptions, how to handle them.

### 3. Core Mechanisms (Internal)
*   [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md) - Detailed step-by-step of acquiring a lock.
*   [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md) - Detailed step-by-step of Pub/Sub notification.
*   [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) - Detailed step-by-step of lease renewal.
*   [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md) - Detailed explanation of `requestUuid` and response caching.

### 4. Implementation Reference (Internal)
*   [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md) - Detailed description of shared Spring Beans and critical internal classes.
*   [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md) - How Redis keys are structured.
*   [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md) - Detailed documentation for each Lua script.

### 5. Operational Aspects
*   [Performance Considerations](05_OPERATIONAL_ASPECTS/performance.md) - Performance characteristics and tuning.
*   [Metrics](05_OPERATIONAL_ASPECTS/metrics.md) - Exposed metrics and their meaning.
*   [Testing Strategy](05_OPERATIONAL_ASPECTS/testing_strategy.md) - How the module is tested.

### 6. History and Migration
*   [Modernization Plan & Assessment](06_HISTORY_AND_MIGRATION/modernization_plan.md) - Past decisions, evolution of the module.
*   [Migration Notes](06_HISTORY_AND_MIGRATION/migration_notes.md) - Notes for users upgrading or adopting this version.

```

---
**`docs/01_OVERVIEW_AND_CONCEPTS/architecture.md`**
---
```markdown
# Redis Locking Module: Architecture Overview

## 1. Introduction

This document outlines the consolidated architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, performant, and developer-friendly distributed locking mechanism using Redis. The architecture emphasizes shared, Spring-managed components, efficient non-polling lock acquisition, clear configuration hierarchies, atomicity through Lua scripts, and strict adherence to **Destilink Framework guidelines (`/.amazonq/rules/guidelines.md`)**.

This architecture adopts an **Async-First approach**, where all core lock operations are fundamentally asynchronous using `CompletableFuture`s and executed on a Virtual Thread executor. Synchronous `java.util.concurrent.Lock` implementations act as wrappers. It mandates the use of the internal `redis-core` module, particularly `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis interactions, aligning with its asynchronous nature. This architecture synthesizes the best aspects of previous planning phases and the new, explicit requirements.

For detailed descriptions of individual components mentioned here (e.g., `RedisLockOperations`, `LockComponentRegistry`), please refer to the [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md). For specifics on lock types like `RedisReentrantLock`, see the [Lock Types Deep Dive](lock_types_deep_dive.md).

## 2. Core Architectural Principles

*   **Strict Guideline Adherence**: The implementation strictly follows all rules and patterns defined in `/.amazonq/rules/guidelines.md`, including but not limited to, component scanning prohibition, explicit bean definitions, and dependency injection best practices.
*   **Async-First Design with Virtual Threads**: All lock operations are primarily implemented asynchronously using `CompletableFuture`s, with the logic executed on a dedicated Virtual Thread executor for high scalability. The standard `java.util.concurrent.locks.Lock` interface methods (`lock()`, `tryLock()`, `unlock()`) are implemented as synchronous wrappers around these asynchronous operations. (See [Class Hierarchy](class_hierarchy.md) for `AsyncLock` interface).
*   **Mandatory `redis-core` Usage**: All interactions with Redis MUST use the `com.tui.destilink.framework.redis.core` module, specifically `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all command executions, leveraging its asynchronous capabilities. `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` will be used for shared Redis configuration.
*   **Atomicity via Lua-Only Operations**: Critical lock operations (acquire, release, extend, state changes) are implemented using Redis Lua scripts to ensure atomicity and prevent race conditions. All Redis operations that modify lock state, TTL, or metadata are performed exclusively via Lua scripts. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **Centralized Idempotency**: All mutating operations are idempotent through a centralized mechanism using unique request UUIDs (`requestUuid`) and a Redis-based response cache. The `requestUuid` is generated per logical operation by `RedisLockOperationsImpl` and the idempotency check is performed atomically within each Lua script. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Spring-Managed Shared Components**: Critical, stateless, or resource-intensive components (`ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`, `LockComponentRegistry`) are implemented as Spring-managed singleton beans, configured explicitly within `RedisLockAutoConfiguration`. `RedisLockOperations` will delegate all Redis command execution to `ClusterCommandExecutor` from `redis-core`. (Details in [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md)).
*   **Centralized Component Access**: A `LockComponentRegistry` bean acts as a central holder for these shared services, simplifying dependency injection into lock implementations.
*   **Efficient Non-Polling Lock Acquisition**: Lock acquisition primarily relies on Redis Pub/Sub for unlock notifications, managed via `UnlockMessageListener` and `LockSemaphoreHolder`. A fallback re-polling mechanism handles potential missed notifications. (See [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md) and [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)).
*   **Contextual and Structured Exception Handling**: A defined exception hierarchy, rooted in `AbstractRedisLockException` (which implements `ExceptionMarkerProvider`), provides detailed context for improved diagnostics and structured logging. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
*   **Explicit Configuration & Dependency Injection**: Strict adherence to Destilink Framework guidelines for auto-configuration. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Standardized Key Construction via `redis-core`**: All Redis keys MUST be constructed using utilities from the `redis-core` module and include a mandatory `<lockType>` segment for semantic isolation. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
*   **Clear Configuration Hierarchy**: Global YAML settings provide base defaults. Programmatic builders allow overriding specific properties. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Lightweight Lock Instances & Resource Sharing**: Concrete lock implementations are designed to be lightweight, short-lived objects, sharing resources through Spring-managed singleton beans.
*   **Distributed Reentrancy**: Managed using Redis Hashes for correctness across distributed instances. (See [Lock Types Deep Dive](lock_types_deep_dive.md)).
*   **Enhanced Logging & Observability**: Comprehensive logging using SLF4J, MDC, and structured exceptions. Metrics via Micrometer. (See [Metrics](05_OPERATIONAL_ASPECTS/metrics.md)).

## 3. High-Level Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["<code>LockBucketRegistry</code> (Bean)"];
        LBB["<code>LockBucketBuilder</code>"];
        LCB["<code>LockConfigBuilder</code>"];
        ALTCB["<code>AbstractLockTypeConfigBuilder</code> & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic (Async-First)"]
        direction LR;
        ARL["<code>AbstractRedisLock</code> (Base Class)"];
        RLI["Concrete Lock Implementations<br>(e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`)<br>ALL Implement `AsyncLock` and wrap `java.util.concurrent.locks.Lock`."];
        LSH["<code>LockSemaphoreHolder</code> (Per Lock Key, Guava Cache Managed)"];
    end
    
    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        LCR["<code>LockComponentRegistry</code> (Bean)"];
        SL["<code>ScriptLoader</code> (Bean)"];
        UMLM["<code>UnlockMessageListenerManager</code> (Bean)"];
        UML["<code>UnlockMessageListener</code> (Per Bucket, Managed by UMLM)"];
        LW["<code>LockWatchdog</code> (Bean)"];
        ROps["<code>RedisLockOperations</code> (Bean)"];
        LMP["<code>LockMonitor</code> (Bean, Optional, Micrometer Metrics)"];
        LOS["<code>DefaultLockOwnerSupplier</code> (Bean)"];
        LERRH["<code>RedisLockErrorHandler</code> (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["<code>RedisLockProperties</code> (Global YAML-backed)"];
        LBC["<code>LockBucketConfig</code> (Programmatically Resolved for Bucket)"];
        RAutoConfig["<code>RedisLockAutoConfiguration</code> (@AutoConfiguration)"];
        RCProps["<code>RedisCoreProperties</code> (from redis-core)"];
    end

    subgraph Exceptions ["Exception Handling"]
        BaseExc["<code>AbstractRedisLockException</code> (implements ExceptionMarkerProvider)"];
        SpecExc["Specialized Lock Exceptions..."];
    end

    subgraph Logging ["Logging Infrastructure"]
        SLF4J["SLF4J API"];
        MDC["MDC (via LockContextDecorator)"];
        EMP["ExceptionMarkerProvider (via Exceptions)"];
        CoreLogging["Destilink Core Logging (ExceptionLogstashMarkersJsonProvider)"];
    end

    subgraph ExternalSystems ["External Systems"]
        Redis["Redis (Cluster)"];
        CCExec["<code>ClusterCommandExecutor</code> (from redis-core)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;
    
    ARL -- "uses services from" --> LCR;
    ARL -- "uses for waiting" --> LSH;
    ARL -- "throws" --> Exceptions;
    ARL -- "logs via" --> SLF4J;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides (optional)" --> LMP;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;

    UMLM -- "manages & provides" --> UML;
    UML -- "manages `LockSemaphoreHolder` map via Guava Cache" --> LSH;
    UML -- "listens to Pub/Sub from" --> Redis;
    UML -- "signals" --> LSH;
    
    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> SL;
    RAutoConfig -- "defines bean" --> UMLM;
    RAutoConfig -- "defines bean" --> LW;
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "defines bean (conditional)" --> LMP;
    RAutoConfig -- "defines bean" --> LOS;
    RAutoConfig -- "defines bean" --> LERRH;
    RAutoConfig -- "enables" --> RLP;
    RAutoConfig -- "uses" --> RCProps;

    RLP -- "provides global defaults for" --> LBC;
    LBR -- "initializes & resolves config into" --> LBC;
    LBC -- "provides config to" --> LBB;

    ROps -- "uses" --> SL;
    ROps -- "interacts with" --> CCExec;
    ROps -- "uses" --> LERRH;
    LW -- "extends lease in" --> CCExec;
    
    Exceptions -- "are subclasses of" --> BaseExc;
    BaseExc -- "provides marker to" --> EMP;
    EMP -- "consumed by" --> CoreLogging;
    SLF4J -- "writes to" --> MDC;
    SLF4J -- "integrates with" --> CoreLogging;
    CCExec -- "communicates with" --> Redis;


    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style LBB fill:#lightblue;
    style LCB fill:#lightblue;
    style ALTCB fill:#lightblue;
    style ARL fill:#adebad;
    style RLI fill:#adebad;
    style LSH fill:#adebad;
    style LCR fill:#ccffcc;
    style SL fill:#ffffcc;
    style UMLM fill:#ffffcc;
    style UML fill:#ffffcc;
    style LW fill:#ffffcc;
    style ROps fill:#ffffcc;
    style LMP fill:#ffffcc;
    style LOS fill:#ffffcc;
    style LERRH fill:#ffffcc;
    style RLP fill:#ffcc99;
    style LBC fill:#fdd;
    style RAutoConfig fill:#ffcc99;
    style RCProps fill:#ffcc99;
    style Exceptions fill:#ffdddd;
    style Logging fill:#e6e6fa;
    style Redis fill:#ffcccc;
    style CCExec fill:#ffcccc;
```
*Note: Detailed descriptions of each component in the diagram are available in the [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md).*

## 4. Key Component Categories

The module consists of several categories of components:

*   **Configuration Components**: `RedisLockProperties`, `LockBucketConfig`, `RedisLockAutoConfiguration`, and leveraging `RedisCoreProperties` from `redis-core`. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Shared Spring-Managed Services**: `LockComponentRegistry`, `ScriptLoader`, `UnlockMessageListenerManager`, `UnlockMessageListener`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md)).
*   **Core Locking Logic & Instantiation**: `LockBucketRegistry`, `LockBucketBuilder`, `LockConfigBuilder`, `AbstractLockTypeConfigBuilder` (and subclasses), `AbstractRedisLock`, Concrete Lock Implementations (e.g., `RedisReentrantLock`), `LockSemaphoreHolder`. (See [Class Hierarchy](class_hierarchy.md), [Lock Types Deep Dive](lock_types_deep_dive.md), and [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md)).
*   **Exception Handling**: `AbstractRedisLockException` and its specialized subclasses. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).

## 5. Data Flow and Interactions (High-Level)

Key operational flows include:

*   **Lock Acquisition**: Primarily asynchronous, executed on a Virtual Thread, with synchronous calls wrapping `CompletableFuture`s. (Detailed in [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md)).
*   **Lock Release & Unlock Notification**: Utilizes Redis Pub/Sub. All Redis commands for release are executed via `ClusterCommandExecutor`. (Detailed in [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)).
*   **Lease Extension (Watchdog)**: Watchdog operations use `ClusterCommandExecutor`. (Detailed in [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)).
*   **Asynchronous Lock Operations**: For locks implementing `AsyncLock`, acquisition and release operations are invoked asynchronously, returning `CompletableFuture`s. This is the primary mode of operation.

## 6. Adherence to Destilink Framework Guidelines

This architecture is designed to comply with **all** Destilink Framework guidelines specified in `/.amazonq/rules/guidelines.md`, focusing on:

*   **No `@ComponentScan`** in `RedisLockAutoConfiguration`.
*   **Explicit Dependencies** via constructor injection.
*   **Standard Configuration Patterns** (`@ConfigurationProperties`, `@AutoConfiguration`).
*   **Strict Dependency Management** as outlined in the guidelines.

## 7. Logging and Observability (High-Level)

*   **SLF4J API** for all logging.
*   **MDC (Mapped Diagnostic Context)** via `LockContextDecorator` for contextual information.
*   **Structured Exception Logging** via `ExceptionMarkerProvider` and `ExceptionLogstashMarkersJsonProvider`.
*   **Metrics Exposure** via Micrometer, facilitated by the `LockMonitor` bean.
(Further details in [Metrics](05_OPERATIONAL_ASPECTS/metrics.md) and [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
```

---
**`docs/01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md`**
---
```markdown
# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the comprehensive class hierarchy for the `locking-redis-lock` module within the Destilink Framework. The hierarchy is designed to support all documented lock types, adhering to the **Async-First approach** detailed in the [Architecture Overview](architecture.md) and aligning with standard Java patterns and Destilink Framework guidelines.

Understanding this hierarchy is crucial for developers working with or extending the locking module. It clarifies the roles, responsibilities, and relationships between different lock-related interfaces and classes. For a general overview of all documentation, please refer to the [main documentation index](../index.md).

For detailed behavioral descriptions of concrete lock implementations (e.g., `RedisReentrantLock`, `RedisStateLock`), refer to the [Lock Types Deep Dive](lock_types_deep_dive.md). For detailed responsibilities of `AbstractRedisLock`, see the [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#abstractredislock).

## 2. Core Design Principles

The class hierarchy is built upon the following core principles:

*   **Async-First with Virtual Threads**: Core operations are asynchronous, returning `CompletableFuture`s, and are executed on a dedicated Virtual Thread executor, as emphasized in the [Architecture Overview](architecture.md) and detailed in the [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md). Synchronous `java.util.concurrent.locks.Lock` methods serve as blocking wrappers.
*   **Standard Java Compliance**: The hierarchy integrates seamlessly with standard Java concurrency interfaces (`java.util.concurrent.locks.Lock`, `java.util.concurrent.locks.ReadWriteLock`).
*   **Clear Abstraction**: An abstract base class (`AbstractRedisLock`) encapsulates common Redis interaction logic, promoting code reuse and maintainability. (Details in [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#abstractredislock)).
*   **Modularity**: Specific lock types are concrete implementations, allowing for clear separation of concerns. (Details in [Lock Types Deep Dive](lock_types_deep_dive.md)).
*   **Configuration Driven**: Lock behavior is influenced by the [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md) provided to lock instances.
*   **Robust Error Handling**: All custom exceptions thrown by these classes extend `AbstractRedisLockException` as defined in the [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md).

For definitions of terms used here, please consult the [Glossary](glossary.md).

## 3. Class Hierarchy Diagram

```mermaid
classDiagram
    direction TB

    class Lock {
        <<interface>>
        +lock()
        +tryLock()
        +unlock()
        +newCondition()
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncLock {
        <<interface>>
        +lockAsync()
        +tryLockAsync()
        +tryLockAsync(long, TimeUnit)
        +unlockAsync()
    }
    AsyncLock --|> Lock

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    AsyncReadWriteLock --|> ReadWriteLock

    class AbstractRedisLock {
        <<Abstract>>
        #String lockName
        #String ownerId
        #RedisLockOperations redisLockOperations
        #RedisLockProperties redisLockProperties
        #LockOwnerSupplier lockOwnerSupplier
        #LockSemaphoreHolder lockSemaphoreHolder
        +lockAsync()
        +tryLockAsync()
        +unlockAsync()
        +lock()
        +tryLock()
        +unlock()
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        +RedisReentrantLock(String, RedisLockOperations, ...)
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        +RedisStateLock(String, RedisLockOperations, ...)
    }
    RedisStateLock --|> AbstractRedisLock

    class RedisStampedLock {
        +RedisStampedLock(String, RedisLockOperations, ...)
        // +tryOptimisticRead()
        // +validate(long)
        // +tryConvertToWriteLock(long)
    }
    RedisStampedLock --|> AbstractRedisLock

    class RedisReadWriteLock {
        -AsyncLock readLockInstance
        -AsyncLock writeLockInstance
        +RedisReadWriteLock(String, RedisLockOperations, ...)
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    RedisReadWriteLock ..|> AsyncReadWriteLock

    class RedisReadLock {
        +RedisReadLock(String, RedisLockOperations, ...)
    }
    RedisReadLock --|> AbstractRedisLock

    class RedisWriteLock {
        +RedisWriteLock(String, RedisLockOperations, ...)
    }
    RedisWriteLock --|> AbstractRedisLock

    RedisReadWriteLock *-- RedisReadLock : creates
    RedisReadWriteLock *-- RedisWriteLock : creates
```

## 4. Interface and Class Descriptions

### 4.1. Core Java SDK Interfaces

* `java.util.concurrent.locks.Lock`: The standard synchronous lock interface from the JDK. All locks in this module are compatible with this interface.
* `java.util.concurrent.locks.ReadWriteLock`: The standard synchronous read-write lock interface from the JDK.

### 4.2. Custom Asynchronous Lock Interfaces

* **`com.tui.destilink.framework.locking.redis.lock.AsyncLock`**
  * **Extends:** `java.util.concurrent.locks.Lock`
  * **Purpose:** Defines the contract for asynchronous lock operations, crucial for the module's [Async-First design principle](architecture.md). Methods return `CompletableFuture`s to avoid blocking application threads.
  * **Key Methods:** `lockAsync()`, `tryLockAsync()`, `unlockAsync()`.
  * **Note:** Implementations of this interface (typically via `AbstractRedisLock`) provide the synchronous `java.util.concurrent.locks.Lock` methods as blocking wrappers around these core asynchronous operations. This allows an `AsyncLock` instance to be used wherever a standard `Lock` is expected, while still promoting asynchronous usage as the primary pattern.

* **`com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`**
  * **Extends:** `java.util.concurrent.locks.ReadWriteLock`
  * **Purpose:** Defines the contract for an asynchronous read-write lock, ensuring that its `readLock()` and `writeLock()` methods return `AsyncLock` instances.
  * **Key Methods:** `readLock()`, `writeLock()`.

### 4.3. Abstract Base Class

* **`com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
  * **Purpose:** Provides a foundational implementation for Redis-based distributed locks, encapsulating common logic.
  * **Note:** For a detailed description of `AbstractRedisLock`'s responsibilities and internal workings, refer to the [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#abstractredislock).

### 4.4. Concrete Redis Lock Implementations

This section lists the concrete lock classes. These implementations extend `AbstractRedisLock` (or compose locks that do) and therefore provide both the asynchronous API defined by `AsyncLock` and the standard synchronous `java.util.concurrent.locks.Lock` API through inherited blocking wrapper methods. All concrete lock implementations reside in the `com.tui.destilink.framework.locking.redis.lock.impl` package.

For detailed behavioral descriptions, purpose, and typical use cases of each concrete lock type, refer to the [Lock Types Deep Dive](lock_types_deep_dive.md).

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReentrantLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisStateLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisStampedLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReadWriteLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`
* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReadLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisWriteLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`

The "Application-Instance-Bound Lock" characteristic is a behavioral aspect based on configuration, not a distinct class type. See [Lock Types Deep Dive](lock_types_deep_dive.md#application-instance-bound-lock-characteristic) for details.
```

---
**`docs/01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md`**
---
```markdown
# Redis Locking Module: Lock Types Deep Dive

This document provides detailed descriptions of the concrete lock implementations available in the `locking-redis-lock` module. Each lock type serves specific purposes and exhibits distinct behaviors. All concrete lock implementations reside in the `com.tui.destilink.framework.locking.redis.lock.impl` package and extend `AbstractRedisLock` (or compose locks that do), thus providing both asynchronous APIs (via `AsyncLock`) and synchronous `java.util.concurrent.locks.Lock` compatible APIs.

For their position in the overall class structure, see the [Class Hierarchy](class_hierarchy.md).
For information on how locks are acquired, see [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md).
For details on the watchdog mechanism, see [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md).

## 1. `RedisReentrantLock`

*   **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
*   **Purpose:** The primary, general-purpose, reentrant asynchronous distributed lock implementation. This corresponds to what might be referred to as a standard exclusive lock. It allows the same thread (or more accurately, the same lock owner identifier) to acquire the lock multiple times.
*   **Behavior:**
    *   Uses Lua scripts (detailed in [Lua Scripts Documentation](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)) for standard reentrant lock acquisition and release.
    *   Reentrancy (owner and count) is managed in a Redis Hash (`lockDataKey`) associated with the lock key, ensuring distributed reentrancy. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
    *   Each `lock()` or `lockAsync()` call by the same owner increments a counter in Redis. Each `unlock()` or `unlockAsync()` call decrements it. The lock is only truly released when the counter reaches zero.
    *   Can be configured as an "Application-Instance-Bound Lock" (see Section 6 below).
*   **When to Use:** Default choice for most distributed exclusive locking scenarios where reentrancy is desired or acceptable.

## 2. `RedisStateLock`

*   **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
*   **Purpose:** A specialized lock that associates a user-defined state (as a String) with the lock itself. The state is stored in Redis alongside the lock information. This allows for more complex coordination scenarios where the lock's state is an integral part of the locking semantics.
*   **Behavior:**
    *   Uses specific Lua scripts to manage the lock and its associated state atomically. (See [Lua Scripts Documentation](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
    *   The associated state is stored in a separate Redis key, linked to the main lock key via a shared hash tag. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
    *   Provides methods to get and potentially update the state while the lock is held (e.g., `getStateAsync()`, `updateStateAsync()`).
    *   Lock acquisition can be conditional on the current state value.
    *   Can be configured as an "Application-Instance-Bound Lock" (see Section 6 below).
*   **When to Use:** Scenarios requiring distributed locking coupled with a simple, atomically managed state string. For example, managing a distributed state machine transition or ensuring an operation proceeds only if a shared resource is in a specific state.

## 3. `RedisStampedLock`

*   **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
*   **Purpose:** Provides a distributed lock with modes for reading, writing, and optimistic reading, inspired by `java.util.concurrent.locks.StampedLock`. It's designed for scenarios where read operations are frequent and write operations are rare, allowing for higher concurrency with optimistic reads. Note that stamps are `String`-based in this distributed version.
*   **Behavior:**
    *   Implements read, write, and optimistic read lock acquisition and release using specific Lua scripts. All Redis operations are performed via Lua scripts to ensure atomicity. (See [Lua Scripts Documentation](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
    *   Provides methods like `tryOptimisticRead()`, `validate(String stamp)`, `tryConvertToWriteLockAsync(String stamp)`, `tryConvertToReadLockAsync(String stamp)`, etc. (Note: method signatures might be adapted for `AsyncLock` and `String` stamps).
    *   The write lock part can be configured as an "Application-Instance-Bound Lock" (see Section 6 below). Read locks acquired via `RedisStampedLock` are also eligible for watchdog monitoring if their lease time is sufficient.
    *   Optimistic reads do not block writers but require validation using a stamp.
    *   Read and Write locks support reentrancy for the calling thread, managed via `ThreadLocal` counters and specific logic in Lua scripts.
    *   Uses a Redis Hash to store version information, write owner, reentrancy counts, and read holder information. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
*   **When to Use:** Complex scenarios with a high ratio of reads to writes where optimistic reads can significantly improve concurrency. Requires careful usage due to its more complex API compared to other lock types.

## 4. `RedisReadWriteLock`

*   **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`
*   **Purpose:** Provides a distributed read-write lock, allowing multiple readers or a single writer. This is suitable for resources that are read frequently but modified infrequently.
*   **Composition:** Internally creates and manages instances of `com.tui.destilink.framework.locking.redis.lock.impl.RedisReadLock` and `com.tui.destilink.framework.locking.redis.lock.impl.RedisWriteLock`.
*   **Behavior:**
    *   The `readLock()` method returns an `AsyncLock` (`RedisReadLock` instance) that allows multiple concurrent acquisitions.
    *   The `writeLock()` method returns an `AsyncLock` (`RedisWriteLock` instance) that ensures exclusive access.
    *   Both read and write locks support reentrancy for the same thread/owner.
    *   Uses specific Lua scripts for read and write operations that coordinate access through a main Redis Hash. (See [Lua Scripts Documentation](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
    *   The main lock hash stores mode ('read'/'write'), writer ID, writer reentrancy count, and individual reader IDs with their reentrancy counts.
    *   **Individual Read Lock Timeout Keys**: Each reentrant acquisition of a read lock creates a separate Redis String key with its own TTL (based on the `leaseTime` of that acquisition). These keys help cooperatively manage the TTL of the main lock hash. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
    *   The write lock component can be configured as an "Application-Instance-Bound Lock" (see Section 6 below). Acquired read locks are also eligible for watchdog monitoring if their lease time is sufficient, with the watchdog acting on the main `RedisReadWriteLock` instance's key.
*   **When to Use:** Protecting resources where read operations are common and can occur concurrently, while write operations are less frequent and require exclusive access.

### 4.1. `RedisReadLock` (Component of `RedisReadWriteLock`)

*   **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
*   **Purpose:** Represents the read lock component of a `RedisReadWriteLock`.
*   **Behavior:**
    *   Employs specific Lua scripts (`try_read_lock.lua`, `unlock_read_lock.lua`) that permit concurrent acquisition by multiple readers.
    *   Manages reentrancy for the same reader.
    *   Interacts with the main `RedisReadWriteLock` hash and individual read lock timeout keys.
    *   Eligible for watchdog monitoring if its lease time is sufficient (monitors the parent `RedisReadWriteLock`'s key).

### 4.2. `RedisWriteLock` (Component of `RedisReadWriteLock`)

*   **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
*   **Purpose:** Represents the write lock component of a `RedisReadWriteLock`.
*   **Behavior:**
    *   Utilizes specific Lua scripts (`try_write_lock.lua`, `unlock_write_lock.lua`) to ensure exclusive access for a single writer.
    *   Manages reentrancy for the same writer.
    *   Interacts with the main `RedisReadWriteLock` hash.
    *   Can be configured as an "Application-Instance-Bound Lock" (see Section 6 below).

## 5. "Application-Instance-Bound Lock" Characteristic

The "Application-Instance-Bound Lock" (defined in the [Glossary](glossary.md)) is not a distinct class type in this hierarchy. Instead, it is a behavioral characteristic that `RedisReentrantLock`, `RedisStateLock`, `RedisWriteLock` (the write component of `RedisReadWriteLock`), and the write mode of `RedisStampedLock` can exhibit based on their configuration. Read locks from `RedisReadWriteLock` and `RedisStampedLock` can also be monitored by the watchdog if their lease time is sufficient.

*   **Activation:** This behavior is activated when a lock instance is configured with:
    1.  A `leaseTime` that is greater than the calculated `safetyBufferMillis` (derived from watchdog configuration). See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md).
    2.  A `LockOwnerSupplier` that provides a stable and unique identifier for the current application instance or process, and permits watchdog usage for the lock via `canUseWatchdog()`.
*   **Mechanism:**
    *   `AbstractRedisLock` checks these conditions.
    *   If met, upon successful lock acquisition, it registers the lock with the `LockWatchdog`.
    *   The `LockWatchdog` (detailed in [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)) then periodically renews the lock's TTL in Redis as long as the lock is considered held by the registered owner.

This approach ensures that long-running operations holding such locks do not suffer from premature expiration, enhancing the robustness of the distributed system.
```

---
**`docs/01_OVERVIEW_AND_CONCEPTS/glossary.md`**
---
```markdown
# Redis Locking Module: Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation, consolidated from previous planning phases and updated to reflect the Async-First design and explicit `redis-core` integration.

*   **AbstractRedisLock**: Base Java class providing common functionality for all Redis lock implementations in the module. It orchestrates the asynchronous `CompletableFuture`-based operations, dispatching them to a Virtual Thread executor. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#abstractredislock)).
*   **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod ID). These locks are typically eligible for watchdog lease renewal if their lease time exceeds the `safetyBufferMillis`. (See [Lock Types Deep Dive](lock_types_deep_dive.md#application-instance-bound-lock-characteristic)).
*   **Async-First**: A core design principle of this module where all fundamental lock operations are asynchronous, returning `CompletableFuture`s, and executed on Virtual Threads. Synchronous `java.util.concurrent.locks.Lock` methods are implemented as wrappers around these asynchronous operations. (See [Architecture Overview](architecture.md)).
*   **AsyncLock**: An interface that extends `java.util.concurrent.locks.Lock`. It provides non-blocking, `CompletableFuture`-based asynchronous versions of standard lock operations, suitable for reactive programming models and high-concurrency scenarios to avoid thread blocking. All concrete lock implementations in this module must implement this interface. (See [Class Hierarchy](class_hierarchy.md)).
*   **Atomic Operation**: An operation that is guaranteed to execute fully without interruption or interference from other operations, ensured by executing commands as a single Lua script on the Redis server. All atomic operations are executed via `redis-core`'s `ClusterCommandExecutor`.
*   **Bucket (Lock Bucket)**: A logical grouping or namespace for locks. Buckets allow for applying common default configurations (e.g., `leaseTime`, `retryInterval`) to a set of related locks, primarily configured programmatically via builders. The `LockOwnerSupplier` configured at the bucket level is a key factor in determining watchdog eligibility for locks within that bucket. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Builder API**: A fluent API, starting with `LockBucketRegistry.builder(...)`, used to configure and create specific lock instances (e.g., `RedisReentrantLock`, `RedisStateLock`). (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md) for builder classes).
*   **ClusterCommandExecutor**: A critical component from `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` used for all Redis command executions within this module. It provides asynchronous, cluster-aware command execution capabilities, ensuring compliance with framework guidelines for Redis interaction. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockoperations)).
*   **Distributed Lock**: A synchronization primitive used to coordinate access to shared resources among multiple processes or services running in a distributed environment.
*   **`expiresAtMillis`**: The absolute Unix timestamp (in milliseconds) when the lock is currently set to expire, calculated by the Redis server. This is the value used with `PEXPIREAT` and is stored in the lock's metadata hash (`lockDataKey`).
*   **ExceptionMarkerProvider**: A Destilink Framework interface (`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`) implemented by custom exceptions to provide detailed contextual information for structured JSON logging, as per framework guidelines. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
*   **Hash Tag (Redis Cluster)**: A mechanism (`{...}`) used in Redis key names to ensure that multiple keys are allocated to the same hash slot, and thus the same node, in a Redis Cluster setup. Crucial for multi-key Lua scripts executed via `ClusterCommandExecutor`. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
*   **Idempotency Wrapper**: A pattern applied to all mutating Lua scripts. It involves checking a Redis response cache using a `requestUuid` before executing the core logic, and storing the operation's result in the cache with `responseCacheTtl` upon completion.
*   **Individual Read Lock Timeout Key**: A Redis String key with an associated TTL, created for each specific instance of an acquired read lock within a `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{myRWLock}:<readerId>:<count>`). It manages the lease for that individual read access, contributing to the overall lifetime management of the main `RedisReadWriteLock` key. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md) and [Lock Types Deep Dive](lock_types_deep_dive.md#redisreadwritelock)).
*   **`lockDataKey`**: A secondary Redis key (typically a Redis Hash) used to store additional metadata associated with the `lockKey`, such as `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`. Its key format follows the same lock-type-specific pattern as `lockKey`.
*   **`lockKey`**: The primary Redis key for the distributed lock itself (e.g., `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`). This key holds the lock's existence and has an associated expiry. In simple lock types, it may hold the `ownerId` as its value.
*   **Lock-Type Segment**: A mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`) that semantically isolates different lock types, preventing cross-type interference.
*   **`LockBucketConfig`**: A Java class (non-Spring managed) that holds the resolved, effective configuration settings for a specific lock bucket, after merging global defaults and programmatic builder overrides. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockbucketconfig)).
*   **`LockComponentRegistry`**: A Spring-managed bean that acts as a central holder for shared, stateless locking services (like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`), making them available to lock instances and other internal components. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockcomponentregistry)).
*   **`LockOwnerSupplier`**: An interface responsible for providing a unique identifier for the current lock owner (`ownerId`). A `DefaultLockOwnerSupplier` bean is provided. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#defaultlockownersupplier)).
*   **`LockSemaphoreHolder`**: A helper class, managed per lock key, used by `UnlockMessageListener` to manage waiting threads/futures for that specific lock key. It encapsulates a `java.util.concurrent.Semaphore` for synchronous waits, and is integrated with `CompletableFuture` for asynchronous waits. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#locksemaphoreholder)).
*   **`LockWatchdog`**: A Spring-managed bean that periodically extends the lease (TTL) of active, application-instance-bound locks in Redis to prevent premature expiration. Its operations are performed asynchronously via `ClusterCommandExecutor`. (See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) and [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockwatchdog)).
*   **Lua Scripts**: Scripts written in the Lua programming language that are executed atomically on the Redis server to perform complex lock operations. All Lua script executions are handled by `RedisLockOperations` which in turn uses `ClusterCommandExecutor`. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **MDC (Mapped Diagnostic Context)**: A logging mechanism used with SLF4J to enrich log messages with contextual data (e.g., `lock.name`, `lock.operation`). Managed via `LockContextDecorator`.
*   **Non-Polling Wait**: A mechanism where threads waiting for a lock do not continuously check its status. Instead, they wait on a synchronization primitive (like a `Semaphore` in `LockSemaphoreHolder` or a `CompletableFuture`) that is signaled when the lock is released (via Redis Pub/Sub). This is the primary waiting strategy. (See [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md)).
*   **`originalLeaseTimeMillis`**: The `relativeLeaseTimeMillis` value that was *last explicitly set by a user-initiated lock acquisition or extension*. This value is stored persistently in Redis (`lockDataKey`) and is used by the watchdog to determine the intended full lease duration for renewal cycles. It is **never** modified by the watchdog.
*   **`ownerId`**: A unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). This identifies which application instance and thread/process holds the lock.
*   **Override Precedence**: The order in which configuration settings are applied: Instance-specific settings (via builder methods) > Programmatic Bucket Configuration (via `LockBucketBuilder`) > Global Configuration (YAML/Java defaults in `RedisLockProperties`). (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Pub/Sub (Publish/Subscribe)**: A Redis messaging paradigm used by `UnlockMessageListener` instances to receive notifications when locks are released, enabling efficient, non-polling waits. (See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)).
*   **RedisCoreProperties**: The configuration properties class (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java`) from the `redis-core` module. This module uses these properties to ensure consistent Redis client configuration across the framework. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Reentrant Lock**: A lock that can be acquired multiple times by the same owner (e.g., the same thread) without deadlocking. Each `lock()` call must be matched by an `unlock()` call. Reentrancy data (owner, count) is stored in a Redis Hash. (See [Lock Types Deep Dive](lock_types_deep_dive.md#redisreentrantlock)).
*   **`relativeLeaseTimeMillis`**: The duration (in milliseconds) requested by the user for a lock's lease. This is a relative value (e.g., "30 seconds from now"). It is the input to lock acquisition and extension operations.
*   **`RedisLockAutoConfiguration`**: The Spring Boot `@AutoConfiguration` class responsible for setting up all the necessary beans for the locking module, strictly adhering to framework guidelines. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockautoconfiguration)).
*   **`RedisLockErrorHandler`**: A Spring-managed bean responsible for translating low-level Redis exceptions (often from `ClusterCommandExecutor`) into specific, contextual `AbstractRedisLockException` subtypes. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockerrorhandler)).
*   **`RedisLockOperations`**: A Spring-managed bean that abstracts direct Redis communication for lock-specific commands. **Crucially, it delegates all Redis command executions to `ClusterCommandExecutor` from `redis-core`, ensuring an asynchronous interaction model.** (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockoperations)).
*   **`RedisLockProperties`**: A Spring `@ConfigurationProperties` class that binds global settings from YAML files (e.g., `destilink.fw.locking.redis.*`). (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md) and [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockproperties)).
*   **`requestUuid`**: A unique identifier (UUID) generated *per logical operation* by `RedisLockOperationsImpl`. This UUID is used as the key for the centralized idempotency mechanism. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Response Cache**: A centralized Redis-based cache system that stores the results of mutating lock operations, keyed by `requestUuid`. This cache is managed entirely within Lua scripts and provides the foundation for the idempotency mechanism. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Retry Interval (`retryInterval`)**: The duration `AbstractRedisLock` waits after a semaphore/future wait (which might have timed out based on the current lock holder's TTL) before re-attempting to acquire the lock via Lua script. This acts as a fallback. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **`safetyBufferMillis`**: A calculated duration (`watchdog.interval * watchdog.factor`) used by the watchdog. It determines the minimum `leaseTime` for watchdog eligibility and the target TTL the watchdog aims to maintain for eligible locks.
*   **`ScriptLoader`**: A Spring-managed bean that loads and caches all Lua scripts from the classpath at application startup. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#scriptloader)).
*   **StateLock (`RedisStateLock`)**: A type of lock that, in addition to standard locking, also manages and potentially gates access based on an associated "state" value stored in Redis. (See [Lock Types Deep Dive](lock_types_deep_dive.md#redisstatelock)).
*   **StampedLock (`RedisStampedLock`)**: A lock type that provides optimistic read locking and pessimistic write locking, using a "stamp" or version number to detect intervening writes. (See [Lock Types Deep Dive](lock_types_deep_dive.md#redisstampedlock)).
*   **TTL (Time-To-Live)**: The actual expiration time set on a key in Redis.
*   **`UnlockMessageListener`**: A component, managed by `UnlockMessageListenerManager` and instantiated per lock bucket. It subscribes to a Redis Pub/Sub channel pattern specific to its designated bucket and signals the appropriate `LockSemaphoreHolder` or completes a `CompletableFuture` for asynchronous waiters. (See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md) and [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistener)).
*   **`UnlockMessageListenerManager`**: A Spring-managed bean that creates and manages the lifecycle of `UnlockMessageListener` instances. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistenermanager)).
*   **`UnlockType`**: A string constant published as the message payload to a specific lock's Pub/Sub unlock channel. It indicates the nature of the unlock event. (See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md#5a-defined-unlocktype-values-and-listener-logic)).
*   **Watchdog Renewal Configuration**: The watchdog's behavior is primarily configured via `destilink.fw.locking.redis.watchdog.interval` and `destilink.fw.locking.redis.watchdog.factor`. The effective TTL set by the watchdog during renewals is influenced by these settings. (See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) and [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
```

This completes the first batch. The content has been moved and reorganized, with placeholder links or references to where detailed information will reside in other new files. Redundancy in detailed component descriptions has been pushed towards the conceptual `component_reference.md`.

**Next, I will generate the files for `02_USAGE_AND_CONFIGURATION/` and `03_CORE_MECHANISMS_INTERNAL/`.**

---
**`docs/02_USAGE_AND_CONFIGURATION/configuration.md`**
---
```markdown
# Redis Locking Module: Configuration

## 1. Introduction

This document details the configuration property hierarchy and key settings for the `locking-redis-lock` module. A clear understanding of this hierarchy is essential for correctly configuring lock behavior globally, for a logical bucket (defined programmatically), and for individual lock instances.

The guiding principles for this configuration are:

- **Strict Guideline Adherence**: All configuration aspects strictly adhere to the Destilink Framework guidelines, especially regarding property naming, auto-configuration, and explicit dependency management.
- **Clear Override Precedence**: Instance-specific settings (via builders) > Programmatic Bucket Configuration (via builders) > Global Configuration (YAML/Java defaults).
- **Asynchronous-First Operations**: Core lock operations are asynchronous and executed on Virtual Threads.
- **Separation of Concerns**:
    - **System-Level Configuration**: Properties related to the core operation of the locking system and watchdog, generally not intended for per-lock override. These are configured under `destilink.fw.locking.redis.watchdog.*`.
    - **Lock Implementation Defaults**: Properties that define default behaviors for lock instances (e.g., lease time, retry intervals) and *can* be overridden by lock builders. These are configured under `destilink.fw.locking.redis.defaults.*`.
- **Clarity**: Property names and descriptions are designed to be unambiguous.
- **Mandatory Unlock Notifications**: Redis Pub/Sub for unlock notifications is a core, non-optional mechanism.
- **Leveraging `redis-core` Properties**: The module integrates with and leverages properties from `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` for common Redis client configurations, ensuring consistency across the framework.
- **Redis Key Construction**: All Redis keys **MUST** be constructed using utility classes from `redis-core` and include a lock-type segment. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
- **Lua-Only Redis Operations**: All Redis operations that modify lock state, TTL, or metadata are performed exclusively via Lua scripts. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
- **Idempotency**: All mutating operations are idempotent through a centralized mechanism. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).

For details on the Java configuration classes like `RedisLockProperties` and `LockBucketConfig`, refer to the [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md).

## 2. Configuration Hierarchy and Flow

Global defaults are loaded from YAML into `RedisLockProperties`. This class now contains two nested static classes: `RedisLockProperties.WatchdogProperties` for system-level watchdog settings and `RedisLockProperties.Defaults` for overridable lock implementation defaults.

The `LockBucketRegistry` uses these global defaults (from `RedisLockProperties.Defaults`) and potentially properties from `RedisCoreProperties` to initialize a `LockBucketConfig` when a new bucket context is created via the builder API. Specific properties from `RedisLockProperties.Defaults` can then be overridden programmatically through the builder chain for that bucket or for individual lock instances. Watchdog settings from `RedisLockProperties.WatchdogProperties` are applied globally to the watchdog service.

```mermaid
graph TD
    subgraph YAML_Configuration [\"YAML Configuration (`1000-locking-redis-lock.application.yml`)\"]
        direction LR
        A_WatchdogYAML[\"Watchdog Settings<br>(destilink.fw.locking.redis.watchdog.*)\"]
        B_DefaultsYAML[\"Lock Implementation Defaults<br>(destilink.fw.locking.redis.defaults.*)\"]
        C_GlobalRedisCoreYAML[\"Global Redis Core Settings<br>(destilink.fw.redis.core.*)\"]
    end

    subgraph Java_Configuration_Classes [\"Java Configuration Classes\"]
        direction LR
        D_RedisLockProps[\"<code>RedisLockProperties.java</code><br>(@ConfigurationProperties)<br>Contains WatchdogProperties & Defaults inner classes\"]
        E_WatchdogProps[\"<code>RedisLockProperties.WatchdogProperties</code><br>Holds System-Level Watchdog Config\"]
        F_DefaultsProps[\"<code>RedisLockProperties.Defaults</code><br>Holds Global Lock Implementation Defaults\"]
        G_RedisCoreProps[\"<code>RedisCoreProperties.java</code><br>(from redis-core)<br>Provides Shared Redis Config, including Keyspace Prefixes\"]
        H_LockBucketConfig[\"<code>LockBucketConfig.java</code><br>(Resolved effective settings for a specific bucket,<br>initialized from Global Lock Defaults & Redis Core, then builder overrides.)\"]
    end

    subgraph Builder_Chain [\"Builder Chain (Bucket & Instance Configuration)\"]
        direction LR
        I_LockBucketRegistry[\"<code>LockBucketRegistry</code> (Bean - Lock Factory)\"]
        J_LockBucketBuilder[\"<code>LockBucketBuilder</code><br>(Defines Bucket Name, Scope, Owner Supplier.<br>Sets Bucket-level defaults like <code>defaultLeaseTime</code>.<br><code>LockOwnerSupplier</code> is key for watchdog eligibility.)\"]\
        K_LockConfigBuilder[\"<code>LockConfigBuilder</code><br>(Lock Type Selection)\"]
        L_AbstractLockTypeConfigBuilder[\"<code>AbstractLockTypeConfigBuilder</code><br>(Instance Overrides for chosen lock type, e.g., <code>withLeaseTime()</code>)\"]
        M_SpecificLockBuilders[\"Specific Lock Builders<br>(e.g., <code>ReentrantLockConfigBuilder</code>, <code>StateLockConfigBuilder</code>)<br>(Type-specific params, Further Instance Overrides)\"]
    end

    subgraph Lock_Instance_And_Watchdog [\"Lock Instance & Watchdog Service\"]
        N_LockInstance[\"Actual Lock Instance (e.g., RedisReentrantLock)<br>Operates with resolved configuration from Defaults\"]
        O_LockWatchdog[\"<code>LockWatchdog</code> Service<br>Operates with system-level config from WatchdogProperties\"]
    end

    A_WatchdogYAML -- "populates" --> E_WatchdogProps;
    B_DefaultsYAML -- "populates" --> F_DefaultsProps;
    C_GlobalRedisCoreYAML -- "populates" --> G_RedisCoreProps;
    
    D_RedisLockProps -- "contains" --> E_WatchdogProps;
    D_RedisLockProps -- "contains" --> F_DefaultsProps;

    I_LockBucketRegistry -- "uses global defaults from" --> F_DefaultsProps;
    I_LockBucketRegistry -- "uses shared Redis settings & key prefixes from" --> G_RedisCoreProps;
    I_LockBucketRegistry -- "initializes `LockBucketConfig` with global defaults for" --> J_LockBucketBuilder;
    
    J_LockBucketBuilder -- "Can override bucket-level defaults (e.g. <code>withDefaultLeaseTime()</code>) in its" --> H_LockBucketConfig;
    J_LockBucketBuilder -- "Passes config & transitions to" --> K_LockConfigBuilder;
    K_LockConfigBuilder -- "Passes config & transitions to" --> L_AbstractLockTypeConfigBuilder;
    L_AbstractLockTypeConfigBuilder -- "Is extended by/passes config to" --> M_SpecificLockBuilders;
    M_SpecificLockBuilders -- \".build() creates\" --> N_LockInstance;

    L_AbstractLockTypeConfigBuilder -- "Applies *allowed* instance overrides (e.g. <code>withLeaseTime()</code>) over" --> H_LockBucketConfig;
    N_LockInstance -- "Receives final effective configuration from" --> M_SpecificLockBuilders;
    O_LockWatchdog -- "is configured by" --> E_WatchdogProps;


    style A_WatchdogYAML fill:#f9f,stroke:#333,stroke-width:2px
    style B_DefaultsYAML fill:#f9f,stroke:#333,stroke-width:2px
    style C_GlobalRedisCoreYAML fill:#f9f,stroke:#333,stroke-width:2px
    style D_RedisLockProps fill:#ccf,stroke:#333,stroke-width:2px
    style E_WatchdogProps fill:#cdd,stroke:#333,stroke-width:2px
    style F_DefaultsProps fill:#cdd,stroke:#333,stroke-width:2px
    style G_RedisCoreProps fill:#ccf,stroke:#333,stroke-width:2px
    style H_LockBucketConfig fill:#fdd,stroke:#333,stroke-width:2px
    style I_LockBucketRegistry fill:#cff,stroke:#333,stroke-width:2px
    style J_LockBucketBuilder fill:#cff,stroke:#333,stroke-width:2px
    style K_LockConfigBuilder fill:#cff,stroke:#333,stroke-width:2px
    style L_AbstractLockTypeConfigBuilder fill:#cff,stroke:#333,stroke-width:2px
    style M_SpecificLockBuilders fill:#cff,stroke:#333,stroke-width:2px
    style N_LockInstance fill:#cfc,stroke:#333,stroke-width:2px
    style O_LockWatchdog fill:#cfc,stroke:#333,stroke-width:2px
```

**Flow Explanation:**

1.  **YAML Loading**:
    *   Spring Boot loads watchdog-specific properties from `1000-locking-redis-lock.application.yml` (under `destilink.fw.locking.redis.watchdog.*`) into the `RedisLockProperties.WatchdogProperties` inner class.
    *   Lock implementation defaults from the same file (under `destilink.fw.locking.redis.defaults.*`) are loaded into the `RedisLockProperties.Defaults` inner class.
    *   General Redis client properties from `destilink.fw.redis.core.*` are loaded into the `RedisCoreProperties` bean (from `redis-core`).
2.  **Bucket Configuration Initialization**: When `LockBucketRegistry.builder("bucketName", [applicationInstanceId])` is called:
    *   A `LockBucketConfig` object is created by the `LockBucketBuilder`.
    *   This `LockBucketConfig` is initialized with values from the global `RedisLockProperties.Defaults` and derives relevant settings (e.g., base key prefixes) from `RedisCoreProperties`.
3.  **Programmatic Bucket-Level Overrides**: The `LockBucketBuilder` allows setting certain bucket-wide policies (like scope, `lockOwnerSupplier`, default `leaseTime` via `withDefaultLeaseTime()`, `retryInterval`, `stateKeyExpiration`). These settings modify the `LockBucketConfig` for all locks derived from this builder instance.
4.  **Builder Chain**: The (potentially modified) `LockBucketConfig` is passed through the subsequent builder chain.
5.  **Instance Overrides**: Allowed properties (like `leaseTime` via `withLeaseTime()`, `retryInterval`, type-specific parameters) can be further overridden at the instance level.
6.  **Lock Instantiation**: The `build()` method on the final specific lock builder creates the lock instance with the final, effective configuration derived from `RedisLockProperties.Defaults` and builder overrides.
7.  **Watchdog Configuration**: The `LockWatchdog` service is configured directly by `RedisLockProperties.WatchdogProperties`.

## 3. Watchdog Activation Logic

The `LockWatchdog` service is **always active** when the `locking-redis-lock` module is enabled. However, the watchdog only monitors locks that meet specific conditions, as detailed in the [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md):

1.  **Application Instance Binding**: The lock must be owned by the current application instance, determined by `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returning `true`.
2.  **Sufficient Lease Time**: The user-provided `leaseTime` for the lock instance must be greater than the calculated `safetyBufferMillis` (`watchdog.interval * watchdog.factor`).

When these conditions are met, the lock is registered with the watchdog for automatic lease extension.

## 4. Key Configuration Properties

### 4.1. System-Level Watchdog Configuration (`destilink.fw.locking.redis.watchdog.*`)

These properties are defined in `RedisLockProperties.WatchdogProperties` and are generally not overridable per lock.

| Property (YAML Key)          | Java Field (`WatchdogProperties`) | Description                                                                                                | Default (Java)      | Configuration Level | Developer Override Safety | Notes                                                                                                                             |
| :--------------------------- | :-------------------------------- | :--------------------------------------------------------------------------------------------------------- | :------------------ | :------------------ | :------------------------ | :-------------------------------------------------------------------------------------------------------------------------------- |
| `interval`                   | `interval`                        | The fixed delay (interval) for the watchdog's scheduled renewal task.                                      | `PT5S`              | Global System       | Advanced                  | Controls how frequently the watchdog checks and renews active locks.                                                              |
| `factor`                     | `factor`                          | Multiplier for `interval` to calculate the `safetyBufferMillis`. `safetyBufferMillis = interval * factor`. | `2.0`               | Global System       | Advanced                  | Locks with `leaseTime` > `safetyBufferMillis` are eligible for watchdog monitoring. A factor of 2.0 provides a reasonable buffer. |
| `core-pool-size`             | `corePoolSize`                    | Core number of threads for the watchdog's scheduled executor service.                                      | `2`                 | Global System       | Advanced                  | Adjust based on the expected number of concurrently monitored long-lived locks.                                                   |
| `thread-name-prefix`         | `threadNamePrefix`                | Prefix for threads created by the watchdog's executor service.                                             | `dl-lock-watchdog-` | Global System       | Advanced                  | Helps in thread identification and debugging.                                                                                     |
| `shutdown-await-termination` | `shutdownAwaitTermination`        | Maximum time to wait for the watchdog executor to terminate during application shutdown.                   | `PT30S`             | Global System       | Advanced                  | Ensures graceful shutdown of the watchdog.                                                                                        |

### 4.2. Lock Implementation Defaults (`destilink.fw.locking.redis.defaults.*`)

These properties are defined in `RedisLockProperties.Defaults` and provide global defaults that can be overridden by lock builders.

| Property (YAML Key) | Java Field (`Defaults`) | Description                                                                                                                  | Default (Java) | `LockBucketConfig` Default Source | Programmatic Bucket Override (Builder Method)           | Programmatic Instance Override (Builder Method)              | Effective Value Logic      | Developer Override Safety    |
| :------------------ | :---------------------- | :--------------------------------------------------------------------------------------------------------------------------- | :------------- | :-------------------------------- | :------------------------------------------------------ | :----------------------------------------------------------- | :------------------------- | :--------------------------- |
| `lease-time`        | `leaseTime`             | Default duration a lock will be held if not explicitly specified.                                                            | `PT60S`        | Global                            | `LockBucketBuilder.withDefaultLeaseTime(Duration)`      | `AbstractLockTypeConfigBuilder.withLeaseTime(Duration)`      | Instance > Bucket > Global | Instance Overrideable (Safe) |
| `retry-interval`    | `retryInterval`         | Default wait time between retry attempts for *individual failing Redis operations* within `RedisLockOperationsImpl`.         | `PT0.1S`       | Global                            | `LockBucketBuilder.withDefaultRetryInterval(Duration)`  | `AbstractLockTypeConfigBuilder.withRetryInterval(Duration)`  | Instance > Bucket > Global | Instance Overrideable (Safe) |
| `max-retries`       | `maxRetries`            | Default maximum number of retry attempts for *individual failing Redis operations* within `RedisLockOperationsImpl`.         | `3`            | Global                            | `LockBucketBuilder.withDefaultMaxRetries(int)`          | `AbstractLockTypeConfigBuilder.withMaxRetries(int)`          | Instance > Bucket > Global | Instance Overrideable (Safe) |
| `acquire-timeout`   | `acquireTimeout`        | Default overall timeout for a lock acquisition operation (e.g., for `tryLock(timeout, unit)`). This is an approximate limit. | `PT5S`         | Global                            | `LockBucketBuilder.withDefaultAcquireTimeout(Duration)` | `AbstractLockTypeConfigBuilder.withAcquireTimeout(Duration)` | Instance > Bucket > Global | Instance Overrideable (Safe) |

### 4.3. Other Global Settings (`destilink.fw.locking.redis.*`)

These properties are defined directly in `RedisLockProperties` (not in nested classes).

| Property (YAML Key)               | Java Field (`RedisLockProperties`) | Description                                                                                                                   | Default (Java)           | Configuration Level | Programmatic Bucket Override                                | Programmatic Instance Override | Effective Value Logic                                      | Developer Override Safety  | Notes                                                                     |
| :-------------------------------- | :--------------------------------- | :---------------------------------------------------------------------------------------------------------------------------- | :----------------------- | :------------------ | :---------------------------------------------------------- | :----------------------------- | :--------------------------------------------------------- | :------------------------- | :------------------------------------------------------------------------ |
| `enabled`                         | `enabled`                          | Enables or disables the entire Redis locking module.                                                                          | `true`                   | Global              | N/A                                                         | N/A                            | Global `RedisLockProperties.enabled`.                      | Internal                   | Controls module activation.                                               |
| `unlock-message-listener-enabled` | `unlockMessageListenerEnabled`     | Enables or disables the Redis Pub/Sub unlock message listener.                                                                | `true`                   | Global              | N/A                                                         | N/A                            | Global `RedisLockProperties.unlockMessageListenerEnabled`. | Advanced                   | Disabling forces reliance on fallback re-polling. Generally keep enabled. |
| `state-key-expiration`            | `stateKeyExpiration`               | Default expiration time for Redis keys used by state-aware locks (e.g., `StateLock`) to store internal state.                 | `PT5M`                   | Global              | `LockBucketBuilder.withDefaultStateKeyExpiration(Duration)` | N/A                            | Bucket > Global                                            | Bucket Overrideable (Safe) | Ensures cleanup of auxiliary state keys.                                  |
| `prefix`                          | `prefix`                           | **DEPRECATED.** Global prefix for lock keys is derived from `redis-core`'s `KeyspacePrefixes` for "locking". Will be removed. | `destilink` (DEPRECATED) | Global              | N/A                                                         | N/A                            | Derived from `redis-core`.                                 | Internal                   | Use `destilink.fw.redis.core.keyspace-prefixes.locking`.                  |
| `response-cache-ttl`              | `responseCacheTtl`                 | TTL for response cache entries used for idempotency.                                                                          | `PT5M`                   | Global              | N/A                                                         | N/A                            | Global `RedisLockProperties.responseCacheTtl`.             | Advanced                   | Controls duration for idempotency response caching.                       |
| `health-indicator-enabled`        | `healthIndicatorEnabled`           | Enables the Spring Boot health indicator for the locking module.                                                              | `true`                   | Global              | N/A                                                         | N/A                            | Global.                                                    | Internal                   | For monitoring.                                                           |
| `lock-owner-supplier-bean-name`   | `lockOwnerSupplierBeanName`        | Name of the custom `LockOwnerSupplier` bean if overriding the default.                                                        | `null` (uses default)    | Global              | N/A                                                         | N/A                            | Global.                                                    | Advanced                   | For custom owner ID generation.                                           |

This refined structure provides a clearer separation between system-level (watchdog) and overridable (lock defaults) configurations, enhancing maintainability and adherence to framework guidelines.
```

---
**`docs/02_USAGE_AND_CONFIGURATION/exception_handling.md`**
---
```markdown
# Redis Locking Module: Exception Handling Strategy

## 1. Introduction

Effective error handling is crucial for a robust distributed locking mechanism. The `locking-redis-lock` module employs a structured exception hierarchy to provide clear, contextual information when errors occur. This document outlines this hierarchy, emphasizing integration with the Destilink Framework's structured logging capabilities via the `ExceptionMarkerProvider` interface. This consolidated strategy draws from the strengths of previous planning phases and explicitly accounts for the "Async-First" design and interaction with `redis-core`.

The `RedisLockErrorHandler` component (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockerrorhandler)) is central to this strategy, responsible for translating low-level Redis exceptions (often originating from `redis-core`'s `ClusterCommandExecutor`) into meaningful, module-specific exceptions.

## 2. Base Exception: `AbstractRedisLockException`

All custom exceptions thrown by the Redis locking module **must** extend `AbstractRedisLockException`.

*   **Purpose**:
    *   Provides a common type for catching all lock-related errors.
    *   Carries common contextual information.
    *   Integrates with Destilink Framework's structured logging via `com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`.
*   **Recommendation**: `AbstractRedisLockException` should extend `com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException` (or `MarkerNestedException` if a checked base is preferred) to inherit base marker provider capabilities, or implement `ExceptionMarkerProvider` directly. This aligns with Destilink Framework guidelines.
*   **Required Contextual Fields (Enforced by Constructors)**:
    *   `lockName` (String): The full Redis key of the lock involved.
    *   `lockType` (String): Specific type of lock (e.g., "RedisReentrantLock").
    *   `lockOwnerId` (String, nullable): ID of the owner attempting the operation.
    *   `requestUuid` (String, nullable): Unique ID for the lock operation attempt, generated centrally by `RedisLockOperationsImpl` for idempotency tracking. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
    *   `message` (String): Descriptive error message.
    *   `cause` (Throwable, nullable): Underlying exception.
*   **`ExceptionMarkerProvider` Implementation**:
    The `getMarker()` method in `AbstractRedisLockException` (or its `MarkerNestedRuntimeException` parent) must populate an SLF4J `Marker` with these common fields and allow subclasses to add specific details.

    ```java
    // Simplified example within AbstractRedisLockException or its base
    @Override
    public Marker getMarker() {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("lock.name", this.lockName);
        contextMap.put("lock.type", this.lockType);
        if (this.lockOwnerId != null) contextMap.put("lock.ownerId", this.lockOwnerId);
        if (this.requestUuid != null) contextMap.put("lock.requestUuid", this.requestUuid);
        
        populateSpecificMarkers(contextMap); // For subclasses
        
        contextMap.values().removeIf(Objects::isNull); // Ensure no nulls for Markers.appendEntries
        return Markers.appendEntries(contextMap);
    }

    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // Base implementation does nothing; overridden by subclasses.
    }
    ```

## 3. Specialized Exception Classes

These extend `AbstractRedisLockException` and add specific context via `populateSpecificMarkers`. The nature of the asynchronous operations means that exceptions often propagate through `CompletableFuture` chains.

*   **`LockAcquisitionException`**:
    *   **Purpose**: Failure during lock acquisition (not timeout/interruption). May wrap exceptions from `ClusterCommandExecutor` or Lua script errors.
    *   **Specific Context**: `redis.error` (String, e.g., Lua script error).
*   **`LockAcquisitionTimeoutException`**:
    *   **Purpose**: Asynchronous or blocking acquisition failed within specified `acquireTimeout`.
    *   **Specific Context**: `lock.timeoutMillis` (long), `lock.attempts` (int).
*   **`LockReleaseException`**:
    *   **Purpose**: Failure during lock release. May wrap exceptions from `ClusterCommandExecutor`.
    *   **Specific Context**: `lock.attemptedOwnerId` (String), `lock.actualOwnerId` (String, if known and different).
*   **`LeaseExtensionException` / `LockExtensionException`**:
    *   **Purpose**: Watchdog or reentrant acquisition failed to extend lease. May wrap exceptions from `ClusterCommandExecutor`.
    *   **Specific Context**: `lock.leaseTimeMillis` (long, target lease), `lock.extensionResult` (String/Object from Redis).
*   **`LockInterruptedException`**:
    *   **Purpose**: Wraps `InterruptedException` during synchronous lock wait (when the `CompletableFuture` is blocked on).
    *   **Specific Context**: None beyond base; `InterruptedException` is the `cause`.
*   **`LockCommandException`**:
    *   **Purpose**: Lua script or Redis command execution failures originating from `ClusterCommandExecutor` (not connection issues).
    *   **Specific Context**: `redis.scriptName` or `redis.commandName` (String), `redis.arguments` (String).
*   **`LockConnectionException`**:
    *   **Purpose**: Underlying Redis connection problems detected by `redis-core` or `ClusterCommandExecutor`.
    *   **Specific Context**: `redis.connection.error` (String, connection failure details).
*   **`LockNotOwnedException` / `LockIllegalMonitorStateException`**:
    *   **Purpose**: Attempt to operate on a lock not held by the caller, or in an invalid state for the operation (e.g., unlock by non-owner).
    *   **Specific Context**: `lock.expectedOwnerId` (String), `lock.actualOwnerId` (String, if discoverable).
*   **`LockNotFoundException`**:
    *   **Purpose**: Lock key not found when an operation expected it (e.g., extending a non-existent lock).
*   **`StateLock` Specific Exceptions**:
    *   E.g., `StateMismatchException`, `StateNotFoundException`, `StateUpdateException`.
    *   **Specific Context**: `lock.state.key` (String), `lock.state.expected` (String), `lock.state.actual` (String).
*   **`LockConfigurationException`**:
    *   **Purpose**: Errors in locking module or bucket configuration, potentially related to `RedisCoreProperties` usage.
*   **`LockSerializationException` / `LockDeserializationException`**:
    *   **Purpose**: Errors during serialization/deserialization of lock data.
*   **`LockInternalException`**:
    *   **Purpose**: Unexpected internal errors not covered by other specific exceptions.

### 3.1 Idempotency-Related Exceptions

(For details on the idempotency mechanism, see [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).

With the centralized idempotency mechanism, several new exception types handle idempotency-related failures:

*   **`IdempotencyViolationException`**:
    *   **Purpose**: Thrown when the centralized idempotency system detects an inconsistent state or violation of idempotency guarantees.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.idempotency.cacheKey` (String), `lock.idempotency.violation.type` (String).
    *   **Common Scenarios**: Response cache corruption, conflicting cached results, or unexpected cache state.

*   **`IdempotencyTimeoutException`**:
    *   **Purpose**: Thrown when operations related to the idempotency cache (reading/writing response cache) timeout.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.idempotency.operation` (String, e.g., "cache_read", "cache_write"), `lock.idempotency.timeoutMillis` (long).
    *   **Recovery**: These exceptions are typically retryable as they indicate transient Redis connectivity issues.

*   **`DuplicateRequestException`**:
    *   **Purpose**: Thrown when a duplicate `requestUuid` is detected in scenarios where it should not occur (e.g., concurrent requests with the same UUID).
    *   **Specific Context**: `lock.requestUuid` (String), `lock.duplicate.source` (String), `lock.duplicate.timestamp` (long).
    *   **Note**: This is primarily a defensive exception for detecting programming errors or UUID generation issues.

*   **`ResponseCacheException`**:
    *   **Purpose**: General exception for failures in the response cache system that don't fit other specific categories.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.cache.operation` (String), `lock.cache.key` (String), `redis.error` (String).
    *   **Common Scenarios**: Cache serialization/deserialization failures, unexpected cache data format.

**Idempotency Exception Handling Patterns:**

1.  **Automatic Retry**: `IdempotencyTimeoutException` and some `ResponseCacheException` types are automatically retried by `RedisLockOperationsImpl` using the same `requestUuid`.
2.  **Client Notification**: `IdempotencyViolationException` and `DuplicateRequestException` are typically propagated to the client as they indicate either system inconsistency or client-side issues.
3.  **Logging Enhancement**: All idempotency exceptions include the `requestUuid` in their context, enabling correlation of related log entries across retry attempts.
4.  **Monitoring Integration**: Idempotency exceptions are tagged with specific markers for monitoring and alerting on system health. (See [Metrics](05_OPERATIONAL_ASPECTS/metrics.md)).

## 4. Error Handling by `RedisLockErrorHandler`

The `RedisLockErrorHandler` bean is responsible for:
*   Catching low-level Redis exceptions (e.g., from `io.lettuce.core` or `spring-data-redis`), particularly those originating from `redis-core`'s `ClusterCommandExecutor`.
*   Translating them into the appropriate, context-rich `AbstractRedisLockException` subtype, ensuring all necessary contextual information is populated for effective structured logging via the `ExceptionMarkerProvider` mechanism.
*   Handling `CompletionException` or `ExecutionException` when unwrapping results from `CompletableFuture`s, extracting the true cause.
*   **Centralized Exception Context**: Automatically enriching all exceptions with the `requestUuid` generated by `RedisLockOperationsImpl`, enabling correlation of exceptions across retry attempts and providing complete traceability for debugging and monitoring.
*   **Idempotency-Aware Error Handling**: Recognizing and properly categorizing idempotency-related failures, ensuring appropriate retry behavior and client notification based on the nature of the idempotency violation.

(See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockerrorhandler) for more details).

## 5. Logging Integration

*   The `ExceptionMarkerProvider` implementation in `AbstractRedisLockException` (and its potential parent `MarkerNestedRuntimeException`) ensures that all contextual data from lock exceptions is automatically included as structured fields in JSON log output by the Destilink Core logging framework (specifically by `ExceptionLogstashMarkersJsonProvider`). This strictly adheres to the logging guidelines.
*   General module logging uses SLF4J with parameterized messages, as per framework guidelines.
*   The `LockContextDecorator` enriches logs with MDC data (`lock.name`, `lock.operation`, etc.) for operational context.

This structured approach to exception handling and logging significantly improves diagnosability and monitoring of the distributed locking module, fully integrating with the Async-First design and `redis-core` dependency.
```

---
**`docs/03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md`**
---
```markdown
# Redis Locking Module: Lock Acquisition Flow

## 1. Introduction

This document describes the process by which locks are acquired in the `locking-redis-lock` module. The mechanism is designed for efficiency and reliability, primarily relying on Redis Pub/Sub for unlock notifications to minimize polling, while incorporating a fallback mechanism to handle potential missed notifications and ensuring adherence to the `java.util.concurrent.Lock` interface contract.

The core logic is implemented in `AbstractRedisLock.java` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#abstractredislock)), executed on a Virtual Thread executor, and utilizes `UnlockMessageListener.java` (see [Unlock Messaging Flow](unlock_messaging_flow.md) and [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistener)) and `LockSemaphoreHolder.java` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#locksemaphoreholder)). All Redis interactions for lock acquisition are handled **asynchronously** via `RedisLockOperations` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockoperations)) which uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`.

## 2. Core Principles

*   **Async-First with Virtual Threads**: All lock acquisition attempts are fundamentally asynchronous, returning `CompletableFuture`s, with the logic executed on a dedicated Virtual Thread executor. Synchronous `lock()` and `tryLock()` methods are wrappers around these asynchronous calls. (See [Class Hierarchy](01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md)).
*   **Non-Polling Preference**: Threads/futures waiting for a lock primarily wait passively for an unlock notification via Redis Pub/Sub.
*   **Atomic Operations**: Lock acquisition attempts are performed atomically using Lua scripts (see [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)), executed asynchronously via `ClusterCommandExecutor`.
*   **Semaphore/CompletableFuture-Based Waiting**: `LockSemaphoreHolder` manages threads (for synchronous wrappers) or `CompletableFuture`s (for asynchronous calls) waiting for a specific lock key.
*   **TTL-Aware Fallback**: If a Pub/Sub notification is missed or delayed, waiting mechanisms also time out based on the lock's current TTL in Redis or a configured retry interval, prompting a re-attempt acquisition.
*   **`acquireTimeout` Precedence**: The overall `acquireTimeout` is an approximate limit. It does not interrupt in-flight Redis commands. The result of a Redis command takes precedence over a concurrently expiring timeout.
*   **Registration Before Attempt**: To prevent race conditions, the `LockSemaphoreHolder` is registered with the `UnlockMessageListenerManager` *before* the first Redis lock acquisition attempt is made.

## 3. Lock Acquisition Scenarios

### 3.1. Synchronous Wait Scenario (`lock.lock()`) - Wrapper Implementation

This flow applies when `lock.lock()` is called, requiring an indefinite wait until the lock is acquired or the thread is interrupted. This method acts as a blocking wrapper around the asynchronous acquisition logic.

```mermaid
flowchart TD
    A["Start: lock.lock() (Synchronous Wrapper)"] --> B["Call lockAsync()"]
    B --> C["Block on CompletableFuture.get() (Waits indefinitely or until interrupted)"]
    C --> D{"CompletableFuture Completed?"}
    D -- "Yes (Lock Acquired)" --> E["Return"]
    D -- "Exceptional Completion (e.g., LockAcquisitionException, LockTimeoutException)" --> F["Unwrap Exception and Throw (see [Exception Handling](02_USAGE_AND_CONFIGURATION/exception_handling.md))"]
    D -- "InterruptedException caught during get()" --> G["Re-assert Thread.interrupted() / Throw LockInterruptedException"]

    subgraph AsynchronousLockAcquisition ["Internal Asynchronous Lock Acquisition (lockAsync())"]
        ALA1["Dispatch to Virtual Thread Executor"]
        ALA1 --> ALA2["Get/Create LockSemaphoreHolder for lockKey"]
        ALA2 --> ALA3["Register LockSemaphoreHolder with UnlockMessageListenerManager (CRITICAL: Prevents race condition)"]
        ALA3 --> ALA4["Loop (Until Lock Acquired or Interrupted)"]
        ALA4 --> ALA5["Call tryAcquireLockInternalAsync() (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor)"]
        ALA5 --> ALA6{"Lua Script Result? (e.g., < 0 is acquired)"}
        ALA6 -- "Lock Acquired" --> ALA7["Register with LockWatchdog (if applicable) (see [Watchdog Flow](watchdog_flow.md))"]
        ALA7 --> ALA8["Complete CompletableFuture successfully"]
        ALA6 -- "Lock Held by Other (e.g., returns >= 0 with TTL)" --> ALA9["Get current TTL from script response"]
        ALA9 --> ALA10["Calculate Wait Duration: min(Returned TTL, Configured Retry Interval)"]
        ALA10 --> ALA11["Wait on LockSemaphoreHolder for signal or wait duration to elapse"]
        ALA11 -- "Signaled or Timed Out" --> ALA4
    end
    B --> ALA1
```

**Process Description (Synchronous Wrapper `lock()`):**

1.  **Initiate Acquisition**: The synchronous `AbstractRedisLock.lock()` method is called.
2.  **Delegate to Async**: It immediately calls the asynchronous `lockAsync()` method.
3.  **Block and Wait**: The current thread then blocks indefinitely by calling `CompletableFuture.get()` on the `CompletableFuture` returned by `lockAsync()`.
4.  **Handle Completion**:
    *   **Success**: If `lockAsync()` completes successfully, the `get()` call returns, and the `lock()` method also returns.
    *   **Exceptional Completion**: If `lockAsync()` completes exceptionally, the `get()` call throws an `ExecutionException`. `AbstractRedisLock` unwraps this and re-throws the appropriate `AbstractRedisLockException` subtype.
    *   **Interruption**: If the calling thread is interrupted while blocking on `get()`, an `InterruptedException` is caught. The thread's interrupt status is re-asserted, and a `LockInterruptedException` is thrown.

### 3.2. Asynchronous Acquisition (`lockAsync()`) - Primary Flow

This flow describes the core, non-blocking asynchronous lock acquisition, executed on a Virtual Thread.

```mermaid
flowchart TD
    A["Start: lockAsync() (Primary API)"] --> B["Dispatch task to Virtual Thread Executor"]
    
    subgraph VirtualThreadExecution [Task on Virtual Thread]
        VT1["Create new CompletableFuture<Void> (lockFuture)"]
        VT2["Get/Create LockSemaphoreHolder for lockKey"]
        VT3["Register LockSemaphoreHolder with UnlockMessageListenerManager (CRITICAL)"]
        VT3 --> VT4["Recursive Attempt Function (tryAcquireLoop)"]
        VT4 --> VT5["Call tryAcquireLockInternalAsync() (Executes Lua script via RedisLockOperations)"]
        VT5 --> VT6{"Lua Script Result?"}
        VT6 -- "Success (Lock Acquired)" --> VT7["Register with LockWatchdog (if applicable)"]
        VT7 --> VT8["lockFuture.complete(null)"]
        VT6 -- "Failure (Lock Held by Other)" --> VT9["Calculate Wait Duration: min(Returned TTL, Retry Interval)"]
        VT9 --> VT10["Wait on LockSemaphoreHolder for signal or timeout"]
        VT10 -- "Signaled or Timed Out" --> VT4
        VT6 -- "Error/Exception from Redis" --> VT11["lockFuture.completeExceptionally(exception)"]
    end
    
    B --> VT1
    subgraph CallerThread [Caller Thread]
        C["Receives lockFuture immediately"]
    end
    VT1 --> C
```

**Process Description (Asynchronous `lockAsync()`):**

1.  **Dispatch Task**: `AbstractRedisLock.lockAsync()` is called. It immediately dispatches the acquisition logic as a task to the Virtual Thread executor and returns a `CompletableFuture` (`lockFuture`) to the caller.
2.  **Register Waiter (CRITICAL)**: On the Virtual Thread, the first step is to get/create a `LockSemaphoreHolder` for the `lockKey` and register it with the `UnlockMessageListenerManager`. This must happen *before* any attempt to acquire the lock in Redis to prevent a race condition where an unlock message is missed.
3.  **Attempt Atomic Acquisition (Asynchronously)**:
    *   `tryAcquireLockInternalAsync()` is called. This method, implemented by concrete lock classes, invokes the appropriate Lua script (e.g., "try\_lock.lua") via `RedisLockOperations`. This operation itself returns a `CompletableFuture` representing the script execution.
4.  **Handle Script Result (Chained Asynchronously)**:
    *   **Success**: If the script execution indicates the lock was acquired:
        *   If applicable, the lock is registered with the `LockWatchdog`.
        *   The main `lockFuture` is completed successfully.
    *   **Failure (Lock Held by Another)**: If the Lua script indicates the lock is currently held by another (by returning the PTTL):
        *   The logic proceeds to the waiting phase.
    *   **Failure (Error)**: If a script error or an unrecoverable issue occurs, the `lockFuture` is completed exceptionally.
5.  **Wait for Notification/Retry (Asynchronously)**:
    *   The Virtual Thread waits on the `LockSemaphoreHolder` for a specific duration: `min(Returned TTL from script, Configured Retry Interval)`.
    *   The wait will be interrupted if an unlock notification is received for this `lockKey` via the `UnlockMessageListener`.
    *   Upon waking (due to signal or timeout), the acquisition logic (step 3) is re-attempted. This loop continues until the lock is acquired or an unrecoverable error occurs.

### 3.3. Timeout Scenario (`tryLockAsync(long time, TimeUnit unit)`) - Primary Flow

This flow is very similar to `lockAsync()`, with the key difference being the management of an overall `deadline` (`acquireTimeout`).

```mermaid
flowchart TD
    A[Start: tryLockAsync(timeout, unit)] --> B[Calculate Deadline (startTime + timeout)]
    B --> C[Dispatch to Virtual Thread Executor, passing Deadline]
    
    subgraph VirtualThreadExecution [Task on Virtual Thread with Timeout]
        VT1["Create new CompletableFuture<Boolean> (tryLockFuture)"]
        VT2["Get/Create & Register LockSemaphoreHolder"]
        VT2 --> VT3["Recursive Attempt Function (tryAcquireLoopWithTimeout)"]
        VT3 --> VT4{Is currentTime >= Deadline?}
        VT4 -- Yes --> VT5[tryLockFuture.complete(false)]
        VT4 -- No --> VT6["Call tryAcquireLockInternalAsync()"]
        VT6 --> VT7{"Lua Script Result?"}
        VT7 -- "Success (Lock Acquired)" --> VT8["Register with LockWatchdog (if applicable)"]
        VT8 --> VT9["tryLockFuture.complete(true)"]
        VT7 -- "Failure (Lock Held by Other)" --> VT10["Calculate Wait Duration: min(Returned TTL, Retry Interval, Remaining Timeout)"]
        VT10 --> VT11["Wait on LockSemaphoreHolder for signal or calculated timeout"]
        VT11 --> VT3
        VT7 -- "Error/Exception from Redis" --> VT12["tryLockFuture.completeExceptionally(exception)"]
    end
    
    C --> VT1
    subgraph CallerThread [Caller Thread]
        D["Receives tryLockFuture immediately"]
    end
    VT1 --> D
```

**Process Description (Asynchronous `tryLockAsync()`):**

This flow is nearly identical to `lockAsync()`, with the addition of a `deadline` check at the start of each acquisition attempt loop. If the deadline is met, the loop terminates and the `CompletableFuture` is completed with `false`. The wait duration is also capped by the remaining time until the deadline. The `acquireTimeout` does not interrupt in-flight Redis commands; their result takes precedence.

This refined, Async-First, notification-driven approach with robust fallback and timeout handling ensures efficient and correct distributed lock acquisition.
```

---
**`docs/03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md`**
---
```markdown
# Redis Locking Module: Unlock Messaging Flow

## 1. Introduction

This document describes the messaging mechanism used by the `locking-redis-lock` module, specifically utilizing Redis Publish/Subscribe (Pub/Sub) for unlock notifications. This is a core component of the non-polling lock acquisition strategy (see [Lock Acquisition Flow](lock_acquisition_flow.md)), designed to efficiently signal waiting threads/futures when a lock becomes available.

## 2. Redis Publish/Subscribe (Pub/Sub) Overview

Redis Pub/Sub allows publishers to send messages to channels, and subscribers to listen to these channels without direct knowledge of each other.

*   **Publishers**: Operations (typically Lua scripts) that successfully release a lock.
*   **Channels**: The `UnlockMessageListener` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistener)) subscribes to a channel pattern like `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`. The specific lock instance identifier (e.g., `{order123}`) is part of the actual channel name to which messages are published (e.g., `<prefix>:<bucketName>:__unlock_channels__:reentrant:{order123}`). (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).
*   **Message Payload**: The message published by Lua scripts to a specific lock's Pub/Sub channel contains *only* an `UnlockType` string (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED"). The `UnlockMessageListener` derives the `lockName` (the specific lock identifier like `{order123}`) from the channel name it received the message on.
*   **Subscribers**: `UnlockMessageListener` instances, typically one per bucket, managed by `UnlockMessageListenerManager` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistenermanager)).

## 3. Unlock Notification Mechanism Flow

```mermaid
graph TD
    A[Lock Holder Calls unlock()/unlockAsync()] --> B{AbstractRedisLock: releaseLockAsync()};
    B --> C[RedisLockOperations: Executes Unlock Lua Script (e.g., unlock.lua)];
    subgraph Lua Script on Redis Server
        D[Lua: Verifies Ownership & Releases Lock Data (e.g., HASH fields, DEL key)];
        D --> E{Lock Fully Released?};
        E -- Yes --> F[Lua: Constructs Specific Unlock Channel Name<br/>e.g., <prefix>:<bucket>:<lockType>:{lockName}];
        F --> G[Lua: Publishes UnlockType (string) to Specific Channel];
    end
    C --> D;
    G --> H[Redis Pub/Sub Mechanism];

    subgraph UnlockMessageListener (Per Bucket - Subscribed to Pattern)
        I[UnlockMessageListener: Receives Message on Matched Channel];
        I --> J[Listener: Derives specific 'lockName' (e.g., {order123}) from Channel Name];
        J --> K[Listener: Parses 'UnlockType' from Message Payload];
        K --> L[Listener: Retrieves LockSemaphoreHolder for 'lockName'];
        L --> M{UnlockType requires specific signaling?};
        M -- Yes --> N[Listener: Signals LockSemaphoreHolder with specific strategy (e.g., release N permits)];
        M -- No (e.g. Regular Release) --> O[Listener: Signals LockSemaphoreHolder (e.g., release 1 permit)];
    end
    H --> I;

    subgraph Waiting Client (via LockSemaphoreHolder)
        P[LockSemaphoreHolder: Semaphore is released / CompletableFuture is completed];
        P --> Q[Waiting Virtual Thread in AbstractRedisLock wakes up];
        Q --> R[Re-attempts Lock Acquisition (see Lock Acquisition Flow)];
    end
    N --> P;
    O --> P;

    style A fill:#lightgray
    style B fill:#lightblue
    style C fill:#lightblue
    style D fill:#adebad
    style E fill:#adebad
    style F fill:#adebad
    style G fill:#adebad
    style H fill:#ffcccc
    style I fill:#ccffcc
    style J fill:#ccffcc
    style K fill:#ccffcc
    style L fill:#ccffcc
    style M fill:#ccffcc
    style N fill:#ccffcc
    style O fill:#ccffcc
    style P fill:#ffffcc
    style Q fill:#ffffcc
    style R fill:#ffffcc
```

**Detailed Steps:**

1.  **Publish on Unlock**: When a lock is successfully released (typically via an atomic Lua script like `unlock.lua` or `unlock_state_lock.lua` executed by `RedisLockOperations`), the script publishes a message.
    *   The script constructs the **specific unlock channel name** (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}`).
    *   The message payload published to this channel contains *only* an `UnlockType` string (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED"). This `UnlockType` indicates the nature of the unlock event. (See section 5.A for defined types).
2.  **Subscription and Listening**:
    *   The `UnlockMessageListenerManager` ensures that an `UnlockMessageListener` for the relevant `bucketName` is active.
    *   This listener subscribes to a **pattern** for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
3.  **Message Reception and Processing by `UnlockMessageListener`**:
    *   When an `UnlockMessageListener` receives a message on a channel matching its subscription pattern:
        *   It derives the specific `lockName` (e.g., `{order123}`) from the *actual channel name* on which the message was received (e.g., from `<prefix>:<bucketName>:__unlock_channels__:reentrant:{order123}`).
        *   It parses the `UnlockType` from the message payload.
        *   It retrieves the associated `LockSemaphoreHolder` for the derived `lockName` (from an internal map within the listener). The `LockSemaphoreHolder` is responsible for managing both synchronous `Semaphore` waits and asynchronous `CompletableFuture` completions.
        *   Based on the `UnlockType` and potentially the type of lock/waiters, it intelligently signals the `LockSemaphoreHolder`. For synchronous waits, this means releasing permits on the `Semaphore`. For asynchronous waits, this means completing the associated `CompletableFuture`s, which in turn triggers the lock acquisition retry logic within `AbstractRedisLock`.
4.  **Wake Up and Retry**: Signalling the `LockSemaphoreHolder` wakes up one or more waiting threads (for synchronous `lock()`) or triggers the completion logic for `CompletableFuture`s (for `lockAsync()`). These awakened/triggered operations then re-attempt to acquire the lock as described in the [Lock Acquisition Flow](lock_acquisition_flow.md).

## 4. Key Components Involved

*   **Lua Scripts**: Responsible for atomically releasing lock data and publishing the `UnlockType` to the correct specific channel. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **`UnlockMessageListenerManager`**: Manages the lifecycle of `UnlockMessageListener` instances. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistenermanager)).
*   **`UnlockMessageListener`**: Subscribes to channel patterns, receives messages, derives `lockName` from the channel, parses `UnlockType` from payload, and signals the correct `LockSemaphoreHolder`. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#unlockmessagelistener)).
*   **`LockSemaphoreHolder`**: Manages waiting threads/futures for a specific lock key. (See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#locksemaphoreholder)).

## 5. Unlock Channel and Message Details

### 5.A. Defined `UnlockType` Values and Listener Logic

The `UnlockType` string published by Lua scripts provides crucial information to the `UnlockMessageListener` for optimized waking of waiting threads/futures. The listener derives the `lockName` (e.g., `{order123}`) from the channel name (e.g., `<prefix>:<bucketName>:__unlock_channels__:reentrant:{order123}`) and uses the `UnlockType` from the message payload to decide its signaling strategy for the `LockSemaphoreHolder`.

The following `UnlockType` values are defined:

*   **`REENTRANT_FULLY_RELEASED`**:
    *   **Published By**: `unlock.lua` when a reentrant lock is fully released.
    *   **Listener Logic**: Signals one permit/completes one future. Suitable for exclusive locks.

*   **`NON_REENTRANT_RELEASED`**:
    *   **Published By**: `unlock.lua` (or equivalent) when a non-reentrant exclusive lock is released.
    *   **Listener Logic**: Signals one permit/completes one future.

*   **`STATE_LOCK_RELEASED_STATE_UNCHANGED`**:
    *   **Published By**: `unlock_state_lock.lua` when a state lock is released, state unchanged.
    *   **Listener Logic**: Signals one permit/completes one future.

*   **`STATE_LOCK_RELEASED_STATE_UPDATED`**:
    *   **Published By**: `unlock_state_lock.lua` when a state lock is released, state was updated.
    *   **Listener Logic**: Signals one permit/completes one future.

*   **`RW_READ_RELEASED_WAKEN_READERS`**:
    *   **Published By**: `unlock_read_lock.lua` (for `RedisReadWriteLock`) when a read lock is released, prioritize waking readers.
    *   **Listener Logic**: May signal multiple permits/complete multiple futures if it can identify multiple waiters for a read lock.

*   **`RW_READ_RELEASED_WAKEN_SINGLE_WRITER`**:
    *   **Published By**: `unlock_read_lock.lua` when a read lock is released (potentially last reader), and writers are waiting.
    *   **Listener Logic**: Signals one permit/completes one future.

*   **`RW_WRITE_RELEASED_WAKEN_ALL`**:
    *   **Published By**: `unlock_write_lock.lua` (for `RedisReadWriteLock`) when a write lock is released.
    *   **Listener Logic**: May signal multiple permits/futures. Lua script for write lock acquisition ensures only one writer succeeds.

*   **`STAMPED_READ_RELEASED`**:
    *   **Published By**: `unlock_stamped_lock.lua` when a read stamp is released.
    *   **Listener Logic**: Similar to `RW_READ_RELEASED_WAKEN_READERS`.

*   **`STAMPED_WRITE_RELEASED`**:
    *   **Published By**: `unlock_stamped_lock.lua` when a write stamp is released.
    *   **Listener Logic**: Similar to `RW_WRITE_RELEASED_WAKEN_ALL`.

*   **`STAMPED_CONVERTED_TO_READ`**:
    *   **Published By**: `convert_to_read_lock.lua` (for `RedisStampedLock`) when a write lock is converted to read.
    *   **Listener Logic**: May signal multiple permits/futures if other readers were waiting.

*   **`STAMPED_CONVERTED_TO_WRITE`**:
    *   **Published By**: `convert_to_write_lock.lua` (for `RedisStampedLock`) when a read lock is upgraded to write.
    *   **Listener Logic**: Typically no signal needed as current thread acquired the lock.

The `UnlockMessageListener`'s implementation should use these `UnlockType` values to optimize waking strategies, reducing unnecessary contention and improving throughput.

## 6. Reliability and Considerations

*   **Message Delivery**: Redis Pub/Sub is "fire-and-forget"; delivery is not guaranteed.
*   **Fallback Mechanism**: The [Lock Acquisition Flow](lock_acquisition_flow.md) includes a fallback mechanism (timeout based on current lock holder's TTL or `retryInterval`) to handle missed notifications.
*   **Executor for Listeners**: `UnlockMessageListener`'s `onMessage` processing should use a dedicated executor (e.g., virtual threads or a dedicated thread pool) to avoid blocking Redis client message dispatching threads.
*   **Stale Permits/Completions**: The `LockSemaphoreHolder` and `AbstractRedisLock` logic must handle scenarios where a signal arrives for a waiter that has already timed out and retried, or given up.

This messaging system, coupled with semaphore/future-based waiting and TTL-based fallbacks, provides an efficient and robust mechanism for non-polling distributed lock acquisition.
```

---
**`docs/03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md`**
---
```markdown
# Redis Locking Module: Watchdog Flow

## 1. Introduction

The `LockWatchdog` is a critical component in the `locking-redis-lock` module. Its primary responsibility is to prevent distributed locks from expiring prematurely while they are still actively and legitimately held by an application instance. This mechanism is essential for long-running operations that might exceed the initial Time-To-Live (TTL) set on a Redis lock key.

For component details, see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockwatchdog).
For configuration, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md).
The Lua script used is `watchdog_refresh_lock.lua` (see [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).

## 2. Purpose and Design Principles

*   **Lease Extension Pattern**: The watchdog implements the lease extension pattern.
*   **Prevent Premature Expiration**: It periodically renews the lock's TTL in Redis.
*   **Resilience**: If the application instance crashes or the lock is explicitly released, the watchdog stops renewing, allowing the lock to expire naturally.
*   **Preservation of User Intent**: The watchdog never modifies the user's intended total lease duration (`originalLeaseTimeMillis`). It only manages the physical TTL (`expiresAtMillis`) in Redis to keep the lock alive up to that intended duration.
*   **Always Active, Conditionally Per-Lock**: The watchdog service is always active. It monitors individual locks based on their eligibility.

## 3. Activation and Initial TTL

The watchdog becomes active for a specific lock instance if **all** of the following are true:

1.  **Application Instance Binding**: The `LockOwnerSupplier` permits watchdog use for the lock via `canUseWatchdog(lockIdentifier, ownerId)`.
2.  **Sufficient Lease Time**: The user-provided `leaseTime` for the lock is greater than the calculated `safetyBufferMillis`.

*   **`safetyBufferMillis`**: Calculated as `watchdog.interval * watchdog.factor`.

### Initial Lock Acquisition TTL:

*   **If Watchdog NOT Used**: The lock is acquired in Redis with the full `leaseTime` as its initial TTL.
*   **If Watchdog IS Used**: The lock is acquired in Redis with an initial TTL of `safetyBufferMillis`. The lock is then registered with the `LockWatchdog`, which stores the `originalLeaseTimeMillis` (the user's full intended lease).

## 4. Operational Flow

```mermaid
graph TD
    A[Lock Successfully Acquired by AbstractRedisLock] --> B{Activation Conditions Met? (leaseTime > safetyBufferMillis, etc.)};
    B -- Yes --> C[Register with Watchdog: (lockKey, ownerId, originalLeaseTimeMillis)];
    B -- No --> D[Watchdog Not Activated for this Lock];

    subgraph LockWatchdog Bean
        E[registerLock(): Stores LockInfo in monitoredLocks map];
        F[extendLocks() Method (runs every 'watchdog.interval')];
        F --> G{For Each Monitored LockInfo};
        G --> H[Calculate remainingUserTimeMillis = lockInfo.userIntendedExpireTime - currentTime];
        H --> I{Decision Point};
        I -- "remainingUserTime > safetyBuffer" --> J[Standard Renewal: Extend lease by safetyBuffer];
        I -- "0 < remainingUserTime <= safetyBuffer" --> K[Final Leg Renewal: Extend lease to exact userIntendedExpireTime];
        I -- "remainingUserTime <= 0" --> L[User's Lease Expired];

        J --> M[Execute watchdog_refresh_lock.lua with PEXPIREAT(currentTime + safetyBuffer)];
        K --> N[Execute watchdog_refresh_lock.lua with PEXPIREAT(userIntendedExpireTime)];
        L --> O[Unregister Lock from Watchdog];

        M --> P[Update LockInfo in map];
        N --> P;
    end
    C --> E;

    Q[AbstractRedisLock: unlock() called] --> R[lockWatchdog.unregisterLock(lockKey, ownerId)];
    subgraph LockWatchdog Bean
        S[unregisterLock(): Removes LockInfo from monitoredLocks];
    end
    R --> S;

    style A fill:#lightgray;
    style B fill:#lightgray;
    style C fill:#lightgray;
    style D fill:#lightgray;
    style E fill:#ccffcc;
    style F fill:#ccffcc;
    style G fill:#ccffcc;
    style H fill:#ccffcc;
    style I fill:#ccffcc;
    style J fill:#ccffcc;
    style K fill:#ccffcc;
    style L fill:#ccffcc;
    style M fill:#ccffcc;
    style N fill:#ccffcc;
    style O fill:#ccffcc;
    style P fill:#ccffcc;
    style Q fill:#lightgray;
    style R fill:#lightgray;
    style S fill:#ccffcc;
```

### 4.1. Lock Registration

1.  When a lock is acquired and meets all activation conditions.
2.  `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, lockOwnerId, originalLeaseTimeMillis)`.
3.  The `LockWatchdog` stores this information in a `LockInfo` object within its `monitoredLocks` map.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `LockWatchdog.extendLocks()` method runs at a fixed delay defined by `watchdog.interval`.
2.  It iterates through all monitored locks.
3.  For each lock, it calculates `remainingUserTimeMillis = lockInfo.getUserIntendedExpireTimeMillis() - System.currentTimeMillis()`.
4.  **Refresh Strategy**:
    *   **Standard Operation** (`remainingUserTimeMillis > safetyBufferMillis`): The watchdog extends the lock's physical TTL in Redis to maintain the safety buffer. It calls `watchdog_refresh_lock.lua` with a target `PEXPIREAT` of `currentTime + safetyBufferMillis`.
    *   **Final Leg** (`0 < remainingUserTimeMillis <= safetyBufferMillis`): The lock is approaching its final, user-intended expiration. The watchdog performs a final extension, calling `watchdog_refresh_lock.lua` with a target `PEXPIREAT` of `lockInfo.getUserIntendedExpireTimeMillis()`. The lock is then typically unregistered after this cycle.
    *   **Expired** (`remainingUserTimeMillis <= 0`): The user's intended lease has passed. The watchdog unregisters the lock, ceasing all renewal attempts.
5.  All extensions are performed by the `watchdog_refresh_lock.lua` script, which atomically verifies ownership before extending the lease.

### 4.3. User-Initiated Lease Extension

*   When a user calls `extendLease(newRelativeLeaseTime)`:
    *   This `newRelativeLeaseTime` becomes the *new* `originalLeaseTimeMillis` for the lock.
    *   The `extend_lock.lua` script is called, which updates both the `originalLeaseTimeMillis` in the lock's metadata hash and the physical `expiresAtMillis` of the lock key in Redis.
    *   The watchdog registration is updated with the new intended expiration time.

### 4.4. Lock Unregistration

*   When `unlock()` is called, `AbstractRedisLock` calls `lockWatchdog.unregisterLock()`.
*   The watchdog removes the lock from its `monitoredLocks` map, stopping any further lease extensions.

## 5. Key Considerations

*   **Atomicity of `watchdog_refresh_lock.lua`**: This script is critical. It must perform the ownership check and the `PEXPIREAT` command atomically.
*   **Redis Time**: All expiry calculations in Lua scripts use `redis.call('TIME')` for precision and to avoid issues with clock skew between clients and the server.
*   **`originalLeaseTimeMillis` Preservation**: The watchdog **never** overwrites the `originalLeaseTimeMillis` stored in Redis. It only uses it to calculate the target `expiresAtMillis` for renewal.

The `LockWatchdog` ensures the liveness and safety of distributed locks held for extended periods, driven by the `LockOwnerSupplier` and lease duration.
```

---
**`docs/03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md`**
---
```markdown
# Redis Locking Module: Idempotency Mechanism

## 1. Introduction

Idempotency is a critical property for distributed systems, ensuring that performing an operation multiple times has the same effect as performing it once. The `locking-redis-lock` module implements a centralized idempotency mechanism for all its mutating Redis operations (e.g., lock acquisition, release, lease extension, state updates). This mechanism protects against duplicate operations that might occur due to network retries, client-side retries, or other transient failures.

This document details the design and flow of this centralized idempotency system.

## 2. Core Principles

*   **Centralized Responsibility**: `RedisLockOperationsImpl` (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockoperations)) is the single point of responsibility for generating unique `requestUuid` values for each logical lock operation and ensuring these UUIDs are used consistently.
*   **Request UUID (`requestUuid`)**: Each logical mutating operation (e.g., a single call to `tryLock()`, `unlock()`, `extendLease()`) is assigned a unique `requestUuid`. This same `requestUuid` is used for all internal retry attempts of that single logical operation.
*   **Response Cache**: A Redis-based cache (using simple Redis String keys) stores the *result* of successfully completed mutating operations, keyed by the `requestUuid`.
*   **Lua Script Integration (Idempotency Wrapper)**: The idempotency check (looking up the `requestUuid` in the response cache) and the caching of the result are performed atomically *within* the Lua scripts that execute the core lock logic.
*   **Configurable Cache TTL**: The duration for which responses are cached is configurable via `responseCacheTtl`.

## 3. Idempotency Flow

```mermaid
flowchart TD
    A[Client Initiates Mutating Lock Operation (e.g., tryLock, unlock)] --> B[RedisLockOperationsImpl receives call];
    B --> C[Generate NEW Unique requestUuid for this logical operation];
    C --> D[Construct Response Cache Key:<br/>&lt;prefix&gt;:&lt;bucket&gt;:__resp_cache__:&lt;lockType&gt;:{&lt;lockName&gt;}:&lt;requestUuid&gt; <br/>(See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md))];
    D --> E[Call appropriate Lua Script via ClusterCommandExecutor,<br/>passing requestUuid, responseCacheKey, and responseCacheTtl];

    subgraph LuaScriptExecution [Lua Script Execution on Redis Server]
        LS1[Receive requestUuid, responseCacheKey, responseCacheTtl, and operation parameters]
        LS1 --> LS2{Check Response Cache (KEYS[responseCacheKey]) for requestUuid?}
        LS2 -- "Cache Hit: Result Found" --> LS3[Return Cached Result<br/>(Operation already completed)]
        LS2 -- "Cache Miss: No Result Found" --> LS4[Execute Core Lock Operation Logic (e.g., acquire, release)]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Success" --> LS6[Store Operation Result in Response Cache<br/>Key: responseCacheKey, Value: result, TTL: responseCacheTtl]
        LS6 --> LS7[Return Actual Operation Result]
        LS5 -- "Failure" --> LS8[Return Error/Failure Result<br/>(Typically, failures are NOT cached to allow retries of the operation itself)]
    end

    E --> LS1;
    LS3 --> F[RedisLockOperationsImpl receives Cached Result];
    F --> G[Return Cached Result to Client - Idempotent behavior achieved];
    LS7 --> H[RedisLockOperationsImpl receives Fresh Result];
    H --> I[Return Fresh Result to Client - Operation completed successfully];
    LS8 --> J[RedisLockOperationsImpl receives Error/Failure Result];
    J --> K[Propagate Error to Client - Operation failed, can be retried with SAME requestUuid if applicable];

    subgraph RetryScenario [Client-Side or Internal Retry Scenario]
        L[Network Failure/Timeout During Initial Request OR Internal Retry Logic] --> M[Operation Retried with SAME requestUuid];
        M --> B; # Retried call goes back to RedisLockOperationsImpl
        B -- On Retry --> C_Retry[Use EXISTING requestUuid for this logical operation's retry];
        C_Retry --> D;
    end
```

**Detailed Steps:**

1.  **Operation Initiation**: A client calls a mutating method on a lock instance (e.g., `redisReentrantLock.tryLock()`, `redisStateLock.updateStateAsync()`). This call eventually reaches a method in `RedisLockOperationsImpl`.
2.  **`requestUuid` Generation**: For each *new logical operation*, `RedisLockOperationsImpl` generates a unique `requestUuid` (e.g., using `UUID.randomUUID().toString()`). This `requestUuid` will be associated with this specific attempt (and its potential retries) to perform the operation.
3.  **Response Cache Key Construction**: `RedisLockOperationsImpl` constructs the full Redis key for the response cache entry. This key includes the `requestUuid` and follows the standard key schema (e.g., `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`).
4.  **Lua Script Invocation**: `RedisLockOperationsImpl` invokes the relevant Lua script (e.g., `try_lock.lua`, `unlock.lua`). It passes:
    *   The `responseCacheKey` as one of the `KEYS` arguments.
    *   The `requestUuid` as one of the `ARGV` arguments.
    *   The `responseCacheTtl` (from `RedisLockProperties`, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)) as one of the `ARGV` arguments.
5.  **Lua Script Idempotency Check (Atomic)**:
    *   The Lua script first attempts to `GET` the `responseCacheKey`.
    *   **Cache Hit**: If the key exists and a value is found, this indicates the operation was already successfully completed. The script immediately returns the cached value.
    *   **Cache Miss**: If the key does not exist, the script proceeds to execute its core logic (e.g., attempting to acquire the lock, release the lock).
6.  **Lua Script Core Logic & Result Caching (Atomic)**:
    *   If the core logic is executed (cache miss):
        *   **Success**: If the operation succeeds, the script stores its result (e.g., a structured array response) into the `responseCacheKey` using `SET` with `PX` (for TTL in milliseconds, derived from `responseCacheTtl`). It then returns the actual result.
        *   **Failure**: If the core operation fails (e.g., lock not acquired because it's held by another, owner mismatch on unlock), the script typically does *not* cache this failure. It returns the failure indication (e.g., PTTL of current lock holder, "0" for unlock failure). This allows the operation itself to be retried if appropriate.
7.  **Result Propagation**: `RedisLockOperationsImpl` receives the result from the Lua script (either fresh or cached) and returns it to the lock instance, which then propagates it to the original caller.
8.  **Retries**: If an operation needs to be retried (either by the client or by internal retry logic within `RedisLockOperationsImpl` for transient issues), the **same `requestUuid`** generated in Step 2 is reused for the retry attempts. This ensures that if the original attempt actually succeeded but its response was lost, the retry will hit the response cache.

## 4. Key Benefits

*   **Consistency Across Lock Types**: All lock types and mutating operations benefit from this uniform mechanism.
*   **Network Resilience**: Protects against duplicate executions due to transient network issues where a command might have succeeded on Redis but the response failed to reach the client.
*   **Simplified Client Logic**: Clients (or higher layers in the locking module) can retry operations more safely.
*   **Atomic Guarantees**: The check-cache-execute-store-cache logic is performed atomically within Lua scripts.

## 5. Configuration

*   `destilink.fw.locking.redis.response-cache-ttl`: Defines the Time-To-Live for entries in the response cache. This should be configured to be long enough to cover typical client retry windows and potential clock drift, but not so long as to consume excessive Redis memory if `requestUuid`s are very numerous.

## 6. Exception Handling

Specific exceptions related to idempotency issues are defined:
*   `IdempotencyViolationException`
*   `IdempotencyTimeoutException`
*   `DuplicateRequestException`
*   `ResponseCacheException`

(See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md#31-idempotency-related-exceptions) for details).

This centralized idempotency mechanism significantly enhances the robustness and reliability of the distributed locking module.
```

This completes the second batch.

**Next, I will generate the files for `04_IMPLEMENTATION_REFERENCE_INTERNAL/`. This will involve creating the significant `component_reference.md` file.**

---
**`docs/04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md`**
---
```markdown
# Redis Locking Module: Component Reference

This document provides detailed descriptions of the shared Spring-managed beans and critical internal (non-bean) classes within the `locking-redis-lock` module. Understanding these components is crucial for developers working on or extending the module.

For a high-level view of how these components interact, see the [Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md).
For how locks are configured, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md).

## 1. Spring-Managed Beans (@Beans)

These components are typically defined as singleton beans within `RedisLockAutoConfiguration`.

### 1.1. `RedisLockAutoConfiguration`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.config` (example)
*   **Type**: Spring `@AutoConfiguration`
*   **Purpose**: The module's entry point for Spring Boot.
*   **Key Responsibilities**:
    *   Conditionally enables the locking module based on the `destilink.fw.locking.redis.enabled` property.
    *   Explicitly defines all shared service beans (listed below) using `@Bean` methods.
    *   Defines a shared `ExecutorService` bean for Virtual Threads (`Executors.newVirtualThreadPerTaskExecutor()`) used by lock operations.
    *   **Strictly adheres to no `@ComponentScan`** as per Destilink Framework guidelines.
    *   Ensures constructor injection for dependencies where applicable.
    *   Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.
*   **Dependencies**: `RedisLockProperties`, `RedisCoreProperties`.

### 1.2. `LockComponentRegistry`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.registry` (example)
*   **Type**: Spring Bean
*   **Purpose**: A central registry providing access to shared locking services.
*   **Key Responsibilities**:
    *   Acts as a holder for instances of `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, and an `ObjectProvider<LockMonitor>`.
    *   Simplifies dependency injection into lock builders (e.g., `LockBucketBuilder`) and internal components like `AbstractRedisLock`.
*   **Dependencies**: All the shared services it holds.

### 1.3. `RedisLockProperties`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.config` (example)
*   **Type**: Spring `@ConfigurationProperties("destilink.fw.locking.redis")`
*   **Purpose**: Binds global configurations from YAML files.
*   **Key Responsibilities**:
    *   Holds global settings like `enabled`, `stateKeyExpiration`, `responseCacheTtl`.
    *   Contains nested static classes for structured properties:
        *   `RedisLockProperties.WatchdogProperties` (prefix: `destilink.fw.locking.redis.watchdog.*`): Holds system-level watchdog configurations (`interval`, `factor`).
        *   `RedisLockProperties.Defaults` (prefix: `destilink.fw.locking.redis.defaults.*`): Holds global default values for lock implementation properties (e.g., `leaseTime`, `retryInterval`).
    *   Works in conjunction with `com.tui.destilink.framework.redis.core.config.RedisCoreProperties` for shared Redis client configuration (e.g., keyspace prefixes).
*   **Note**: Does **not** define lock buckets via YAML; bucket configuration is programmatic. See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md) for property details.

### 1.4. `LockBucketRegistry`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.registry` (example)
*   **Type**: Spring Bean
*   **Purpose**: The primary factory for initiating the lock creation process using a fluent builder API.
*   **Key Responsibilities**:
    *   Provides the entry point method (e.g., `builder(String bucketName)` or `builder(String bucketName, String applicationInstanceId)`).
    *   Injects the `LockComponentRegistry` and an initialized `LockBucketConfig` (based on global defaults from `RedisLockProperties.Defaults` and `RedisCoreProperties`) into the `LockBucketBuilder` it creates.
*   **Dependencies**: `LockComponentRegistry`, `RedisLockProperties`, `RedisCoreProperties`.

### 1.5. `ScriptLoader`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.lua` (example)
*   **Type**: Spring Bean
*   **Purpose**: Loads and caches Lua scripts used for atomic Redis operations.
*   **Key Responsibilities**:
    *   Loads all necessary Lua scripts (e.g., `try_lock.lua`, `unlock.lua`, etc.) from the classpath (typically `src/main/resources/lua/`) during application startup (e.g., via `@PostConstruct` or `InitializingBean`).
    *   Caches the loaded scripts (e.g., as `org.springframework.data.redis.core.script.RedisScript<T>` instances) for efficient reuse by `RedisLockOperations`.
*   **Dependencies**: None directly, but interacts with classpath resources.

### 1.6. `RedisLockOperations` (Interface) / `RedisLockOperationsImpl` (Implementation)

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.operations` (example)
*   **Type**: Spring Bean (`RedisLockOperationsImpl` implementing `RedisLockOperations`)
*   **Purpose**: Abstraction layer for Redis communication specific to all lock operations, ensuring atomicity and handling idempotency.
*   **Key Responsibilities**:
    *   **Exclusively uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis command executions and Lua script invocations.** This ensures all Redis interactions are asynchronous and leverage the `redis-core` module as mandated.
    *   **Centralized Idempotency Management**:
        *   Generates a unique `requestUuid` for each logical lock operation (e.g., `tryLock`, `unlock`, `extendLease`). This same UUID is used for all internal retry attempts of that single logical operation. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
        *   Constructs response cache keys (e.g., `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`).
        *   Passes `requestUuid`, `responseCacheKey`, and `responseCacheTtl` to Lua scripts for atomic idempotency handling.
    *   Executes Lua scripts retrieved from `ScriptLoader`.
    *   Handles translation of structured script results (e.g., `{status, expiresAt, originalLeaseTime}`) into meaningful outcomes for `AbstractRedisLock`.
    *   Implements retry logic for individual Redis operations based on `defaults.maxRetries` and `defaults.retryInterval`.
    *   Interacts with `RedisLockErrorHandler` to translate Redis/script exceptions.
*   **Dependencies**: `ClusterCommandExecutor` (from `redis-core`), `ScriptLoader`, `RedisLockErrorHandler`, `RedisLockProperties` (for `responseCacheTtl` and `defaults`).

### 1.7. `DefaultLockOwnerSupplier`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.owner` (example)
*   **Type**: Spring Bean (implements `LockOwnerSupplier` interface)
*   **Purpose**: Provides the default mechanism for generating unique owner IDs for locks.
*   **Key Responsibilities**:
    *   Generates a lock owner ID (`ownerId`), typically by combining an application instance identifier (e.g., from Spring application name/port, or a configurable instance ID) and the current thread's ID (`Thread.currentThread().getId()`).
    *   Implements `canUseWatchdog(String lockIdentifier, String ownerId)` to determine if a lock associated with the given owner ID is eligible for watchdog monitoring (usually true if the owner ID represents the current application instance).
    *   Validates the `ownerId` format against a hardcoded regex pattern.
*   **Note**: Can be replaced by a custom `LockOwnerSupplier` bean implementation if different owner ID generation or watchdog eligibility logic is needed. The custom bean name can be specified via `destilink.fw.locking.redis.lock-owner-supplier-bean-name`.

### 1.8. `RedisLockErrorHandler`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.exception` (example)
*   **Type**: Spring Bean
*   **Purpose**: Centralizes the logic for interpreting and translating exceptions from Redis operations into module-specific, context-rich exceptions.
*   **Key Responsibilities**:
    *   Catches low-level Redis exceptions (e.g., from `io.lettuce.core`, `spring-data-redis`), particularly those propagated by `ClusterCommandExecutor` from `redis-core`.
    *   Translates these into appropriate `AbstractRedisLockException` subtypes (e.g., `LockCommandException`, `LockConnectionException`).
    *   Populates exceptions with contextual information (`lockName`, `lockType`, `requestUuid`, etc.) for effective structured logging via `ExceptionMarkerProvider`. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
    *   Handles `CompletionException` or `ExecutionException` when unwrapping results from `CompletableFuture`s used in asynchronous Redis operations, extracting the true cause.
    *   Recognizes and categorizes idempotency-related failures.

### 1.9. `UnlockMessageListenerManager`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.messaging` (example)
*   **Type**: Spring Bean
*   **Purpose**: Manages the lifecycle of `UnlockMessageListener` instances.
*   **Key Responsibilities**:
    *   Creates and manages `UnlockMessageListener` instances, typically one listener per configured lock bucket.
    *   Ensures listeners are correctly subscribed to their respective Redis Pub/Sub channel patterns.
    *   Handles graceful shutdown of listeners (e.g., unsubscribing, shutting down internal executors) via `@PreDestroy` or `DisposableBean`.
*   **Dependencies**: `RedisConnectionFactory` (or similar from `redis-core` for Pub/Sub), `LockComponentRegistry` (to pass to listeners if they need access to e.g. `LockSemaphoreHolder` management logic).

### 1.10. `LockWatchdog`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.watchdog` (example)
*   **Type**: Spring Bean
*   **Purpose**: Background mechanism for automatic lease extension of active, application-instance-bound locks.
*   **Key Responsibilities**:
    *   Maintains an internal collection (e.g., `ConcurrentHashMap<String, LockInfo>`) of locks to monitor.
    *   Provides `registerLock(lockKey, ownerId, originalLeaseTimeMillis)` and `unregisterLock(lockKey, ownerId)` methods called by `AbstractRedisLock`.
    *   Periodically (via `@Scheduled`) iterates monitored locks and attempts to extend their lease in Redis using `watchdog_refresh_lock.lua` script executed via `RedisLockOperations`.
    *   Implements the `safetyBuffer` and "final leg" renewal logic, using `PEXPIREAT` to manage TTLs precisely while preserving the `originalLeaseTimeMillis`.
    *   Handles success/failure of extension attempts, updating internal state or removing locks from monitoring.
    *   Configured by `RedisLockProperties.WatchdogProperties`.
*   **Dependencies**: `RedisLockOperations`, `RedisLockProperties`.
*   **Detailed Flow**: See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md).

### 1.11. `LockMonitor`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.metrics` (example)
*   **Type**: Spring Bean (Optional, e.g., `@ConditionalOnProperty(name = "destilink.fw.locking.redis.health-indicator-enabled", havingValue = "true")`)
*   **Purpose**: Collects and exposes metrics related to lock operations using Micrometer.
*   **Key Responsibilities**:
    *   Registers various Micrometer meters (Timers, Counters, Gauges) for tracking lock acquisitions, hold durations, wait times, contention, watchdog activity, Pub/Sub messages, Lua script executions, and errors.
    *   Provides methods for internal components (like `AbstractRedisLock`, `LockWatchdog`) to record metric events.
*   **Dependencies**: Micrometer `MeterRegistry`.
*   **Exposed Metrics**: See [Metrics](05_OPERATIONAL_ASPECTS/metrics.md).

## 2. Key Internal Non-Bean Classes

These classes are instantiated and used internally, not directly managed as Spring beans themselves, but often configured or utilized by beans.

### 2.1. `AbstractRedisLock`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock` (example)
*   **Type**: Abstract Class
*   **Implements**: `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
*   **Purpose**: Provides foundational implementation for all Redis-based distributed locks, encapsulating common asynchronous logic and synchronous wrappers.
*   **Key Responsibilities**:
    *   Manages common fields: `lockName`, `ownerId`, injected services (via constructor, typically from `LockComponentRegistry` passed by builders).
    *   Implements the core asynchronous lock/unlock logic (`lockAsync`, `tryLockAsync`, `unlockAsync`) by dispatching tasks to a shared Virtual Thread executor.
    *   Inside the virtual thread task, it orchestrates the lock acquisition loop by:
        *   Delegating Lua script execution to `RedisLockOperations`.
        *   Registering with `UnlockMessageListenerManager` to get a `LockSemaphoreHolder` *before* the first Redis attempt.
        *   Interacting with `LockSemaphoreHolder` for managing waiting `CompletableFuture`s (for async calls) or blocking threads (for sync wrappers).
    *   Implements the synchronous `java.util.concurrent.locks.Lock` interface methods (`lock`, `tryLock`, `unlock`) as blocking wrappers around its own core asynchronous methods.
    *   Orchestrates registration/unregistration with `LockWatchdog` if conditions (`leaseTime > safetyBufferMillis`) are met, setting the correct initial TTL.
    *   Constructs Redis keys according to the defined [Redis Key Schema](redis_key_schema.md) (often delegating to a `LockBucket` instance or similar utility).
    *   The `newCondition()` method throws `UnsupportedOperationException`.
    *   Defines abstract asynchronous methods like `tryAcquireLockInternalAsync()`, `releaseLockInternalAsync()` to be implemented by concrete lock types.
*   **Dependencies**: `RedisLockOperations`, `LockOwnerSupplier`, `LockWatchdog`, `UnlockMessageListenerManager`, `RedisLockProperties`/`LockBucketConfig`.

### 2.2. Concrete Lock Implementations
    (e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`, `RedisReadLock`, `RedisWriteLock`, `RedisStampedLock`)

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.impl` (example)
*   **Type**: Concrete Classes
*   **Extends/Implements**: Typically extend `AbstractRedisLock` and implement `AsyncLock`. `RedisReadWriteLock` implements `AsyncReadWriteLock`.
*   **Purpose**: Provide specific distributed locking semantics (reentrant, state-based, read-write, stamped).
*   **Key Responsibilities**:
    *   Implement abstract methods from `AbstractRedisLock` (e.g., `tryAcquireLockInternalAsync`, `releaseLockInternalAsync`) by providing the correct Lua script names and arguments for their specific locking logic.
    *   Manage any type-specific state or logic (e.g., `RedisStateLock`'s state value, `RedisReentrantLock`'s reentrancy logic in Lua, `RedisReadWriteLock`'s coordination of read/write components).
    *   All Redis operations are performed asynchronously via `RedisLockOperations`.
*   **Detailed Behavior**: See [Lock Types Deep Dive](01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md).

### 2.3. `LockBucketConfig`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.config` (example)
*   **Type**: Non-Spring managed POJO
*   **Purpose**: Holds the *resolved* and *effective* configuration for a specific lock bucket.
*   **Key Responsibilities**:
    *   Stores properties like bucket name, resolved `leaseTime`, `retryInterval`, `stateKeyExpiration`, `lockOwnerSupplier` instance, key prefixing information, etc., for a particular bucket.
    *   Instantiated and populated by `LockBucketBuilder`, initialized with global defaults from `RedisLockProperties.Defaults` and `RedisCoreProperties`, and then potentially overridden by `LockBucketBuilder` methods.
    *   Passed along the builder chain and ultimately used to configure concrete lock instances.

### 2.4. Builder Chain (`LockBucketBuilder`, `LockConfigBuilder`, `AbstractLockTypeConfigBuilder` & Subclasses)

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.builder` (example)
*   **Type**: Non-Spring managed classes (instantiated by `LockBucketRegistry` or preceding builders).
*   **Purpose**: Provide a fluent API for programmatically configuring and creating lock instances.
*   **Key Responsibilities**:
    *   **`LockBucketBuilder`**: Configures bucket name, scope (application vs. distributed - influences key prefix from `RedisCoreProperties`), custom `LockOwnerSupplier`, and bucket-level default overrides for `leaseTime`, `retryInterval`, `stateKeyExpiration`. Modifies the `LockBucketConfig`.
    *   **`LockConfigBuilder`**: Allows selection of the lock type (reentrant, state, read-write, etc.), transitioning to a type-specific builder.
    *   **`AbstractLockTypeConfigBuilder` & Subclasses** (e.g., `ReentrantLockConfigBuilder`, `StateLockConfigBuilder`): Allow overriding instance-specific parameters (`leaseTime`, `retryInterval`) and type-specific parameters (e.g., `expectedState` for `RedisStateLock`). The `.build()` method constructs the concrete lock instance, passing the resolved configuration and `LockComponentRegistry`.
*   **Flow**: `LockBucketRegistry` -> `LockBucketBuilder` -> `LockConfigBuilder` -> Specific `LockTypeConfigBuilder` -> `build()` -> Lock Instance.

### 2.5. `UnlockMessageListener`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.messaging` (example)
*   **Type**: Non-Spring managed (Instantiated and managed by `UnlockMessageListenerManager`). Implements `org.springframework.data.redis.connection.MessageListener`.
*   **Purpose**: Listens to Redis Pub/Sub channels for unlock notifications within a specific bucket.
*   **Key Responsibilities**:
    *   Each instance is dedicated to a single lock bucket and subscribes to a pattern for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
    *   Maintains an internal map of `lockName` (e.g., `{order123}`) to `LockSemaphoreHolder` instances. This map uses `com.google.common.cache.Cache` with `weakValues()` for automatic cleanup of `LockSemaphoreHolder` instances when no longer referenced.
    *   Upon receiving a message on a subscribed channel, its `onMessage()` method is invoked.
    *   It derives the specific `lockName` from the *channel name* (not the message payload).
    *   It parses the `UnlockType` from the message payload (which *only* contains the `UnlockType` string).
    *   Based on the `UnlockType`, it intelligently signals the `Semaphore` within the corresponding `LockSemaphoreHolder` (for synchronous waits) or completes the relevant `CompletableFuture` (for asynchronous waits being managed by the `LockSemaphoreHolder`).
    *   Message processing (`onMessage`) should be executed on an optimized executor (e.g., a dedicated thread pool or virtual threads provided by the manager) to prevent blocking Redis client message dispatching threads.
*   **Detailed Flow**: See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md).

### 2.6. `LockSemaphoreHolder`

*   **Package**: `com.tui.destilink.framework.locking.redis.lock.messaging` (example)
*   **Type**: Non-Spring managed (Instantiated and managed per lock key, typically within `UnlockMessageListener`).
*   **Purpose**: Manages waiting threads (for synchronous lock wrappers) or `CompletableFuture`s (for asynchronous lock calls) for a specific lock key.
*   **Key Responsibilities**:
    *   Encapsulates a `java.util.concurrent.Semaphore` (typically fair, 0 initial permits) for synchronous `lock()` methods to block on.
    *   Manages a collection of `CompletableFuture`s associated with asynchronous `lockAsync()` or `tryLockAsync()` calls waiting for the same lock key.
    *   Tracks `waitersCount` (AtomicInteger).
    *   Provides methods like `waitForUnlock(timeout)` (for semaphore-based waits, which drains stale permits before acquiring) and mechanisms to register/complete `CompletableFuture`s.
    *   Signaled by `UnlockMessageListener` when an unlock notification is received for its associated lock key.
    *   Handles timeouts for waits, coordinating with `AbstractRedisLock` for retry logic.
```

---
**`docs/04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md`**
---
```markdown
# Redis Locking Module: Redis Key Schema

## 1. Introduction

This document outlines the consolidated Redis key schema used by the `locking-redis-lock` module. A well-defined and consistent key schema is crucial for managing lock data effectively in Redis, avoiding conflicts, and ensuring compatibility with Redis Cluster.

**Crucially, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. These key construction classes from `redis-core` are currently primarily utilized by the locking module and can be further optimized if specific needs arise or their usage expands significantly across the framework.**

For how these keys are used by Lua scripts, see [Lua Scripts](lua_scripts.md).
For configuration of prefixes, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md).

## 2. Key Structure Principles

*   **Hierarchical Naming**: Keys follow a hierarchical structure using colons (`:`) as separators for clarity and organization.
*   **Configurable Prefixing via `redis-core`**: The base prefix for all lock-related keys is derived from the `redis-core` module's configuration (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.KeyspacePrefixes`).
*   **Semantic Isolation with `<lockType>`**: A mandatory, stable, documented string identifying the lock type (e.g., `reentrant`, `stamped`, `state`) is included in the key path. This prevents accidental cross-type operations and simplifies Lua script logic.
*   **Bucket-Based Grouping**: Locks are grouped by a `bucketName` for logical organization and application of common configurations.
*   **Hash Tags for Cluster Compatibility**: The specific lock identifier (`<lockName>`) is enclosed in curly braces `{<lockName>}` to act as a hash tag. This is essential for the atomicity of multi-key Lua scripts.
*   **Separation of Lock and Data**: A primary `lockKey` is used for the lock's existence and TTL, while a secondary `lockDataKey` (a Redis Hash) stores detailed metadata.

## 3. General Key Format

The general format for most keys is:
`<effective_prefix>:<bucketName>:<namespace>:<lockType>:{<lockName>}[:<suffix>]`

### **Note on `<effective_prefix>`:**

The `<effective_prefix>` placeholder used throughout this schema refers to a composite prefix. It is formed by taking one of the configured keyspace prefixes from `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.KeyspacePrefixes` (specifically, the value of the `application` or `distributed` property) and appending the static segment `:__lock_buckets__`.
For example, if `RedisCoreProperties.keyspacePrefixes.application` is 'myApp', then `<effective_prefix>` becomes `myApp:__lock_buckets__`.

## 4. Standardized Key Schema Table

| Key Type                              | Standardized Format                                                                                                                                                                                | Hash Tag       | Example                                                                                                                                                                    | Redis Type    | Notes                                                                                                                                                                                                                                                                                                                                             |
| :------------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Main Lock Key (`lockKey`)**         | `<effective_prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`                                                                                                                                | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`                                                                                                             | String        | Holds the `ownerId`. Its existence signifies the lock is held. Its TTL is managed by `PEXPIREAT`.                                                                                                                                                                                                                                                 |
| **Lock Metadata Key (`lockDataKey`)** | `<effective_prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data`                                                                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}:data`                                                                                                        | Hash          | Stores detailed metadata. Common fields: `ownerId`, `expiresAtMillis`, `originalLeaseTimeMillis`, `count` (for reentrancy). Specific lock types may add more fields. TTL should be kept in sync with `lockKey`.                                                                                                                                   |
| State Key (for `RedisStateLock`)      | `<effective_prefix>:<bucketName>:__locks__:state:{<lockName>}:state`                                                                                                                               | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:state:{order123}:state`                                                                                                           | String        | Value is the state string. Used by `RedisStateLock`. TTL managed by `stateKeyExpiration` configuration.                                                                                                                                                                                                                                           |
| ReadWriteLock Main Data               | `<effective_prefix>:<bucketName>:__locks__:readwrite:{<lockName>}:data`                                                                                                                            | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:readwrite:{configA}:data`                                                                                                       | Hash          | Stores `mode` ('read'/'write'), a field for the writer's ID storing its reentrancy count, and fields for each reader's ID storing their reentrant counts. Its TTL is cooperatively managed by its configured lease (with watchdog) and the maximum remaining TTL of any active Individual Read Lock Timeout Keys.                                 |
| Individual Read Lock Timeout Key      | `<effective_prefix>:<bucketName>:__rwttl__:readwrite:{<lockName>}:<readerId>:<reentrantCount>`                                                                                                     | `{<lockName>}` | `myApp:__lock_buckets__:resource:__rwttl__:readwrite:{configA}:readerThreadId_uuid:1`                                                                                      | String        | Stores a placeholder ('1'). TTL set to the `leaseTime` of the specific reentrant read lock acquisition. Used by `RedisReadWriteLock`'s Lua scripts to manage individual read lock instance expirations and to correctly update the TTL of the main ReadWriteLock key.                                                                             |
| Stamped Lock Data Hash                | `<effective_prefix>:<bucketName>:__locks__:stamped:{<lockName>}:data`                                                                                                                              | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:stamped:{itemX}:data`                                                                                                               | Hash          | Stores fields like `version`, `write_owner_id`, `write_reentrancy_count`, and reader information (e.g., a `read_holders` set/count). For `RedisStampedLock`. (See [Lock Types Deep Dive](01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md#redisstampedlock)).                                                                                     |
| Unlock Channel (Pub/Sub)              | Publish To: `<effective_prefix>:<bucketName>:__unlock_channels__:<lockType>:{<lockName>}` <br/> Listener Subscribes To Pattern: `<effective_prefix>:<bucketName>:__unlock_channels__:<lockType>:*` | N/A            | Publish: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:{order123}` <br/> Subscribe Pattern: `myApp:__lock_buckets__:orders:__unlock_channels__:reentrant:*` | N/A (Channel) | Not a stored key. Locks publish to a specific channel. `UnlockMessageListener` (per bucket) subscribes to a pattern. Message payload contains _only_ the `UnlockType`. The specific lock identifier is derived by the listener from the channel name itself. (See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)). |
| Response Cache (Idempotency)          | `<effective_prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`                                                                                                             | `{<lockName>}` | `myApp:__lock_buckets__:orders:__resp_cache__:reentrant:{order123}:a1b2-c3d4-e5f6-g7h8`                                                                                    | String        | Caches Lua script responses for idempotency. TTL set by `responseCacheTtl` configuration. Key is constructed by client-side Java code (in `RedisLockOperationsImpl`) and passed to Lua scripts. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).                                                              |

## 5. Key Design Considerations

*   **Clarity and Readability**: Keys are structured to be human-readable and understandable.
*   **Semantic Isolation**: The `<lockType>` segment provides clear logical separation for different types of locks.
*   **Redis Cluster Compatibility**: The consistent use of hash tags `{<lockName>}` ensures that all keys directly pertaining to a single lock instance (main lock, data, state, idempotency cache) are co-located on the same Redis Cluster node.
*   **Key Construction Implementation**: The `LockBucket` class (or a similar dedicated component, often used internally by builders and `AbstractRedisLock`) is responsible for the correct construction of these keys based on the provided configuration (prefix, bucket name, lock type) and lock details, utilizing utilities from `redis-core`.

This consolidated key schema provides a robust foundation for the Redis locking module's data management.
```

---
**`docs/04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md`**
---
```markdown
# Redis Locking Module: Lua Scripts Documentation

## 1. Introduction

The `locking-redis-lock` module relies heavily on Lua scripts executed on the Redis server to ensure atomicity for complex lock operations. These scripts are crucial for the correctness and consistency of the distributed locks. All Lua scripts are loaded and cached by the `ScriptLoader` bean at application startup (see [Component Reference](component_reference.md#scriptloader)), and `RedisLockOperations` (see [Component Reference](component_reference.md#redislockoperations)) executes them via `ClusterCommandExecutor`.

**Key Principles**:
*   **Redis Cluster Compatibility**: All keys use a hash tag `{<lockName>}` to ensure co-location on a single node for multi-key operations. (See [Redis Key Schema](redis_key_schema.md)).
*   **Idempotency Wrapper**: Every mutating script implements an idempotency wrapper. It first checks a response cache using a `requestUuid`. If a result is found, it's returned immediately. Otherwise, the core logic is executed, and the result is cached with a `responseCacheTtl`. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Lua-Only Operations**: All modifications to lock state, TTL, or metadata are performed exclusively within these scripts.
*   **Structured Return Values**: Scripts return a structured response (a Redis array) containing a status code, `expiresAtMillis`, and `originalLeaseTimeMillis` to provide comprehensive information to the client.
*   **Redis Time**: All expiry calculations use `redis.call('TIME')` for precision.

## 2. Core Lock Operations Scripts

### 2.1. `acquire_lock.lua`

*   **Purpose**: Atomically attempts to acquire a standard or reentrant lock.
*   **KEYS**:
    1.  `lockKey`: The main lock key (e.g., `...:reentrant:{myLock}`). A Redis String.
    2.  `lockDataKey`: The lock's metadata key (e.g., `...:reentrant:{myLock}:data`). A Redis Hash.
    3.  `responseCacheKey`: For the idempotency cache.
*   **ARGV**:
    1.  `requestUuid`: For idempotency.
    2.  `relativeLeaseTimeMillis`: The user-requested lease duration in milliseconds.
    3.  `lockOwnerId`: Identifier of the client.
    4.  `responseCacheTtl`: TTL for the response cache entry in milliseconds.
*   **Logic**:
    1.  Idempotency check on `responseCacheKey`.
    2.  Get current Redis time via `redis.call('TIME')`.
    3.  Calculate `newExpiresAtMillis = (currentTime * 1000) + relativeLeaseTimeMillis`.
    4.  If `lockKey` does not exist:
        *   `SET lockKey lockOwnerId PX relativeLeaseTimeMillis`.
        *   `HMSET lockDataKey ownerId lockOwnerId expiresAtMillis newExpiresAtMillis originalLeaseTimeMillis relativeLeaseTimeMillis count 1`.
        *   `PEXPIREAT lockDataKey newExpiresAtMillis`.
        *   Result: `{-1, newExpiresAtMillis, relativeLeaseTimeMillis}` (Acquired).
    5.  If `lockKey` exists and `lockOwnerId` matches:
        *   `HINCRBY lockDataKey count 1`.
        *   `PEXPIREAT lockKey newExpiresAtMillis`.
        *   `HSET lockDataKey expiresAtMillis newExpiresAtMillis`.
        *   Result: `{-1, newExpiresAtMillis, HGET(lockDataKey, originalLeaseTimeMillis)}` (Re-acquired).
    6.  Else (lock held by another):
        *   Result: `{PTTL(lockKey), HGET(lockDataKey, expiresAtMillis), HGET(lockDataKey, originalLeaseTimeMillis)}`.
    7.  Cache the result array in `responseCacheKey` and return.
*   **Returns**: A Redis array: `{status_code, expiresAtMillis, originalLeaseTimeMillis}`. Status `< 0` if acquired, `>= 0` if held by another (value is PTTL).

### 2.2. `unlock.lua`

*   **Purpose**: Atomically releases a standard or reentrant lock.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `unlockChannel`: The specific Pub/Sub channel to publish to.
    4.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `lockOwnerId`.
    3.  `unlockTypePayload`: The string to publish (e.g., "REENTRANT_FULLY_RELEASED").
    4.  `responseCacheTtl`.
*   **Logic**:
    1.  Idempotency check.
    2.  If `lockKey` does not exist or `GET lockKey` does not match `lockOwnerId`, result is `0`.
    3.  Else (owner matches):
        *   `HINCRBY lockDataKey count -1`.
        *   If new count is `0`:
            *   `DEL lockKey lockDataKey`.
            *   `PUBLISH unlockChannel unlockTypePayload`.
            *   Result is `1`.
        *   Else (still reentrantly held), result is `0`.
    4.  Cache and return the numeric result.
*   **Returns**: `1` if the lock was fully released. `0` if not held, owner mismatch, or still reentrantly held.

### 2.3. `extend_lock.lua` (User-Initiated Extension)

*   **Purpose**: Atomically extends the lease of a lock and updates its `originalLeaseTimeMillis`.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `newRelativeLeaseTimeMillis`: The new user-requested lease duration.
    3.  `lockOwnerId`.
    4.  `responseCacheTtl`.
*   **Logic**:
    1.  Idempotency check.
    2.  If `lockKey` owner matches `lockOwnerId`:
        *   Calculate `newExpiresAtMillis`.
        *   `PEXPIREAT lockKey newExpiresAtMillis`.
        *   `HSET lockDataKey originalLeaseTimeMillis newRelativeLeaseTimeMillis`.
        *   `HSET lockDataKey expiresAtMillis newExpiresAtMillis`.
        *   `PEXPIREAT lockDataKey newExpiresAtMillis`.
        *   Result: `{1, newExpiresAtMillis, newRelativeLeaseTimeMillis}` (Success).
    3.  Else, Result: `{0, nil, nil}` (Failure).
    4.  Cache and return result.
*   **Returns**: A Redis array: `{status_code, expiresAtMillis, originalLeaseTimeMillis}`. Status `1` for success, `0` for failure.

### 2.4. `watchdog_refresh_lock.lua`

*   **Purpose**: Atomically extends the lease of a lock if the caller is the current owner. Does NOT modify `originalLeaseTimeMillis`.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `targetExpiresAtMillis`: The new absolute expiration timestamp.
    3.  `lockOwnerId`.
    4.  `responseCacheTtl`.
*   **Logic**:
    1.  Idempotency check.
    2.  If `lockKey` owner matches `lockOwnerId`:
        *   `PEXPIREAT lockKey targetExpiresAtMillis`.
        *   `HSET lockDataKey expiresAtMillis targetExpiresAtMillis`.
        *   `PEXPIREAT lockDataKey targetExpiresAtMillis`.
        *   Result: `{1, targetExpiresAtMillis, HGET(lockDataKey, originalLeaseTimeMillis)}`.
    3.  Else, Result: `{0, nil, nil}`.
    4.  Cache and return result.
*   **Returns**: A Redis array: `{status_code, newExpiresAtMillis, originalLeaseTimeMillis}`. Status `1` for success, `0` for failure.

## 3. StateLock Specific Scripts

These follow the same principles as the core scripts but add logic to check and manipulate an additional `stateKey`. All keys (`lockKey`, `lockDataKey`, `stateKey`) must share the same hash tag.

*   **`acquire_state_lock.lua`**: Similar to `acquire_lock.lua`, but first checks if the value of `stateKey` matches a provided `expectedState`.
*   **`update_state.lua`**: Atomically updates `stateKey`'s value, but only if the caller owns the associated `lockKey`.
*   **`unlock_state_lock.lua`**: Similar to `unlock.lua`, but can optionally update the `stateKey` to a new value upon successful release.

## 4. ReadWriteLock Scripts

These are more complex, managing a single data hash (`lockDataKey`) and multiple individual read lock timeout keys (`individualTimeoutKey`).

*   **`try_read_lock.lua`**:
    *   Checks `lockDataKey` mode.
    *   Fails if mode is 'write' and writer is not the current `ownerId`.
    *   If successful, sets mode to 'read', increments reader's count in the hash, and creates a new `individualTimeoutKey` with a TTL.
    *   Cooperatively updates the main `lockDataKey`'s TTL to be the max of its current TTL and the new reader's TTL.
*   **`unlock_read_lock.lua`**:
    *   Decrements reader's count.
    *   Deletes the corresponding `individualTimeoutKey`.
    *   If this was the last reader and no writer exists, deletes the main lock.
    *   Otherwise, recalculates the main lock's TTL based on the max remaining TTL of any other `individualTimeoutKey`.
*   **`try_write_lock.lua`**:
    *   Fails if mode is 'write' (and not reentrant) or if mode is 'read'.
    *   If successful, sets mode to 'write', sets writer's reentrancy count.
*   **`unlock_write_lock.lua`**:
    *   Decrements writer's reentrancy count.
    *   If fully released, checks if any readers are present in the hash. If so, transitions mode to 'read' and sets TTL based on their `individualTimeoutKey`s. If not, deletes the lock.

## 5. StampedLock Scripts (Conceptual)

These scripts manage versioned locking for `RedisStampedLock`. The `lockDataKey` hash stores fields like `version`, `write_owner_id`, `write_reentrancy_count`, `read_holders_count`.

*   **`try_stamped_lock.lua`**: Acquires a lock in 'read', 'write', or 'optimistic' mode.
*   **`unlock_stamped_lock.lua`**: Releases a lock based on its stamp.
*   **`validate_stamp.lua`**: Validates an optimistic read stamp against the current version.
*   **`convert_to_write_lock.lua`**: Atomically attempts to upgrade a read lock to a write lock.
*   **`convert_to_read_lock.lua`**: Atomically downgrades a write lock to a read lock.

This provides a comprehensive overview of the Lua scripts. Actual implementations will require careful handling of all edge cases and precise Redis commands.
```

This completes the third batch. `component_reference.md` is now quite substantial, acting as a central point for understanding the roles of various classes and beans.

**Next, I will generate the files for `05_OPERATIONAL_ASPECTS/` and `06_HISTORY_AND_MIGRATION/`.**

---
**`docs/05_OPERATIONAL_ASPECTS/performance.md`**
---
```markdown
# Redis Locking Module: Performance Considerations

## 1. Introduction

This document discusses the performance characteristics and tuning considerations for the `locking-redis-lock` module. The architecture is designed with performance in mind, particularly by minimizing direct Redis polling, leveraging atomic Lua script operations, and using Virtual Threads for client-side operations.

For metrics to monitor performance, see [Metrics](metrics.md).
For configuration options, see [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md).

## 2. Key Performance Aspects

*   **Reduced Redis Load (Non-Polling)**: The primary performance gain comes from the non-polling lock acquisition strategy.
    *   Instead of clients constantly polling Redis, they passively wait on local semaphores (for synchronous wrappers) or `CompletableFuture`s (for asynchronous calls), which are signaled by Redis Pub/Sub.
    *   Redis commands are primarily executed only during actual acquisition attempts, release operations, or lease extensions by the watchdog. This drastically reduces idle Redis traffic and server CPU load, especially under contention.
*   **Efficient Client-Side Waiting & Asynchronous Operations**:
    *   The Async-First design, coupled with a Virtual Thread executor, ensures that application platform threads are not blocked by I/O or waiting operations. This leads to significantly better resource utilization, application throughput, and responsiveness, especially in high-concurrency environments.
*   **Atomic Operations with Lua Scripts**: Using Lua scripts for complex operations like acquisition (including reentrancy checks), release, and lease extension reduces network round trips between the client and Redis. This leads to lower latency for these critical operations. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **Optimized Reentrancy Management**: Storing reentrancy count and owner information directly within a Redis Hash for reentrant locks allows for efficient, atomic updates on the server side.

## 3. Performance Tuning and Considerations

*   **Redis Network Latency**: The performance of all Redis-based operations is highly sensitive to network latency between application instances and the Redis server(s). Minimize this latency for optimal results.
*   **Redis Server Throughput**: Ensure your Redis deployment (standalone or cluster) has sufficient CPU, memory, and network throughput to handle the peak load of lock operations plus other application traffic.
*   **Lua Script Complexity**: While the provided Lua scripts are designed for efficiency, be mindful that very complex Lua logic can consume Redis CPU. The current scripts are focused and performant.
*   **Pub/Sub Overhead**: Redis Pub/Sub is generally efficient. However, an extremely high volume of lock acquisitions and releases (leading to many Pub/Sub messages) can introduce some overhead. The design of one `UnlockMessageListener` per *bucket* (which subscribes to a pattern and then internally maps to specific `LockSemaphoreHolder` instances) is a balance. For extremely high-churn buckets, this listener could become a hot spot if message processing within it is not highly optimized (e.g., using a dedicated executor for `onMessage` handling).
*   **Watchdog Configuration (`destilink.fw.locking.redis.watchdog.*`)**:
    *   `interval`: Controls how frequently the `LockWatchdog`'s central scheduled task runs. A shorter interval is more responsive but adds more background load.
    *   `factor`: This multiplier determines the `safetyBufferMillis`. A larger factor makes the watchdog more resilient to temporary network issues but may increase the average physical TTL of locks in Redis.
    *   See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) and [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md) for details.
*   **`retryInterval` (`destilink.fw.locking.redis.defaults.retry-interval`)**:
    *   This is the fallback polling interval used by a waiting Virtual Thread *after* its wait on a semaphore/future (based on the lock's current TTL or Pub/Sub signal) times out or completes, prompting a re-attempt.
    *   A shorter interval leads to faster retries if Pub/Sub messages are missed but increases Redis traffic in such scenarios.
*   **Lock Contention**: Extremely high contention for a *single specific lock key* can still lead to performance degradation as multiple clients vie for the same resource, leading to more retries and Pub/Sub signaling. Application design should aim to minimize such hotspots if possible (e.g., by using more granular locks).
*   **Redis Cluster and Hash Tags**: Correct use of hash tags in the [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md) is crucial for performance and correctness in a Redis Cluster environment. It ensures related keys are co-located on the same node, enabling multi-key Lua scripts.
*   **Thread Pool / Executor Usage**:
    *   The main lock operations run on a shared Virtual Thread executor, which is highly efficient.
    *   The `UnlockMessageListener` should process incoming Pub/Sub messages on a separate, optimized executor to avoid blocking the Redis client's core I/O threads.
    *   The `LockWatchdog` uses its own scheduled executor.
    *   Ensure these executors are appropriately sized for the expected load.
*   **`CompletableFuture` Chaining**: When using the `AsyncLock` API, efficient use of `CompletableFuture` chaining (e.g., `thenApplyAsync`, `handleAsync` with appropriate executors) in the application code can maximize the benefits of the async-first design.

## 4. Monitoring

Utilize the metrics exposed by the module via Micrometer (see [Metrics](metrics.md)) to monitor:
*   Lock acquisition times (average, max, percentiles).
*   Lock contention rates or wait times.
*   Watchdog activity (number of locks monitored, extensions performed/failed).
*   Error rates for lock operations.
*   Pub/Sub message rates.
*   Lua script execution times.

This data is essential for identifying performance bottlenecks, understanding lock usage patterns, and appropriately tuning the configuration parameters.
```

---
**`docs/05_OPERATIONAL_ASPECTS/metrics.md`**
---
```markdown
# Redis Locking Module: Metrics Documentation

## 1. Introduction

This document outlines the key metrics exposed by the `locking-redis-lock` module. These metrics provide visibility into the module's performance, usage patterns, and health, facilitating monitoring and troubleshooting. The module leverages Micrometer for metrics collection, allowing integration with various monitoring systems.

The `LockMonitor` bean (see [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockmonitor)), conditionally enabled, is responsible for registering and updating these metrics. Metric names follow Micrometer conventions and should be prefixed appropriately (e.g., `destilink.locking.redis.`).

## 2. Metric Categories and Key Metrics

### 2.1. Lock Acquisition Metrics

*   **`lock.acquisition.time` (Timer)**: Measures the duration of lock acquisition attempts (both synchronous and asynchronous).
    *   Tags: `lockName` (the specific lock identifier), `bucketName`, `lockType` (e.g., "reentrant", "state", "read", "write"), `success` (true/false).
    *   Useful for identifying slow lock acquisitions or high contention.
*   **`lock.acquisition.attempts` (Counter)**: Counts the number of lock acquisition attempts.
    *   Tags: `lockName`, `bucketName`, `lockType`, `result` (e.g., "acquired", "timeout", "interrupted", "error", "no_wait_acquired" for `tryLock()` success without waiting).
*   **`lock.wait.time` (Timer)**: Measures the duration threads/futures spend waiting for a lock to become available (i.e., time spent in `LockSemaphoreHolder` or waiting for `CompletableFuture` completion due to contention).
    *   Tags: `lockName`, `bucketName`, `lockType`.
*   **`lock.active.waiters` (Gauge)**: The current number of threads/futures actively waiting to acquire a specific lock.
    *   Tags: `lockName`, `bucketName`, `lockType`.
    *   Typically implemented by querying `LockSemaphoreHolder.getWaitersCount()` or equivalent for `CompletableFuture`s.

### 2.2. Lock Lifecycle Metrics

*   **`lock.held.duration` (Timer)**: Measures how long locks are held once acquired until they are released.
    *   Tags: `lockName`, `bucketName`, `lockType`, `ownerId`.
*   **`lock.active.count` (Gauge)**: The current number of actively held locks known to this application instance (especially those managed by the watchdog).
    *   Tags: `bucketName`, `lockType`, `scope` (application/distributed - derived from `LockOwnerSupplier`).
    *   Can be derived from `LockWatchdog`'s monitored locks or a separate registry if non-watchdog-managed locks need to be tracked locally.

### 2.3. Watchdog Metrics

(See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md) for mechanism details).

*   **`lock.watchdog.extensions` (Counter)**: Counts the number of successful lease extensions performed by the watchdog.
    *   Tags: `lockName` (if available, otherwise global or per bucket), `bucketName`.
*   **`lock.watchdog.extension.failures` (Counter)**: Counts the number of failed lease extensions.
    *   Tags: `lockName` (if available), `bucketName`, `reason` (e.g., "owner_mismatch", "redis_error", "lock_not_found").
*   **`lock.watchdog.monitored.count` (Gauge)**: The current number of locks being actively monitored by the watchdog on this application instance.

### 2.4. Pub/Sub Messaging Metrics

(See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md) for mechanism details).

*   **`lock.unlock.notifications.received` (Counter)**: Number of unlock messages received by `UnlockMessageListener` instances on this application instance.
    *   Tags: `bucketName`.
*   **`lock.unlock.notifications.published` (Counter)**: Number of unlock messages published by Lua scripts triggered by this application instance.
    *   Tags: `bucketName` (or `lockName` if granularly tracked before publishing).
*   **`lock.semaphore.signals` (Counter)**: Number of times semaphores/futures in `LockSemaphoreHolder` were signaled due to unlock messages.
    *   Tags: `lockName`, `bucketName`.

### 2.5. Lua Script Execution Metrics

(See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md) for script details).

*   **`lock.lua.script.execution.time` (Timer)**: Measures the execution time of Lua scripts on the Redis server, as observed by the client (includes network RTT).
    *   Tags: `scriptName` (e.g., "try_lock", "unlock", "extend_lock").
*   **`lock.lua.script.execution.errors` (Counter)**: Counts errors encountered during Lua script execution (e.g., script compilation errors, runtime errors within the script).
    *   Tags: `scriptName`.

### 2.6. Idempotency Cache Metrics

(See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md) for details).

*   **`lock.idempotency.cache.hits` (Counter)**: Number of times a request found a response in the idempotency cache.
    *   Tags: `lockName`, `bucketName`, `operationType` (e.g., "acquire", "release").
*   **`lock.idempotency.cache.misses` (Counter)**: Number of times a request did not find a response in the idempotency cache (leading to actual operation execution).
    *   Tags: `lockName`, `bucketName`, `operationType`.

### 2.7. Error Metrics

(See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md) for exception details).

*   **`lock.operation.errors` (Counter)**: Counts general errors encountered during lock operations, categorized by a high-level exception type or operation.
    *   Tags: `operationType` (e.g., "acquire", "release", "extend", "state_update"), `exceptionType` (e.g., "LockAcquisitionTimeoutException", "LockConnectionException", "IdempotencyViolationException").

## 3. Configuration and Exposure

*   Metrics are exposed via Micrometer, which integrates with Spring Boot Actuator.
*   Application teams can configure their Micrometer setup (e.g., in `application.yml` or programmatically) to export these metrics to their chosen monitoring system (e.g., Prometheus, Datadog, New Relic).
*   The `LockMonitor` bean, conditionally enabled by `destilink.fw.locking.redis.health-indicator-enabled` (or a more specific metrics property), is responsible for registering and updating these metrics.

## 4. Using Metrics

These metrics are vital for:
*   **Performance Tuning**: Identifying bottlenecks in lock acquisition or high contention areas.
*   **Troubleshooting**: Diagnosing issues related to lock failures, watchdog problems, messaging delays, or idempotency mechanism issues.
*   **Capacity Planning**: Understanding lock usage patterns to ensure Redis resources are adequate.
*   **Alerting**: Setting up alerts for critical issues like high error rates, excessive wait times, watchdog failures, or a high number of idempotency cache misses if unexpected.

By providing comprehensive metrics, the `locking-redis-lock` module aims to be a transparent and manageable component within the Destilink Framework.
```

---
**`docs/05_OPERATIONAL_ASPECTS/testing_strategy.md`**
---
```markdown
# Redis Locking Module: Testing Strategy

## 1. Introduction

This document outlines the comprehensive testing strategy for the `locking-redis-lock` module. A robust testing approach is crucial to ensure the correctness, reliability, and performance of this distributed locking mechanism, particularly given its interactions with Redis and concurrent access patterns.

## 2. Testing Principles

*   **Layered Testing**: Employ a combination of unit tests, integration tests, and potentially performance tests.
*   **Focus on Correctness**: Ensure core locking logic (acquisition, release, reentrancy, state management, read-write separation, stamped lock operations) functions correctly under diverse conditions.
*   **Atomicity Verification**: Specifically test scenarios relying on the atomicity of Lua scripts to prevent race conditions.
*   **Distributed Environment Simulation**: Design tests that simulate multiple clients concurrently acquiring and releasing locks.
*   **Error Handling Coverage**: Verify correct handling of various error conditions (Redis issues, command failures, invalid operations) and ensure appropriate, contextual exceptions are thrown. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
*   **Configuration Validation**: Test behavior with different valid and invalid configurations. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Async-First Testing**: Ensure asynchronous operations (`AsyncLock` API) are thoroughly tested for correctness, including `CompletableFuture` completion states (success, failure, timeout, interruption), non-blocking behavior, and correct execution on Virtual Threads with MDC propagation.
*   **Idempotency Verification**: Test that retried operations with the same `requestUuid` produce the same result or hit the response cache correctly. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).

## 3. Testing Layers

### 3.1. Unit Tests

*   **Purpose**: Test individual components and classes in isolation, focusing on their specific logic without external dependencies like a live Redis or full Spring context.
*   **Focus**:
    *   Logic within classes like `LockSemaphoreHolder` (waiter counting, semaphore signaling, future management).
    *   Configuration parsing and validation in `RedisLockProperties`.
    *   Utility classes.
    *   Parts of `AbstractRedisLock` or concrete lock implementations that involve state management or logic not directly tied to Redis commands. Mocks are used for `RedisLockOperations`, `LockComponentRegistry`, etc.
    *   `RedisLockErrorHandler` logic for mapping specific internal errors to exception types (mocking the input errors).
    *   Builder logic in the `LockBucketBuilder` chain for correct configuration propagation.
*   **Tools**: JUnit, Mockito.

### 3.2. Integration Tests

*   **Purpose**: Test the interaction between the module's components and with a real or embedded Redis instance. These tests typically require a Spring Boot test context.
*   **Focus**:
    *   **Redis Interaction & Lua Scripts**:
        *   Verify Lua scripts (see [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)) are correctly loaded and executed by `RedisLockOperations` via `ClusterCommandExecutor`.
        *   Ensure Redis keys (see [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)) are created, manipulated, and deleted as expected, including the `<lockType>` segment and `lockDataKey` hash.
    *   **End-to-End Lock Behavior (All Lock Types)**:
        *   Test lock acquisition (`lock()`, `tryLock()`, `tryLock(timeout)`, `lockAsync()`, `tryLockAsync()`), release (`unlock()`, `unlockAsync()`).
        *   Verify reentrancy for all applicable lock types.
        *   Test all specific behaviors for `RedisStateLock`, `RedisReadWriteLock`, and `RedisStampedLock`.
    *   **Concurrency**:
        *   Simulate multiple threads/processes concurrently attempting to acquire/release the same and different locks to uncover race conditions, deadlocks, or synchronization issues.
    *   **Watchdog Mechanism** (see [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)):
        *   Verify correct registration/unregistration and eligibility logic based on `safetyBufferMillis`.
        *   Test that the watchdog correctly extends leases using `PEXPIREAT`, distinguishing between standard and "final leg" renewals.
        *   Verify the `originalLeaseTimeMillis` is preserved by the watchdog.
        *   Test the `watchdog_refresh_lock.lua` script's atomicity and ownership check.
    *   **Pub/Sub Messaging & Non-Polling Wait** (see [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)):
        *   Verify unlock notifications are published correctly to the specific lock's channel with the correct `UnlockType` payload.
        *   Test that `UnlockMessageListener` receives messages and signals the correct `LockSemaphoreHolder`.
        *   **CRITICAL**: Test that the `LockSemaphoreHolder` is registered *before* the first Redis acquisition attempt to prevent race conditions.
    *   **Idempotency**:
        *   Simulate retries of mutating operations with the same `requestUuid` and verify that the operation is effectively executed once (either by hitting the response cache or by the Lua script's internal idempotency if the first attempt failed before caching).
    *   **Error Handling & `acquireTimeout` Precedence**:
        *   Simulate Redis errors and verify `RedisLockErrorHandler` translates them into correct exceptions.
        *   Test that `acquireTimeout` does not interrupt in-flight Redis commands and that the Redis operation's result takes precedence.
    *   **Configuration Scenarios**: Test module behavior with various valid configurations.
*   **Tools**:
    *   Spring Boot Test framework (`@SpringBootTest`).
    *   Testcontainers with a Redis container (ideally Redis Cluster for some scenarios) or an embedded Redis server.
    *   Concurrency utilities like `CountDownLatch`, `CyclicBarrier`, `ExecutorService` to simulate concurrent access.
    *   Awaitility for testing asynchronous operations.

### 3.3. Performance Tests (Optional but Recommended)

*   **Purpose**: Measure the module's performance characteristics under load.
*   **Focus**:
    *   Throughput (lock acquisitions/releases per second).
    *   Latency of lock operations (p50, p90, p99).
    *   Resource consumption (client-side CPU, Redis CPU/memory), especially the efficiency of Virtual Threads.
    *   Scalability with increasing numbers of clients and lock contention.
*   **Scenarios**:
    *   Low, medium, and high contention for the same lock key.
    *   Multiple clients operating on different lock keys.
    *   Long-held locks with watchdog activity.
    *   High rate of Pub/Sub messages.
*   **Metrics**: Utilize Micrometer metrics exposed by the `LockMonitor` for data collection. (See [Metrics](metrics.md)).
*   **Tools**: JMH (Java Microbenchmark Harness) for microbenchmarks, or custom load generation scripts.

## 4. Specific Testing Considerations

*   **`RedisReadWriteLock` TTL and State**: Thoroughly test the dynamic TTL management of the main R/W Hash key based on individual read lock timeout keys and writer activity.
*   **`RedisStampedLock` State Transitions**: Verify all stamp validations and lock conversion logic for correctness and atomicity.
*   **`LockSemaphoreHolder` Management**: Ensure instances are correctly created on demand per lock key and are eligible for garbage collection (e.g., via Guava Cache `weakValues()`).
*   **Redis Cluster**: If the module is intended for Redis Cluster, integration tests **must** be run against a Redis Cluster setup to verify correct behavior with hash tags and key distribution for multi-key Lua scripts.
*   **Shutdown Hooks**: Test graceful shutdown of components like `UnlockMessageListenerManager` and `LockWatchdog`.

## 5. Test Coverage

*   Aim for high code coverage (e.g., >80-85% for critical components) as measured by tools like JaCoCo.
*   Focus on testing observable behavior and contracts.
*   Ensure all public API methods of lock implementations are covered.
*   Pay special attention to edge cases, error conditions, and boundary values in configurations.

By adhering to this comprehensive testing strategy, the `locking-redis-lock` module can achieve a high degree of reliability and robustness.
```

---
**`docs/06_HISTORY_AND_MIGRATION/modernization_plan.md`**
---
```markdown
# Redis Locking Module: Modernization Plan & Assessment

## 1. Introduction

This document outlines the key modernization tasks and the preceding assessment that led to the current design of the `locking-redis-lock` module. The plan was based on a thorough analysis of previous conceptual implementations or common pitfalls in distributed locking, aiming to align the module with current Destilink Framework best practices, improve maintainability, ensure robustness, and implement a comprehensive logging and metrics strategy. This document primarily reflects the design goals achieved in the current version.

## 2. Modernization Assessment Summary (Goals Achieved)

An assessment of common challenges in distributed locking and goals for this module highlighted several key areas of focus:

*   **Inefficient Lock Acquisition (Polling)**: Avoidance of polling Redis for lock status to reduce Redis load and client resource usage.
    *   **Modernized Solution**: Achieved through a notification-based mechanism using Redis Pub/Sub and client-side semaphores/`CompletableFuture`s. (See [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md) and [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md)).
*   **Lack of Atomicity in Key Operations**: Ensuring multi-command operations are atomic to prevent race conditions.
    *   **Modernized Solution**: Achieved through the extensive utilization of Redis Lua scripts for all critical lock operations. All lock state and TTL modifications are performed exclusively via Lua. (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **Flawed Distributed Reentrancy**: Ensuring reentrancy works correctly in a distributed environment.
    *   **Modernized Solution**: Storing reentrancy count and owner information in a Redis Hash (`lockDataKey`) associated with the lock key.
*   **Limited Exception Handling**: Providing granular and context-rich exceptions.
    *   **Modernized Solution**: Implemented a comprehensive exception hierarchy with `ExceptionMarkerProvider` for structured logging, managed by `RedisLockErrorHandler`. (See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).
*   **Configuration Clarity and Flexibility**:
    *   **Modernized Solution**: Clearer structure via `RedisLockProperties` (global YAML) and programmatic builder-based configuration for buckets and instances. (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Lack of Detailed Metrics**: Insufficient visibility into runtime behavior.
    *   **Modernized Solution**: Detailed metrics exposure via Micrometer. (See [Metrics](05_OPERATIONAL_ASPECTS/metrics.md)).
*   **Async-First Design with Virtual Threads**: Prioritizing non-blocking, scalable operations.
    *   **Modernized Solution**: Core logic is asynchronous using `CompletableFuture`s and executed on a Virtual Thread executor. (See [Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md)).
*   **Idempotency**: Ensuring operations are safe to retry.
    *   **Modernized Solution**: Centralized idempotency mechanism using `requestUuid` and a response cache, managed by `RedisLockOperationsImpl` and implemented atomically within Lua scripts. (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Robust Watchdog**: A more precise and reliable lease extension mechanism.
    *   **Modernized Solution**: The watchdog is always active, uses a `safetyBuffer` for eligibility, and uses `PEXPIREAT` to manage TTLs without altering the user's intended `originalLeaseTimeMillis`. (See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)).

The assessment concluded that a comprehensive design focusing on these areas was necessary, leading to the current architecture.

## 3. Key Design Pillars (Reflecting Modernization)

The current module design embodies the following modernized approaches:

### 3.1. Spring Auto-Configuration Adherence (`RedisLockAutoConfiguration.java`)
*   No `@ComponentScan`.
*   Explicit `@Bean` definitions for all shared components, including a Virtual Thread executor.
*   Appropriate `@ConditionalOn...` annotations.
*   Registration in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.
(See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#redislockautoconfiguration)).

### 3.2. Centralized Service Access (`LockComponentRegistry`)
*   A single bean providing access to all shared services, simplifying dependency management for builders and lock instances.
(See [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md#lockcomponentregistry)).

### 3.3. Bucket-Scoped Unlock Notification (`UnlockMessageListenerManager`, `UnlockMessageListener`)
*   One `UnlockMessageListener` per configured lock bucket, subscribing to a pattern and managing `LockSemaphoreHolder` instances for specific lock keys within that bucket.
(See [Unlock Messaging Flow](03_CORE_MECHANISMS_INTERNAL/unlock_messaging_flow.md) and relevant entries in [Component Reference](04_IMPLEMENTATION_REFERENCE_INTERNAL/component_reference.md)).

### 3.4. Structured Exception Handling & Logging
*   Base `AbstractRedisLockException` implementing `ExceptionMarkerProvider` for rich, structured JSON logs.
*   `RedisLockErrorHandler` for translating underlying exceptions.
(See [Exception Handling Strategy](02_USAGE_AND_CONFIGURATION/exception_handling.md)).

### 3.5. Aligned Configuration, Watchdog, and Lua Scripts
*   Clear separation of global defaults (`RedisLockProperties.Defaults`), watchdog system settings (`RedisLockProperties.WatchdogProperties`), and programmatic overrides via builders (`LockBucketConfig`).
*   Watchdog logic (`LockWatchdog`) and Lua scripts (`watchdog_refresh_lock.lua`) operate consistently with the configured lease times and watchdog parameters.
(See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md), [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md), [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).

### 3.6. Robust and Atomic Lua Scripts
*   All critical Redis operations are encapsulated in Lua scripts ensuring atomicity, idempotency, and use of Redis Time.
*   Scripts return structured data to the client for comprehensive status reporting.
(See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).

### 3.7. Race-Condition-Aware Waiting Logic
*   `LockSemaphoreHolder` is registered with the listener manager *before* the first Redis acquisition attempt to prevent missed unlock notifications.
(See [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md)).

### 3.8. Comprehensive Logging & Metrics Strategy
*   MDC setup via `LockContextDecorator`.
*   Parameterized SLF4J logging.
*   `LockMonitor` bean for exposing key operational metrics via Micrometer.
(See [Metrics](05_OPERATIONAL_ASPECTS/metrics.md)).

### 3.9. Mandatory `redis-core` Integration
*   All Redis communication uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` via `RedisLockOperations`.
*   Key construction uses `redis-core` utilities and includes a mandatory `<lockType>` segment.
(See [Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md) and [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).

## 4. Verification
The modernized design is validated through:
*   Adherence to Destilink Framework guidelines.
*   Comprehensive testing as outlined in the [Testing Strategy](05_OPERATIONAL_ASPECTS/testing_strategy.md).

This modernization effort has resulted in a `locking-redis-lock` module that is robust, maintainable, performant, and compliant with current framework standards.
```

---
**`docs/06_HISTORY_AND_MIGRATION/migration_notes.md`**
---
```markdown
# Redis Locking Module: Migration & User Notes (v1.0)

## 1. Introduction

This document highlights key behavioral aspects and design choices of the `locking-redis-lock` module as of its **v1.0 (first official) release**. These notes serve to clarify its functionality, especially for users familiar with other locking libraries or anticipating certain common patterns from previous internal discussions or non-production drafts of a similar module.

This version represents a significant redesign focused on an Async-First approach using Virtual Threads, mandatory `redis-core` integration, Lua-script-based atomicity, and a robust, precise watchdog mechanism.

For detailed information on specific aspects, please refer to the core documentation:
*   [Architecture Overview](01_OVERVIEW_AND_CONCEPTS/architecture.md)
*   [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)
*   [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md)
*   [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)
*   [Lock Types Deep Dive](01_OVERVIEW_AND_CONCEPTS/lock_types_deep_dive.md)

## 2. Key Behavioral and Design Points for v1.0

Users adopting or working with this v1.0 module should be aware of the following:

*   **Async-First Design with Virtual Threads**:
    *   The primary API for lock operations is asynchronous, returning `CompletableFuture`s (see `AsyncLock` interface in [Class Hierarchy](01_OVERVIEW_AND_CONCEPTS/class_hierarchy.md)).
    *   All lock logic is executed on a scalable Virtual Thread executor, preventing platform thread blocking.
    *   Standard `java.util.concurrent.locks.Lock` methods (`lock()`, `tryLock()`, etc.) are provided as synchronous, blocking wrappers.
*   **Efficient Lock Acquisition (Non-Polling by Default)**:
    *   The primary mechanism for waiting for a lock is notification-based, utilizing Redis Pub/Sub.
    *   A fallback re-polling mechanism exists if notifications are missed, with the wait time based on the lock's current TTL.
    *   (See [Lock Acquisition Flow](03_CORE_MECHANISMS_INTERNAL/lock_acquisition_flow.md)).
*   **`lock()` Method Blocks Indefinitely**:
    *   The standard `lock()` method (with no arguments) will block until the lock is acquired or the calling thread is interrupted.
    *   It does **not** use or respect any internal default timeout property for its own blocking.
    *   For time-bounded acquisition attempts, applications **must** use `tryLock(long timeout, TimeUnit unit)` or the asynchronous `tryLockAsync(long timeout, TimeUnit unit)`.
*   **Robust Watchdog Mechanism**:
    *   The watchdog is always active and monitors eligible locks (those with a `leaseTime` greater than the configured `safetyBufferMillis`).
    *   It uses `PEXPIREAT` for precise TTL management and preserves the user's intended total lease duration (`originalLeaseTimeMillis`).
    *   (See [Watchdog Flow](03_CORE_MECHANISMS_INTERNAL/watchdog_flow.md)).
*   **Atomic Operations via Lua Scripts**:
    *   All critical lock operations are performed using Lua scripts executed atomically on the Redis server. Direct Redis commands for lock state/TTL modification from Java are prohibited.
    *   (See [Lua Scripts](04_IMPLEMENTATION_REFERENCE_INTERNAL/lua_scripts.md)).
*   **Programmatic Bucket Configuration**:
    *   While global default settings are loaded from YAML into `RedisLockProperties`, the configuration of logical "lock buckets" is done **programmatically** through a fluent builder API, starting with `LockBucketRegistry`.
    *   (See [Configuration](02_USAGE_AND_CONFIGURATION/configuration.md)).
*   **Centralized Idempotency**:
    *   All mutating Redis operations are designed to be idempotent using a `requestUuid` (generated by `RedisLockOperationsImpl`) and a response caching mechanism integrated into Lua scripts.
    *   (See [Idempotency Mechanism](03_CORE_MECHANISMS_INTERNAL/idempotency_mechanism.md)).
*   **Mandatory `redis-core` Usage**:
    *   All Redis communication is performed via `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`.
    *   Key construction relies on utilities from `redis-core`.
*   **Lock-Type Specific Keys**: All Redis keys include a mandatory `<lockType>` segment (e.g., `reentrant`, `state`) for semantic isolation. (See [Redis Key Schema](04_IMPLEMENTATION_REFERENCE_INTERNAL/redis_key_schema.md)).

## 3. Notes for Users Familiar with Earlier Drafts or Other Libraries

*   **No Implicit Timeout on `lock()`**: Re-emphasizing, the parameterless `lock()` method blocks indefinitely. Any prior internal drafts or expectations of a `defaultTimeout` property applying to this method are superseded by the current design; use `tryLock(long timeout, TimeUnit unit)` or its async counterpart for timed attempts.
*   **Primary API is Asynchronous**: Developers are encouraged to use the `AsyncLock` methods (`lockAsync()`, `tryLockAsync()`, etc.) for better resource utilization.
*   **Programmatic Configuration for Buckets**: Bucket-specific configurations are defined in code using the builder API, not via a map structure in the global YAML properties.
*   **`RedisStandardLock` Concept Deprecated**: Any previous discussions, internal drafts, or concepts referring to a lock type named `RedisStandardLock` should be considered deprecated. **This lock type IS NOT TO BE IMPLEMENTED.** The designated successor for general-purpose, reentrant distributed locking is `RedisReentrantLock`.
*   **Property Removals**: Several properties from earlier drafts, such as `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, and various `max*Length` properties, have been removed to simplify configuration and hardcode sensible defaults.

## 4. Compatibility & Future Migrations

*   **Source Compatibility**: This module aims to maintain source compatibility for the core `java.util.concurrent.Lock` and `java.util.concurrent.locks.ReadWriteLock` interface methods. The `AsyncLock` and `AsyncReadWriteLock` interfaces extend these.
*   **Behavioral Differences**: The non-polling nature, explicit timeout requirements for `lock()`, Async-First API, and precise watchdog mechanism are key behavioral characteristics of this module.

As this is the v1.0 release, there are no direct migration steps from a previous production version of *this specific module*. These notes aim to guide users in understanding its current design and behavior for new adoption or for those familiar with preliminary concepts.
```

This completes the generation of all the markdown files according to the new structure. I have focused on extracting, reorganizing, and ensuring semantic consistency with the original content. Redundancies have been minimized by aiming for Single Sources of Truth (e.g., `component_reference.md` for bean details, `glossary.md` for terms).

Please review these files carefully. The process of refactoring such a large amount of interconnected documentation is complex, and while I've aimed for 100% semantic consistency, a human review is always essential to catch any nuances or misinterpretations.