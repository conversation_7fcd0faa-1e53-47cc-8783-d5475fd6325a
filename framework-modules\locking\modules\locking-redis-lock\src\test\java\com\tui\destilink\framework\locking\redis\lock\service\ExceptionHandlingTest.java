package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.exception.LockException;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.assertj.core.api.Assertions.*;

/**
 * Test suite for exception handling and propagation in Redis lock operations.
 * <p>
 * Tests ensure that:
 * - Invalid parameters result in appropriate exceptions
 * - Timeout scenarios are handled correctly
 * - Exceptions are properly propagated through CompletableFuture
 * - Error messages are meaningful and actionable
 * - Retry mechanisms work correctly with exceptions
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-exceptions:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class ExceptionHandlingTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(ExceptionHandlingTest.class);

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    private String lockKey;
    private String ownerId;

    @BeforeEach
    void setUp() {
        lockKey = "test-exceptions:" + UNIQUE_ID + ":lock";
        ownerId = lockOwnerSupplier.getLockOwnerId();
    }

    @Test
    @DisplayName("Should handle null lock key gracefully")
    void shouldHandleNullLockKeyGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Null lock key should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(null, ownerId, leaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("lockKey");
    }

    @Test
    @DisplayName("Should handle null owner ID gracefully")
    void shouldHandleNullOwnerIdGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Null owner ID should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(lockKey, null, leaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("ownerId");
    }

    @Test
    @DisplayName("Should handle null lease time gracefully")
    void shouldHandleNullLeaseTimeGracefully() {
        // Null lease time should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(lockKey, ownerId, null);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("leaseTime");
    }

    @Test
    @DisplayName("Should handle empty lock key gracefully")
    void shouldHandleEmptyLockKeyGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Empty lock key should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock("", ownerId, leaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("lockKey");
    }

    @Test
    @DisplayName("Should handle empty owner ID gracefully")
    void shouldHandleEmptyOwnerIdGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Empty owner ID should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(lockKey, "", leaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("ownerId");
    }

    @Test
    @DisplayName("Should handle negative lease time gracefully")
    void shouldHandleNegativeLeaseTimeGracefully() {
        Duration negativeLeaseTime = Duration.ofSeconds(-1);
        
        // Negative lease time should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(lockKey, ownerId, negativeLeaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("leaseTime");
    }

    @Test
    @DisplayName("Should handle zero lease time gracefully")
    void shouldHandleZeroLeaseTimeGracefully() {
        Duration zeroLeaseTime = Duration.ZERO;
        
        // Zero lease time should result in exception
        CompletableFuture<Boolean> future = redisLockOperations.tryLock(lockKey, ownerId, zeroLeaseTime);
        
        assertThatThrownBy(() -> future.join())
                .isInstanceOf(CompletionException.class)
                .hasCauseInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("leaseTime");
    }

    @Test
    @DisplayName("Should handle unlock of non-existent lock gracefully")
    void shouldHandleUnlockOfNonExistentLockGracefully() {
        // Unlocking a non-existent lock should not throw exception, just return false
        CompletableFuture<Boolean> future = redisLockOperations.unlock(lockKey, ownerId);
        
        assertThat(future).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(false);
    }

    @Test
    @DisplayName("Should handle unlock with wrong owner gracefully")
    void shouldHandleUnlockWithWrongOwnerGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        String wrongOwnerId = ownerId + ":wrong";
        
        // Acquire lock with correct owner
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();
        
        // Unlock with wrong owner should return false, not throw exception
        CompletableFuture<Boolean> future = redisLockOperations.unlock(lockKey, wrongOwnerId);
        
        assertThat(future).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(false);
        
        // Original lock should still be held
        CompletableFuture<Boolean> isLocked = redisLockOperations.isLocked(lockKey);
        assertThat(isLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle extend lock of non-existent lock gracefully")
    void shouldHandleExtendLockOfNonExistentLockGracefully() {
        Duration newLeaseTime = Duration.ofSeconds(60);
        
        // Extending a non-existent lock should return false, not throw exception
        CompletableFuture<Boolean> future = redisLockOperations.extendLock(lockKey, ownerId, newLeaseTime);
        
        assertThat(future).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(false);
    }

    @Test
    @DisplayName("Should handle extend lock with wrong owner gracefully")
    void shouldHandleExtendLockWithWrongOwnerGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        Duration newLeaseTime = Duration.ofSeconds(60);
        String wrongOwnerId = ownerId + ":wrong";
        
        // Acquire lock with correct owner
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();
        
        // Extend with wrong owner should return false, not throw exception
        CompletableFuture<Boolean> future = redisLockOperations.extendLock(lockKey, wrongOwnerId, newLeaseTime);
        
        assertThat(future).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(false);
        
        // Original lock should still be held
        CompletableFuture<Boolean> isLocked = redisLockOperations.isLocked(lockKey);
        assertThat(isLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle concurrent exception scenarios")
    void shouldHandleConcurrentExceptionScenarios() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Acquire lock
        redisLockOperations.tryLock(lockKey, ownerId, leaseTime).join();
        
        // Multiple concurrent operations with invalid parameters should all fail gracefully
        CompletableFuture<Boolean> future1 = redisLockOperations.tryLock(lockKey, null, leaseTime);
        CompletableFuture<Boolean> future2 = redisLockOperations.unlock(lockKey, null);
        CompletableFuture<Boolean> future3 = redisLockOperations.extendLock(lockKey, null, leaseTime);
        
        // All should complete exceptionally
        assertThatThrownBy(() -> future1.join())
                .isInstanceOf(CompletionException.class);
        assertThatThrownBy(() -> future2.join())
                .isInstanceOf(CompletionException.class);
        assertThatThrownBy(() -> future3.join())
                .isInstanceOf(CompletionException.class);
        
        // Original lock should still be held
        CompletableFuture<Boolean> isLocked = redisLockOperations.isLocked(lockKey);
        assertThat(isLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should provide meaningful error messages")
    void shouldProvideMeaningfulErrorMessages() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Test various invalid scenarios and verify error messages are helpful
        CompletableFuture<Boolean> nullKeyFuture = redisLockOperations.tryLock(null, ownerId, leaseTime);
        CompletableFuture<Boolean> emptyKeyFuture = redisLockOperations.tryLock("", ownerId, leaseTime);
        CompletableFuture<Boolean> nullOwnerFuture = redisLockOperations.tryLock(lockKey, null, leaseTime);
        CompletableFuture<Boolean> nullLeaseFuture = redisLockOperations.tryLock(lockKey, ownerId, null);
        
        // Verify error messages contain relevant information
        assertThatThrownBy(() -> nullKeyFuture.join())
                .hasMessageContaining("lockKey")
                .hasMessageContaining("null");
        
        assertThatThrownBy(() -> emptyKeyFuture.join())
                .hasMessageContaining("lockKey")
                .hasMessageContaining("empty");
        
        assertThatThrownBy(() -> nullOwnerFuture.join())
                .hasMessageContaining("ownerId")
                .hasMessageContaining("null");
        
        assertThatThrownBy(() -> nullLeaseFuture.join())
                .hasMessageContaining("leaseTime")
                .hasMessageContaining("null");
    }
}
