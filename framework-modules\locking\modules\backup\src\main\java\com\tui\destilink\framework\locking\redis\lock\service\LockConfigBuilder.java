package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import lombok.RequiredArgsConstructor;

/**
 * Builder for configuring locks.
 * <p>
 * This class provides a fluent API for configuring locks of different types.
 * It is created by {@code LockBucketBuilder} and creates specific lock type
 * builders like {@code ReentrantLockConfigBuilder}.
 * </p>
 * <p>
 * The builder pattern implemented here allows application code to configure
 * locks with a fluent API and override default lock configuration.
 * </p>
 */
@RequiredArgsConstructor
public class LockConfigBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final LockBucketConfig bucketConfig;

    /**
     * Creates a builder for a reentrant lock with the specified name.
     * <p>
     * Reentrant locks allow the same owner to acquire the lock multiple times
     * without blocking itself.
     * </p>
     *
     * @param lockName The name of the lock
     * @return A builder for configuring a reentrant lock
     */
    public ReentrantLockConfigBuilder reentrant(String lockName) {
        return new ReentrantLockConfigBuilder(componentRegistry, bucketName, lockName, bucketConfig);
    }
}
