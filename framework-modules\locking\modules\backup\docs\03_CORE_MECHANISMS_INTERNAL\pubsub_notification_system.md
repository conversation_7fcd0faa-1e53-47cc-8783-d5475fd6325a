# Redis Locking Module: Pub/Sub Notification System

## 1. Introduction

The Pub/Sub notification system is a core mechanism that enables efficient lock acquisition by notifying waiting clients when locks become available. This system significantly reduces the need for polling and improves overall performance by providing near-instant notifications when locks are released.

This document details the implementation, message flow, and integration of the Redis Pub/Sub notification system within the locking module. For architectural context, see [Architecture Overview](../01_OVERVIEW_AND_CONCEPTS/architecture.md).

## 2. System Overview

### 2.1. Purpose and Benefits

- **Efficient Lock Acquisition**: Reduces polling overhead by providing immediate notifications
- **Improved Performance**: Faster lock acquisition times for waiting clients
- **Resource Optimization**: Lower Redis load compared to continuous polling
- **Scalability**: Supports multiple subscribers per lock key
- **Reliability**: Includes fallback mechanisms for notification failures

### 2.2. Key Components

- **Message Publisher**: Publishes unlock notifications when locks are released
- **Message Subscriber**: Listens for unlock notifications and wakes up waiting threads
- **Channel Management**: Manages Redis channels for different lock keys
- **Fallback Mechanism**: Polling fallback when notifications are unavailable

## 3. Channel Architecture

### 3.1. Channel Naming Convention

```java
public class NotificationChannelUtils {
    
    private static final String UNLOCK_CHANNEL_PREFIX = "unlock:";
    
    public static String buildUnlockChannel(String lockKey) {
        // Channel format: unlock:{keyspace-prefix}:lock:{bucket}:{lock-name}
        return UNLOCK_CHANNEL_PREFIX + lockKey;
    }
    
    public static String extractLockKeyFromChannel(String channel) {
        if (channel.startsWith(UNLOCK_CHANNEL_PREFIX)) {
            return channel.substring(UNLOCK_CHANNEL_PREFIX.length());
        }
        return null;
    }
}
```

### 3.2. Channel Structure

```
Channel Hierarchy:
unlock:destilink:lock:user-accounts:account-123
unlock:destilink:lock:inventory:product-456
unlock:destilink:lock:orders:order-789

Pattern: unlock:{redis-key-prefix}:lock:{bucket-name}:{lock-identifier}
```

### 3.3. Message Format

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnlockNotification {
    private String lockKey;
    private String previousOwnerId;
    private long timestamp;
    private String notificationType; // "UNLOCK", "EXPIRE", "DELETE"
    private Map<String, Object> metadata;
    
    public String toJson() {
        try {
            return objectMapper.writeValueAsString(this);
        } catch (Exception e) {
            // Fallback to simple format
            return previousOwnerId;
        }
    }
    
    public static UnlockNotification fromJson(String json) {
        try {
            return objectMapper.readValue(json, UnlockNotification.class);
        } catch (Exception e) {
            // Fallback: treat as simple owner ID
            return UnlockNotification.builder()
                .previousOwnerId(json)
                .timestamp(System.currentTimeMillis())
                .notificationType("UNLOCK")
                .build();
        }
    }
}
```

## 4. Publisher Implementation

### 4.1. Unlock Notification Publisher

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockUnlockPublisher {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisLockProperties properties;
    
    public void publishUnlockNotification(String lockKey, String ownerId) {
        if (!properties.isUnlockMessageListenerEnabled()) {
            log.debug("Unlock message publishing is disabled");
            return;
        }
        
        try {
            String channel = NotificationChannelUtils.buildUnlockChannel(lockKey);
            
            UnlockNotification notification = UnlockNotification.builder()
                .lockKey(lockKey)
                .previousOwnerId(ownerId)
                .timestamp(System.currentTimeMillis())
                .notificationType("UNLOCK")
                .build();
            
            String message = notification.toJson();
            
            Long subscriberCount = redisTemplate.convertAndSend(channel, message);
            
            log.debug("Published unlock notification for lock: {} to {} subscribers", 
                     lockKey, subscriberCount);
            
        } catch (Exception e) {
            log.warn("Failed to publish unlock notification for lock: {}", lockKey, e);
            // Don't throw - notification failure shouldn't break lock release
        }
    }
    
    public void publishLockExpiredNotification(String lockKey, String ownerId) {
        if (!properties.isUnlockMessageListenerEnabled()) {
            return;
        }
        
        try {
            String channel = NotificationChannelUtils.buildUnlockChannel(lockKey);
            
            UnlockNotification notification = UnlockNotification.builder()
                .lockKey(lockKey)
                .previousOwnerId(ownerId)
                .timestamp(System.currentTimeMillis())
                .notificationType("EXPIRE")
                .build();
            
            redisTemplate.convertAndSend(channel, notification.toJson());
            
        } catch (Exception e) {
            log.warn("Failed to publish lock expiration notification for lock: {}", lockKey, e);
        }
    }
}
```

### 4.2. Integration with Lock Release

```lua
-- unlock_with_notification.lua
local lockKey = KEYS[1]
local unlockChannel = KEYS[2]
local expectedOwner = ARGV[1]

-- Check current owner
local currentOwner = redis.call('GET', lockKey)
if not currentOwner then
    return {false, "LOCK_NOT_EXISTS"}
end

if currentOwner ~= expectedOwner then
    return {false, "NOT_OWNER", currentOwner}
end

-- Release lock
redis.call('DEL', lockKey)

-- Publish notification
local notification = cjson.encode({
    lockKey = lockKey,
    previousOwnerId = currentOwner,
    timestamp = redis.call('TIME')[1] * 1000,
    notificationType = "UNLOCK"
})

redis.call('PUBLISH', unlockChannel, notification)

return {true, "SUCCESS"}
```

## 5. Subscriber Implementation

### 5.1. Message Listener Configuration

```java
@Configuration
@ConditionalOnProperty(
    prefix = "destilink.fw.locking.redis", 
    name = "unlock-message-listener-enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
@RequiredArgsConstructor
public class PubSubListenerConfiguration {
    
    private final RedisConnectionFactory connectionFactory;
    private final LockUnlockMessageListener unlockMessageListener;
    
    @Bean
    @ConditionalOnMissingBean
    public RedisMessageListenerContainer redisMessageListenerContainer() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // Configure thread pool
        container.setTaskExecutor(createTaskExecutor());
        
        // Add error handler
        container.setErrorHandler(createErrorHandler());
        
        return container;
    }
    
    @Bean
    public PatternTopic unlockNotificationTopic() {
        return new PatternTopic("unlock:*");
    }
    
    @EventListener
    public void onApplicationReady(ApplicationReadyEvent event) {
        RedisMessageListenerContainer container = redisMessageListenerContainer();
        container.addMessageListener(unlockMessageListener, unlockNotificationTopic());
        log.info("Registered unlock message listener for pattern: unlock:*");
    }
    
    private TaskExecutor createTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("redis-pubsub-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    
    private ErrorHandler createErrorHandler() {
        return throwable -> log.error("Error in Redis message listener", throwable);
    }
}
```

### 5.2. Unlock Message Listener

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockUnlockMessageListener implements MessageListener {
    
    private final LockNotificationRegistry notificationRegistry;
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String channel = new String(message.getChannel());
            String messageBody = new String(message.getBody());
            
            log.debug("Received unlock notification on channel: {} with message: {}", 
                     channel, messageBody);
            
            String lockKey = NotificationChannelUtils.extractLockKeyFromChannel(channel);
            if (lockKey == null) {
                log.warn("Could not extract lock key from channel: {}", channel);
                return;
            }
            
            UnlockNotification notification = UnlockNotification.fromJson(messageBody);
            notification.setLockKey(lockKey);
            
            // Notify waiting threads
            notificationRegistry.notifyWaiters(lockKey, notification);
            
        } catch (Exception e) {
            log.error("Error processing unlock notification", e);
        }
    }
}
```

### 5.3. Notification Registry

```java
@Component
@Slf4j
public class LockNotificationRegistry {
    
    private final Map<String, Set<LockWaiter>> waitingThreads = new ConcurrentHashMap<>();
    private final ReentrantReadWriteLock registryLock = new ReentrantReadWriteLock();
    
    public void registerWaiter(String lockKey, LockWaiter waiter) {
        registryLock.writeLock().lock();
        try {
            waitingThreads.computeIfAbsent(lockKey, k -> ConcurrentHashMap.newKeySet())
                          .add(waiter);
            log.debug("Registered waiter for lock: {} (total waiters: {})", 
                     lockKey, waitingThreads.get(lockKey).size());
        } finally {
            registryLock.writeLock().unlock();
        }
    }
    
    public void unregisterWaiter(String lockKey, LockWaiter waiter) {
        registryLock.writeLock().lock();
        try {
            Set<LockWaiter> waiters = waitingThreads.get(lockKey);
            if (waiters != null) {
                waiters.remove(waiter);
                if (waiters.isEmpty()) {
                    waitingThreads.remove(lockKey);
                }
            }
        } finally {
            registryLock.writeLock().unlock();
        }
    }
    
    public void notifyWaiters(String lockKey, UnlockNotification notification) {
        registryLock.readLock().lock();
        try {
            Set<LockWaiter> waiters = waitingThreads.get(lockKey);
            if (waiters != null && !waiters.isEmpty()) {
                log.debug("Notifying {} waiters for lock: {}", waiters.size(), lockKey);
                
                // Notify all waiters (they will compete for the lock)
                waiters.forEach(waiter -> waiter.onUnlockNotification(notification));
            }
        } finally {
            registryLock.readLock().unlock();
        }
    }
    
    public int getWaiterCount(String lockKey) {
        registryLock.readLock().lock();
        try {
            Set<LockWaiter> waiters = waitingThreads.get(lockKey);
            return waiters != null ? waiters.size() : 0;
        } finally {
            registryLock.readLock().unlock();
        }
    }
}
```

## 6. Lock Waiter Implementation

### 6.1. LockWaiter Interface

```java
public interface LockWaiter {
    
    /**
     * Called when an unlock notification is received for the lock this waiter is waiting for
     */
    void onUnlockNotification(UnlockNotification notification);
    
    /**
     * Get the lock key this waiter is waiting for
     */
    String getLockKey();
    
    /**
     * Get the owner ID of this waiter
     */
    String getOwnerId();
    
    /**
     * Check if this waiter is still active (not timed out or cancelled)
     */
    boolean isActive();
}
```

### 6.2. CountDownLatch-based Waiter

```java
@RequiredArgsConstructor
@Slf4j
public class CountDownLatchWaiter implements LockWaiter {
    
    private final String lockKey;
    private final String ownerId;
    private final CountDownLatch latch;
    private final AtomicBoolean notified = new AtomicBoolean(false);
    private final AtomicBoolean active = new AtomicBoolean(true);
    
    @Override
    public void onUnlockNotification(UnlockNotification notification) {
        if (!active.get()) {
            return;
        }
        
        if (notified.compareAndSet(false, true)) {
            log.debug("Received unlock notification for lock: {} (waiter: {})", lockKey, ownerId);
            latch.countDown();
        }
    }
    
    @Override
    public String getLockKey() {
        return lockKey;
    }
    
    @Override
    public String getOwnerId() {
        return ownerId;
    }
    
    @Override
    public boolean isActive() {
        return active.get();
    }
    
    public void deactivate() {
        active.set(false);
    }
    
    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        try {
            return latch.await(timeout, unit);
        } finally {
            deactivate();
        }
    }
}
```

## 7. Integration with Lock Acquisition

### 7.1. Enhanced Lock Acquisition Flow

```java
public class RedisReentrantLock implements ReentrantLock {
    
    private final LockNotificationRegistry notificationRegistry;
    private final RedisLockProperties properties;
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        long startTime = System.nanoTime();
        long timeoutNanos = unit.toNanos(time);
        
        // First attempt - try to acquire immediately
        if (doTryLock()) {
            return true;
        }
        
        // If Pub/Sub is disabled, fall back to polling
        if (!properties.isUnlockMessageListenerEnabled()) {
            return tryLockWithPolling(timeoutNanos, startTime);
        }
        
        // Use Pub/Sub notification system
        return tryLockWithNotifications(timeoutNanos, startTime);
    }
    
    private boolean tryLockWithNotifications(long timeoutNanos, long startTime) 
            throws InterruptedException {
        
        CountDownLatch notificationLatch = new CountDownLatch(1);
        CountDownLatchWaiter waiter = new CountDownLatchWaiter(lockKey, ownerId, notificationLatch);
        
        // Register for notifications
        notificationRegistry.registerWaiter(lockKey, waiter);
        
        try {
            while (true) {
                // Check remaining time
                long elapsedNanos = System.nanoTime() - startTime;
                long remainingNanos = timeoutNanos - elapsedNanos;
                
                if (remainingNanos <= 0) {
                    return false;
                }
                
                // Try to acquire lock
                if (doTryLock()) {
                    return true;
                }
                
                // Wait for notification or timeout
                long waitTimeNanos = Math.min(remainingNanos, 
                                            properties.getDefaults().getRetryInterval().toNanos());
                
                if (notificationLatch.await(waitTimeNanos, TimeUnit.NANOSECONDS)) {
                    // Received notification, reset latch and try again
                    notificationLatch = new CountDownLatch(1);
                    waiter = new CountDownLatchWaiter(lockKey, ownerId, notificationLatch);
                    notificationRegistry.registerWaiter(lockKey, waiter);
                }
                // If no notification, continue loop (will timeout if time exceeded)
            }
            
        } finally {
            notificationRegistry.unregisterWaiter(lockKey, waiter);
            waiter.deactivate();
        }
    }
    
    private boolean tryLockWithPolling(long timeoutNanos, long startTime) 
            throws InterruptedException {
        
        Duration retryInterval = properties.getDefaults().getRetryInterval();
        
        while (true) {
            long elapsedNanos = System.nanoTime() - startTime;
            if (elapsedNanos >= timeoutNanos) {
                return false;
            }
            
            if (doTryLock()) {
                return true;
            }
            
            // Sleep for retry interval
            Thread.sleep(retryInterval.toMillis());
        }
    }
}
```

## 8. Performance Optimization

### 8.1. Connection Pooling

```java
@Configuration
public class PubSubConnectionConfiguration {
    
    @Bean
    @Qualifier("pubSubRedisConnectionFactory")
    public RedisConnectionFactory pubSubRedisConnectionFactory(
            RedisProperties redisProperties) {
        
        LettuceConnectionFactory factory = new LettuceConnectionFactory(
            new RedisStandaloneConfiguration(
                redisProperties.getHost(), 
                redisProperties.getPort()
            )
        );
        
        // Optimize for Pub/Sub
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .commandTimeout(Duration.ofSeconds(1))
            .shutdownTimeout(Duration.ofSeconds(2))
            .build();
        
        factory.setClientConfiguration(clientConfig);
        return factory;
    }
}
```

### 8.2. Message Batching

```java
@Component
@RequiredArgsConstructor
public class BatchedNotificationPublisher {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final ScheduledExecutorService batchExecutor;
    private final Queue<NotificationBatch> pendingNotifications = new ConcurrentLinkedQueue<>();
    
    @PostConstruct
    public void startBatchProcessor() {
        batchExecutor.scheduleAtFixedRate(this::processBatch, 10, 10, TimeUnit.MILLISECONDS);
    }
    
    public void queueNotification(String lockKey, String ownerId) {
        pendingNotifications.offer(new NotificationBatch(lockKey, ownerId, System.currentTimeMillis()));
    }
    
    private void processBatch() {
        List<NotificationBatch> batch = new ArrayList<>();
        NotificationBatch notification;
        
        // Collect up to 100 notifications or all available
        while (batch.size() < 100 && (notification = pendingNotifications.poll()) != null) {
            batch.add(notification);
        }
        
        if (!batch.isEmpty()) {
            publishBatch(batch);
        }
    }
    
    private void publishBatch(List<NotificationBatch> batch) {
        try {
            redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (NotificationBatch notification : batch) {
                    String channel = NotificationChannelUtils.buildUnlockChannel(notification.getLockKey());
                    connection.publish(channel.getBytes(), notification.getOwnerId().getBytes());
                }
                return null;
            });
        } catch (Exception e) {
            log.error("Failed to publish notification batch", e);
        }
    }
}
```

## 9. Monitoring and Metrics

### 9.1. Pub/Sub Metrics

```java
@Component
@RequiredArgsConstructor
public class PubSubMetrics {
    
    private final MeterRegistry meterRegistry;
    
    private final Counter notificationsPublished = Counter.builder("locking.pubsub.notifications.published")
        .description("Number of unlock notifications published")
        .register(meterRegistry);
    
    private final Counter notificationsReceived = Counter.builder("locking.pubsub.notifications.received")
        .description("Number of unlock notifications received")
        .register(meterRegistry);
    
    private final Gauge activeWaiters = Gauge.builder("locking.pubsub.active.waiters")
        .description("Number of threads waiting for lock notifications")
        .register(meterRegistry);
    
    private final Timer notificationLatency = Timer.builder("locking.pubsub.notification.latency")
        .description("Time between lock release and notification receipt")
        .register(meterRegistry);
    
    public void recordNotificationPublished() {
        notificationsPublished.increment();
    }
    
    public void recordNotificationReceived(long publishTimestamp) {
        notificationsReceived.increment();
        
        if (publishTimestamp > 0) {
            long latencyMs = System.currentTimeMillis() - publishTimestamp;
            notificationLatency.record(latencyMs, TimeUnit.MILLISECONDS);
        }
    }
    
    public void updateActiveWaiters(int count) {
        activeWaiters.set(count);
    }
}
```

### 9.2. Health Monitoring

```java
@Component
@RequiredArgsConstructor
public class PubSubHealthIndicator implements HealthIndicator {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final LockNotificationRegistry notificationRegistry;
    
    @Override
    public Health health() {
        try {
            // Test pub/sub connectivity
            String testChannel = "health-check-" + UUID.randomUUID();
            Long subscribers = redisTemplate.convertAndSend(testChannel, "ping");
            
            // Get current waiter statistics
            int totalWaiters = notificationRegistry.getTotalWaiters();
            
            return Health.up()
                .withDetail("pubsub-connectivity", "ok")
                .withDetail("test-subscribers", subscribers)
                .withDetail("active-waiters", totalWaiters)
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("pubsub-connectivity", "failed")
                .withException(e)
                .build();
        }
    }
}
```

## 10. Error Handling and Fallbacks

### 10.1. Notification Failure Handling

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class ResilientNotificationPublisher {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final CircuitBreaker circuitBreaker;
    
    public void publishWithFallback(String lockKey, String ownerId) {
        try {
            circuitBreaker.executeSupplier(() -> {
                publishUnlockNotification(lockKey, ownerId);
                return null;
            });
        } catch (Exception e) {
            log.warn("Pub/Sub notification failed for lock: {}, falling back to polling", lockKey, e);
            // Notification failure doesn't break lock release
            // Waiting threads will fall back to polling
        }
    }
    
    private void publishUnlockNotification(String lockKey, String ownerId) {
        String channel = NotificationChannelUtils.buildUnlockChannel(lockKey);
        redisTemplate.convertAndSend(channel, ownerId);
    }
}
```

### 10.2. Subscriber Resilience

```java
@Component
@RequiredArgsConstructor
public class ResilientMessageListener implements MessageListener {
    
    private final LockNotificationRegistry notificationRegistry;
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong lastErrorTime = new AtomicLong(0);
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            processMessage(message, pattern);
            // Reset error count on successful processing
            errorCount.set(0);
            
        } catch (Exception e) {
            long errors = errorCount.incrementAndGet();
            lastErrorTime.set(System.currentTimeMillis());
            
            log.error("Error processing unlock notification (error #{}):", errors, e);
            
            // If too many consecutive errors, consider circuit breaking
            if (errors > 10) {
                log.error("Too many consecutive pub/sub errors, may need intervention");
            }
        }
    }
    
    private void processMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());
        String messageBody = new String(message.getBody());
        
        String lockKey = NotificationChannelUtils.extractLockKeyFromChannel(channel);
        if (lockKey != null) {
            UnlockNotification notification = UnlockNotification.fromJson(messageBody);
            notificationRegistry.notifyWaiters(lockKey, notification);
        }
    }
    
    public long getErrorCount() {
        return errorCount.get();
    }
    
    public long getLastErrorTime() {
        return lastErrorTime.get();
    }
}
```

## 11. Configuration and Tuning

### 11.1. Pub/Sub Configuration

```yaml
destilink:
  fw:
    locking:
      redis:
        # Enable/disable pub/sub notifications
        unlock-message-listener-enabled: true
        
        # Pub/Sub specific settings
        pubsub:
          # Connection pool settings for pub/sub
          connection-pool-size: 5
          
          # Message processing thread pool
          listener-thread-pool:
            core-size: 2
            max-size: 10
            queue-capacity: 100
            
          # Batch processing settings
          batch-processing:
            enabled: true
            batch-size: 100
            batch-interval: 10ms
            
          # Circuit breaker settings
          circuit-breaker:
            failure-threshold: 5
            recovery-timeout: 30s
```

### 11.2. Performance Tuning Guidelines

1. **Connection Pooling**: Use separate connection pools for pub/sub vs regular operations
2. **Thread Pool Sizing**: Size listener thread pools based on expected notification volume
3. **Batch Processing**: Enable batching for high-throughput scenarios
4. **Circuit Breaking**: Implement circuit breakers for resilience
5. **Monitoring**: Track notification latency and delivery rates

## 12. Best Practices

### 12.1. Design Principles

1. **Non-blocking Publishers**: Never block lock release on notification publishing
2. **Graceful Degradation**: Always provide polling fallback
3. **Resource Cleanup**: Properly unregister waiters to prevent memory leaks
4. **Error Isolation**: Don't let pub/sub errors affect lock functionality
5. **Monitoring**: Track pub/sub health and performance metrics

### 12.2. Common Pitfalls

- **Memory Leaks**: Forgetting to unregister waiters
- **Blocking Operations**: Blocking lock operations on pub/sub
- **Error Propagation**: Letting pub/sub errors break lock functionality
- **Resource Exhaustion**: Not limiting waiter registrations
- **Network Partitions**: Not handling Redis connectivity issues

The Pub/Sub notification system provides significant performance benefits while maintaining the reliability and robustness required for distributed locking scenarios.