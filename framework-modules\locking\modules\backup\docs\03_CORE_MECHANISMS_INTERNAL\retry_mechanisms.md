# Redis Locking Module: Retry Mechanisms

## 1. Introduction

The retry mechanisms in the Redis locking module provide robust and intelligent strategies for handling lock acquisition failures, network issues, and temporary Redis unavailability. These mechanisms ensure high reliability while preventing resource exhaustion and maintaining system performance.

This document details the various retry strategies, their configuration, and integration with other core mechanisms like the [Pub/Sub Notification System](pubsub_notification_system.md) and [Idempotency Mechanism](idempotency_mechanism.md).

## 2. Retry Strategy Overview

### 2.1. Multi-Layered Retry Approach

The module implements a sophisticated retry system with multiple layers:

1. **Immediate Retry**: Quick retries for transient failures
2. **Notification-Based Retry**: Leverages Pub/Sub for efficient waiting
3. **Exponential Backoff**: Progressive delays for persistent failures
4. **Circuit Breaking**: Prevents cascade failures
5. **Fallback Strategies**: Alternative approaches when primary methods fail

### 2.2. Retry Triggers

Retries are triggered by various conditions:

- Lock acquisition failures (lock already held)
- Network connectivity issues
- Redis server timeouts
- Temporary Redis unavailability
- Pub/Sub notification failures

## 3. Core Retry Components

### 3.1. Retry Configuration

```java
@Data
@ConfigurationProperties(prefix = "destilink.fw.locking.redis.retry")
public class RetryProperties {
    
    /**
     * Maximum number of retry attempts for lock acquisition
     */
    private int maxAttempts = 3;
    
    /**
     * Base interval between retry attempts
     */
    private Duration baseInterval = Duration.ofMillis(100);
    
    /**
     * Maximum interval between retry attempts
     */
    private Duration maxInterval = Duration.ofSeconds(5);
    
    /**
     * Multiplier for exponential backoff
     */
    private double backoffMultiplier = 2.0;
    
    /**
     * Jitter factor to randomize retry intervals (0.0 to 1.0)
     */
    private double jitterFactor = 0.1;
    
    /**
     * Enable/disable exponential backoff
     */
    private boolean exponentialBackoffEnabled = true;
    
    /**
     * Timeout for individual Redis operations during retry
     */
    private Duration operationTimeout = Duration.ofSeconds(1);
    
    /**
     * Circuit breaker configuration
     */
    private CircuitBreakerProperties circuitBreaker = new CircuitBreakerProperties();
    
    @Data
    public static class CircuitBreakerProperties {
        private boolean enabled = true;
        private int failureThreshold = 5;
        private Duration recoveryTimeout = Duration.ofSeconds(30);
        private int minimumNumberOfCalls = 10;
    }
}
```

### 3.2. Retry Context

```java
@Data
@Builder
@AllArgsConstructor
public class RetryContext {
    private final String lockKey;
    private final String ownerId;
    private final String operation;
    private final Instant startTime;
    
    @Builder.Default
    private int attemptCount = 0;
    
    @Builder.Default
    private Duration totalElapsed = Duration.ZERO;
    
    private Throwable lastException;
    private Duration nextRetryDelay;
    private boolean shouldRetry;
    
    public void incrementAttempt() {
        this.attemptCount++;
        this.totalElapsed = Duration.between(startTime, Instant.now());
    }
    
    public boolean hasExceededMaxAttempts(int maxAttempts) {
        return attemptCount >= maxAttempts;
    }
    
    public boolean hasExceededTimeout(Duration timeout) {
        return totalElapsed.compareTo(timeout) >= 0;
    }
}
```

## 4. Retry Strategy Implementations

### 4.1. Exponential Backoff Strategy

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class ExponentialBackoffRetryStrategy implements RetryStrategy {
    
    private final RetryProperties retryProperties;
    private final Random random = new Random();
    
    @Override
    public Duration calculateNextDelay(RetryContext context) {
        if (!retryProperties.isExponentialBackoffEnabled()) {
            return retryProperties.getBaseInterval();
        }
        
        // Calculate exponential backoff: baseInterval * (multiplier ^ attempt)
        double multiplier = Math.pow(retryProperties.getBackoffMultiplier(), context.getAttemptCount());
        long baseDelayMs = retryProperties.getBaseInterval().toMillis();
        long calculatedDelayMs = (long) (baseDelayMs * multiplier);
        
        // Apply maximum interval limit
        long maxDelayMs = retryProperties.getMaxInterval().toMillis();
        long delayMs = Math.min(calculatedDelayMs, maxDelayMs);
        
        // Add jitter to prevent thundering herd
        if (retryProperties.getJitterFactor() > 0) {
            double jitterRange = delayMs * retryProperties.getJitterFactor();
            double jitter = (random.nextDouble() - 0.5) * 2 * jitterRange;
            delayMs = Math.max(0, delayMs + (long) jitter);
        }
        
        Duration delay = Duration.ofMillis(delayMs);
        log.debug("Calculated retry delay for attempt {}: {}", context.getAttemptCount(), delay);
        
        return delay;
    }
    
    @Override
    public boolean shouldRetry(RetryContext context, Throwable exception) {
        // Don't retry if max attempts exceeded
        if (context.hasExceededMaxAttempts(retryProperties.getMaxAttempts())) {
            log.debug("Max retry attempts ({}) exceeded for lock: {}", 
                     retryProperties.getMaxAttempts(), context.getLockKey());
            return false;
        }
        
        // Check if exception is retryable
        return isRetryableException(exception);
    }
    
    private boolean isRetryableException(Throwable exception) {
        // Network and connectivity issues are retryable
        if (exception instanceof RedisConnectionFailureException ||
            exception instanceof QueryTimeoutException ||
            exception instanceof RedisSystemException) {
            return true;
        }
        
        // Lock acquisition failures are retryable
        if (exception instanceof LockAcquisitionException) {
            return true;
        }
        
        // Configuration and programming errors are not retryable
        if (exception instanceof IllegalArgumentException ||
            exception instanceof IllegalStateException) {
            return false;
        }
        
        // Default to retryable for unknown exceptions
        return true;
    }
}
```

### 4.2. Notification-Aware Retry Strategy

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class NotificationAwareRetryStrategy implements RetryStrategy {
    
    private final LockNotificationRegistry notificationRegistry;
    private final RetryProperties retryProperties;
    private final ExponentialBackoffRetryStrategy fallbackStrategy;
    
    @Override
    public Duration calculateNextDelay(RetryContext context) {
        // If we're waiting for a specific lock and pub/sub is available,
        // use notification-based waiting
        if (isLockContentionFailure(context.getLastException()) && 
            notificationRegistry.isNotificationSystemAvailable()) {
            
            // Register for notifications and wait longer
            return retryProperties.getBaseInterval().multipliedBy(10);
        }
        
        // Fall back to exponential backoff for other failures
        return fallbackStrategy.calculateNextDelay(context);
    }
    
    @Override
    public boolean shouldRetry(RetryContext context, Throwable exception) {
        return fallbackStrategy.shouldRetry(context, exception);
    }
    
    private boolean isLockContentionFailure(Throwable exception) {
        return exception instanceof LockAcquisitionException &&
               exception.getMessage().contains("already held");
    }
}
```

### 4.3. Circuit Breaker Integration

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class CircuitBreakerRetryStrategy implements RetryStrategy {
    
    private final Map<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    private final RetryProperties retryProperties;
    private final NotificationAwareRetryStrategy delegateStrategy;
    
    @Override
    public Duration calculateNextDelay(RetryContext context) {
        CircuitBreaker circuitBreaker = getOrCreateCircuitBreaker(context.getLockKey());
        
        if (circuitBreaker.getState() == CircuitBreaker.State.OPEN) {
            log.warn("Circuit breaker is OPEN for lock key: {}, using extended delay", 
                    context.getLockKey());
            return retryProperties.getMaxInterval();
        }
        
        return delegateStrategy.calculateNextDelay(context);
    }
    
    @Override
    public boolean shouldRetry(RetryContext context, Throwable exception) {
        CircuitBreaker circuitBreaker = getOrCreateCircuitBreaker(context.getLockKey());
        
        // Record the failure
        circuitBreaker.recordFailure();
        
        // Don't retry if circuit breaker is open
        if (circuitBreaker.getState() == CircuitBreaker.State.OPEN) {
            log.warn("Circuit breaker is OPEN for lock key: {}, stopping retries", 
                    context.getLockKey());
            return false;
        }
        
        return delegateStrategy.shouldRetry(context, exception);
    }
    
    private CircuitBreaker getOrCreateCircuitBreaker(String lockKey) {
        return circuitBreakers.computeIfAbsent(lockKey, key -> {
            RetryProperties.CircuitBreakerProperties config = retryProperties.getCircuitBreaker();
            
            return CircuitBreaker.builder()
                .failureThreshold(config.getFailureThreshold())
                .recoveryTimeout(config.getRecoveryTimeout())
                .minimumNumberOfCalls(config.getMinimumNumberOfCalls())
                .build();
        });
    }
    
    public void recordSuccess(String lockKey) {
        CircuitBreaker circuitBreaker = circuitBreakers.get(lockKey);
        if (circuitBreaker != null) {
            circuitBreaker.recordSuccess();
        }
    }
}
```

## 5. Retry Executor

### 5.1. Main Retry Executor

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class LockRetryExecutor {
    
    private final CircuitBreakerRetryStrategy retryStrategy;
    private final RetryProperties retryProperties;
    private final MeterRegistry meterRegistry;
    
    public <T> T executeWithRetry(String lockKey, String ownerId, String operation,
                                  Duration timeout, Supplier<T> action) throws Exception {
        
        RetryContext context = RetryContext.builder()
            .lockKey(lockKey)
            .ownerId(ownerId)
            .operation(operation)
            .startTime(Instant.now())
            .build();
        
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            return doExecuteWithRetry(context, timeout, action);
        } finally {
            sample.stop(Timer.builder("locking.retry.duration")
                .tag("operation", operation)
                .tag("lock_key", lockKey)
                .register(meterRegistry));
        }
    }
    
    private <T> T doExecuteWithRetry(RetryContext context, Duration timeout, 
                                   Supplier<T> action) throws Exception {
        
        while (true) {
            context.incrementAttempt();
            
            try {
                log.debug("Executing {} attempt {} for lock: {}", 
                         context.getOperation(), context.getAttemptCount(), context.getLockKey());
                
                T result = action.get();
                
                // Record success
                retryStrategy.recordSuccess(context.getLockKey());
                recordRetryMetrics(context, true);
                
                return result;
                
            } catch (Exception e) {
                context.setLastException(e);
                
                log.debug("Attempt {} failed for lock: {} - {}", 
                         context.getAttemptCount(), context.getLockKey(), e.getMessage());
                
                // Check if we should retry
                if (!shouldContinueRetrying(context, timeout, e)) {
                    recordRetryMetrics(context, false);
                    throw e;
                }
                
                // Calculate and apply delay
                Duration delay = retryStrategy.calculateNextDelay(context);
                context.setNextRetryDelay(delay);
                
                log.debug("Retrying in {} for lock: {}", delay, context.getLockKey());
                
                try {
                    Thread.sleep(delay.toMillis());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }
    }
    
    private boolean shouldContinueRetrying(RetryContext context, Duration timeout, Exception e) {
        // Check timeout
        if (context.hasExceededTimeout(timeout)) {
            log.debug("Retry timeout exceeded for lock: {}", context.getLockKey());
            return false;
        }
        
        // Check retry strategy
        return retryStrategy.shouldRetry(context, e);
    }
    
    private void recordRetryMetrics(RetryContext context, boolean success) {
        Counter.builder("locking.retry.attempts")
            .tag("operation", context.getOperation())
            .tag("success", String.valueOf(success))
            .tag("attempts", String.valueOf(context.getAttemptCount()))
            .register(meterRegistry)
            .increment();
    }
}
```

## 6. Integration with Lock Implementations

### 6.1. Reentrant Lock Integration

```java
public class RedisReentrantLock implements ReentrantLock {
    
    private final LockRetryExecutor retryExecutor;
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        Duration timeout = Duration.of(time, unit.toChronoUnit());
        
        try {
            return retryExecutor.executeWithRetry(
                lockKey,
                ownerId,
                "ACQUIRE_LOCK",
                timeout,
                this::doTryLockOnce
            );
        } catch (LockAcquisitionException e) {
            // Expected failure case
            return false;
        } catch (InterruptedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during lock acquisition with retry", e);
            return false;
        }
    }
    
    private boolean doTryLockOnce() {
        // Actual lock acquisition logic without retry
        boolean acquired = executeLuaScript("acquire_lock.lua", /* args */);
        
        if (!acquired) {
            throw new LockAcquisitionException("Lock is already held by another owner");
        }
        
        return true;
    }
    
    @Override
    public void unlock() {
        try {
            retryExecutor.executeWithRetry(
                lockKey,
                ownerId,
                "RELEASE_LOCK",
                Duration.ofSeconds(5), // Short timeout for unlock
                () -> {
                    doUnlockOnce();
                    return null;
                }
            );
        } catch (Exception e) {
            log.error("Failed to unlock after retries: {}", lockKey, e);
            throw new LockException("Failed to release lock", e);
        }
    }
    
    private void doUnlockOnce() {
        boolean released = executeLuaScript("release_lock.lua", /* args */);
        
        if (!released) {
            throw new LockReleaseException("Failed to release lock");
        }
    }
}
```

### 6.2. State Lock Integration

```java
public class RedisStateLock implements StateLock {
    
    private final LockRetryExecutor retryExecutor;
    
    @Override
    public boolean tryLockWithState(String expectedState, String newState, 
                                  long time, TimeUnit unit) throws InterruptedException {
        Duration timeout = Duration.of(time, unit.toChronoUnit());
        
        try {
            return retryExecutor.executeWithRetry(
                lockKey,
                ownerId,
                "ACQUIRE_STATE_LOCK",
                timeout,
                () -> doTryLockWithStateOnce(expectedState, newState)
            );
        } catch (StateTransitionException e) {
            return false;
        } catch (InterruptedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during state lock acquisition with retry", e);
            return false;
        }
    }
    
    private boolean doTryLockWithStateOnce(String expectedState, String newState) {
        boolean acquired = executeLuaScript("acquire_state_lock.lua", 
                                          expectedState, newState, /* other args */);
        
        if (!acquired) {
            throw new StateTransitionException("Invalid state transition");
        }
        
        return true;
    }
}
```

## 7. Specialized Retry Scenarios

### 7.1. Network Partition Handling

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class NetworkPartitionRetryHandler {
    
    private final RetryProperties retryProperties;
    private final RedisHealthIndicator redisHealthIndicator;
    
    public Duration calculateNetworkPartitionDelay(RetryContext context) {
        // Check Redis connectivity
        Health redisHealth = redisHealthIndicator.health();
        
        if (redisHealth.getStatus() == Status.DOWN) {
            // Redis is down, use longer delays
            Duration baseDelay = retryProperties.getMaxInterval();
            
            // Progressive backoff for network issues
            int attempt = context.getAttemptCount();
            return baseDelay.multipliedBy(Math.min(attempt, 5));
        }
        
        // Redis is up, use normal retry intervals
        return retryProperties.getBaseInterval();
    }
    
    public boolean shouldRetryNetworkFailure(RetryContext context, Throwable exception) {
        // Always retry network failures, but with limits
        if (isNetworkException(exception)) {
            // Allow more attempts for network issues
            return context.getAttemptCount() < (retryProperties.getMaxAttempts() * 2);
        }
        
        return false;
    }
    
    private boolean isNetworkException(Throwable exception) {
        return exception instanceof RedisConnectionFailureException ||
               exception instanceof ConnectException ||
               exception instanceof SocketTimeoutException;
    }
}
```

### 7.2. High Contention Retry Strategy

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class HighContentionRetryStrategy {
    
    private final LockContentionMonitor contentionMonitor;
    private final RetryProperties retryProperties;
    
    public Duration calculateContentionDelay(RetryContext context) {
        String lockKey = context.getLockKey();
        double contentionLevel = contentionMonitor.getContentionLevel(lockKey);
        
        // Base delay adjusted by contention level
        Duration baseDelay = retryProperties.getBaseInterval();
        
        if (contentionLevel > 0.8) {
            // High contention: use longer delays with more jitter
            return baseDelay.multipliedBy(5).plus(
                Duration.ofMillis((long) (Math.random() * 1000))
            );
        } else if (contentionLevel > 0.5) {
            // Medium contention: moderate delays
            return baseDelay.multipliedBy(2).plus(
                Duration.ofMillis((long) (Math.random() * 500))
            );
        } else {
            // Low contention: normal delays
            return baseDelay;
        }
    }
}
```

## 8. Monitoring and Observability

### 8.1. Retry Metrics

```java
@Component
@RequiredArgsConstructor
public class RetryMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // Counters
    private final Counter totalRetries = Counter.builder("locking.retries.total")
        .description("Total number of retry attempts")
        .register(meterRegistry);
    
    private final Counter successfulRetries = Counter.builder("locking.retries.successful")
        .description("Number of successful retry operations")
        .register(meterRegistry);
    
    private final Counter failedRetries = Counter.builder("locking.retries.failed")
        .description("Number of failed retry operations")
        .register(meterRegistry);
    
    // Timers
    private final Timer retryDuration = Timer.builder("locking.retry.duration")
        .description("Duration of retry operations")
        .register(meterRegistry);
    
    // Gauges
    private final AtomicInteger activeRetries = new AtomicInteger(0);
    
    @PostConstruct
    public void initializeGauges() {
        Gauge.builder("locking.retries.active")
            .description("Number of active retry operations")
            .register(meterRegistry, activeRetries, AtomicInteger::get);
    }
    
    public void recordRetryAttempt(String operation, String lockKey) {
        totalRetries.increment(
            Tags.of(
                Tag.of("operation", operation),
                Tag.of("lock_key", lockKey)
            )
        );
    }
    
    public void recordRetrySuccess(String operation, int attempts) {
        successfulRetries.increment(
            Tags.of(
                Tag.of("operation", operation),
                Tag.of("attempts", String.valueOf(attempts))
            )
        );
    }
    
    public void recordRetryFailure(String operation, int attempts, String reason) {
        failedRetries.increment(
            Tags.of(
                Tag.of("operation", operation),
                Tag.of("attempts", String.valueOf(attempts)),
                Tag.of("failure_reason", reason)
            )
        );
    }
    
    public Timer.Sample startRetryTimer() {
        activeRetries.incrementAndGet();
        return Timer.start(meterRegistry);
    }
    
    public void stopRetryTimer(Timer.Sample sample, String operation) {
        activeRetries.decrementAndGet();
        sample.stop(retryDuration.withTags("operation", operation));
    }
}
```

### 8.2. Retry Health Indicator

```java
@Component
@RequiredArgsConstructor
public class RetryHealthIndicator implements HealthIndicator {
    
    private final RetryMetrics retryMetrics;
    private final CircuitBreakerRetryStrategy circuitBreakerStrategy;
    
    @Override
    public Health health() {
        try {
            // Check retry system health
            double successRate = calculateSuccessRate();
            int activeRetries = retryMetrics.getActiveRetries();
            Map<String, CircuitBreaker.State> circuitBreakerStates = 
                circuitBreakerStrategy.getCircuitBreakerStates();
            
            Health.Builder builder = Health.up()
                .withDetail("success-rate", String.format("%.2f%%", successRate * 100))
                .withDetail("active-retries", activeRetries);
            
            // Check circuit breaker states
            long openCircuitBreakers = circuitBreakerStates.values().stream()
                .mapToLong(state -> state == CircuitBreaker.State.OPEN ? 1 : 0)
                .sum();
            
            builder.withDetail("open-circuit-breakers", openCircuitBreakers);
            
            if (successRate < 0.8 || openCircuitBreakers > 5) {
                builder.status(Status.DOWN)
                    .withDetail("reason", "High failure rate or too many open circuit breakers");
            }
            
            return builder.build();
            
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
    
    private double calculateSuccessRate() {
        // Calculate success rate from metrics
        // Implementation depends on metrics collection approach
        return 0.95; // Placeholder
    }
}
```

## 9. Configuration Examples

### 9.1. Production Configuration

```yaml
destilink:
  fw:
    locking:
      redis:
        retry:
          # Conservative retry settings for production
          max-attempts: 5
          base-interval: PT0.1S
          max-interval: PT10S
          backoff-multiplier: 1.5
          jitter-factor: 0.2
          exponential-backoff-enabled: true
          operation-timeout: PT2S
          
          circuit-breaker:
            enabled: true
            failure-threshold: 10
            recovery-timeout: PT60S
            minimum-number-of-calls: 20
```

### 9.2. Development Configuration

```yaml
destilink:
  fw:
    locking:
      redis:
        retry:
          # Faster feedback for development
          max-attempts: 3
          base-interval: PT0.05S
          max-interval: PT2S
          backoff-multiplier: 2.0
          jitter-factor: 0.1
          exponential-backoff-enabled: true
          operation-timeout: PT1S
          
          circuit-breaker:
            enabled: false  # Disabled for easier debugging
```

## 10. Best Practices and Guidelines

### 10.1. Retry Strategy Selection

1. **Lock Contention**: Use notification-aware retry for high contention scenarios
2. **Network Issues**: Use exponential backoff with circuit breakers
3. **Redis Failures**: Implement longer delays with health checks
4. **Mixed Scenarios**: Combine strategies with appropriate fallbacks

### 10.2. Configuration Guidelines

1. **Base Interval**: Start with 100ms, adjust based on system characteristics
2. **Max Attempts**: Limit to 3-5 for most scenarios to prevent resource exhaustion
3. **Jitter**: Always include jitter (10-20%) to prevent thundering herd
4. **Circuit Breakers**: Enable for production systems with appropriate thresholds
5. **Monitoring**: Always monitor retry rates and success ratios

### 10.3. Common Pitfalls

- **Infinite Retries**: Always set maximum attempt limits
- **No Jitter**: Can cause thundering herd problems
- **Ignoring Timeouts**: Can lead to resource exhaustion
- **Poor Exception Handling**: Not distinguishing retryable vs non-retryable errors
- **Missing Monitoring**: Unable to detect and diagnose retry issues

The retry mechanisms provide the resilience needed for distributed locking while maintaining system performance and preventing resource exhaustion through intelligent backoff strategies and circuit breaking.