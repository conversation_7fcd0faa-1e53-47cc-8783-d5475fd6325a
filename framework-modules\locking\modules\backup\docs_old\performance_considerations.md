# Redis Locking Module: Final Performance Considerations

## 1. Introduction

This document discusses performance characteristics and tuning for the `locking-redis-lock` module, now leveraging **Virtual Threads**, a refined **watchdog mechanism**, and **centralized idempotency**. The architecture prioritizes minimizing platform thread blocking and direct Redis polling.

## 2. Key Performance Aspects

*   **Reduced Platform Thread Blocking (Virtual Threads)**: All lock operations (acquisition, release, waiting, watchdog tasks, listener tasks) are executed on Virtual Threads. This significantly improves scalability by preventing platform threads from blocking on I/O or delays, allowing the application to handle many more concurrent lock requests.
*   **Reduced Redis Load (Non-Polling & Efficient Watchdog)**:
    *   Primary wait is Pub/Sub. Fallback uses `min(TTL, defaults.retryInterval)` with `Thread.sleep()` on Virtual Threads.
    *   Watchdog uses `PEXPIREAT` and intelligent refresh logic (maintaining `safetyBufferMillis` or targeting `userIntendedExpireTimeMillis`), potentially reducing unnecessary writes compared to fixed TTL renewals if `safetyBufferMillis` is chosen well.
*   **Atomic Operations with <PERSON><PERSON>**: Reduces network round trips. Lua scripts now also handle `lockDataKey` updates and idempotency checks atomically.
*   **Optimized Reentrancy Management**: Stored in Redis Hashes.
*   **Centralized Idempotency**: Adds a slight overhead (one `GET` and potentially one `PSETEX` per mutating operation) for the response cache check, but provides crucial correctness for retries. `responseCacheTtl` affects cache size.

## 3. Performance Tuning and Considerations

*   **Redis Network Latency & Server Throughput**: Still paramount.
*   **Lua Script Complexity**: Scripts are slightly more complex with `lockDataKey` and idempotency logic, but remain focused.
*   **Pub/Sub Overhead**: Generally efficient. Channel names now include `lockType`.
*   **Watchdog Configuration (`watchdog.interval`, `watchdog.factor`)**:
    *   `interval`: How often the watchdog runs. Shorter means more responsive but more Redis checks.
    *   `factor`: Influences `safetyBufferMillis`. A larger factor means a larger `safetyBufferMillis`, potentially fewer renewals for very long locks but longer effective minimum TTL if watchdog kicks in.
    *   These settings, combined with `userProvidedLeaseTime`, determine watchdog monitoring eligibility and renewal frequency.
*   **`defaults.retryInterval`**: Fallback polling interval for Pub/Sub misses and retry delay for individual Redis operations in `RedisLockOperationsImpl`. `Thread.sleep()` on Virtual Threads is efficient.
*   **`defaults.maxRetries`**: For *individual Redis operations* within `RedisLockOperationsImpl`. Higher values increase resilience to transient Redis blips but can delay overall failure detection.
*   **`defaults.acquireTimeout`**: Overall limit for `tryLock`. Must be set reasonably to avoid premature timeouts while also preventing indefinite waits in applications. Consider typical Redis operation times and `retryInterval * maxRetries` for individual ops.
*   **`responseCacheTtl`**: Duration for idempotency records. Longer TTLs increase Redis memory for the cache but provide longer windows for retry idempotency.
*   **Virtual Thread Executor**: The default `Executors.newVirtualThreadPerTaskExecutor()` is generally suitable. No complex tuning usually needed.
*   **Lock Contention**: High contention on a single `lockKey` remains a bottleneck.
*   **Redis Cluster and Hash Tags**: Correct use of `{<lockName>}` in keys (which now include `lockType`) is vital.

## 4. Monitoring

Utilize Micrometer metrics:
*   Lock acquisition times (consider impact of Virtual Thread scheduling).
*   Lock contention / wait times (time spent parked by Virtual Threads).
*   Watchdog activity (locks monitored, renewals, `safetyBufferMillis` effectiveness).
*   Idempotency cache hits/misses.
*   Error rates, especially `RetryableLockException` vs. `NonRetryableLockException`.