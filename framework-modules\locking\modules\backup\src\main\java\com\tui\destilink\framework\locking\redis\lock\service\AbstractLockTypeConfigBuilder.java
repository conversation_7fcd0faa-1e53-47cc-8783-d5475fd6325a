package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

/**
 * Abstract base class for lock type-specific builders.
 * <p>
 * This class provides common configuration options for all lock types,
 * such as lease time and retry interval. It is extended by specific lock
 * type builders like {@code ReentrantLockConfigBuilder}.
 * </p>
 * <p>
 * The builder pattern implemented here allows for fluent configuration
 * of lock instances with method chaining.
 * </p>
 *
 * @param <B> The concrete builder type (for method chaining)
 * @param <L> The lock type that will be built
 */
@RequiredArgsConstructor
@Getter(AccessLevel.PROTECTED)
public abstract class AbstractLockTypeConfigBuilder<B extends AbstractLockTypeConfigBuilder<B, L>, L extends AbstractRedisLock> {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final String lockName;
    private final LockBucketConfig bucketConfig;

    private Duration instanceLeaseTime;
    private Duration instanceRetryInterval;

    /**
     * Sets the lease time for this lock instance.
     * <p>
     * The lease time determines how long the lock will be held before
     * it automatically expires if not explicitly released or renewed.
     * </p>
     *
     * @param leaseTime The lease time duration
     * @return This builder instance for method chaining
     */
    @SuppressWarnings("unchecked")
    public B withLeaseTime(Duration leaseTime) {
        this.instanceLeaseTime = leaseTime;
        return (B) this;
    }

    /**
     * Sets the retry interval for this lock instance.
     * <p>
     * The retry interval determines how long to wait between retry attempts
     * when trying to acquire a lock that is currently held by another owner.
     * </p>
     *
     * @param retryInterval The retry interval duration
     * @return This builder instance for method chaining
     */
    @SuppressWarnings("unchecked")
    public B withRetryInterval(Duration retryInterval) {
        this.instanceRetryInterval = retryInterval;
        return (B) this;
    }

    /**
     * Gets the effective lease time for this lock instance.
     * <p>
     * Returns the instance-specific lease time if set, otherwise
     * falls back to the bucket-level default.
     * </p>
     *
     * @return The effective lease time duration
     */
    protected Duration getEffectiveLeaseTime() {
        return instanceLeaseTime != null ? instanceLeaseTime : bucketConfig.leaseTime();
    }

    /**
     * Gets the effective retry interval for this lock instance.
     * <p>
     * Returns the instance-specific retry interval if set, otherwise
     * falls back to the bucket-level default.
     * </p>
     *
     * @return The effective retry interval duration
     */
    protected Duration getEffectiveRetryInterval() {
        return instanceRetryInterval != null ? instanceRetryInterval : bucketConfig.retryInterval();
    }

    /**
     * Builds and returns a lock instance with the configured settings.
     * <p>
     * This method must be implemented by concrete builder subclasses to
     * create the appropriate lock type with the configured settings.
     * </p>
     *
     * @return A new lock instance
     */
    public abstract L build();
}
