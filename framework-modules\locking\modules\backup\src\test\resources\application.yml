debug: true
spring:
  application:
    name: test-redis-lock

destilink:
  fw:
    locking:
      redis:
        enabled: true
        lease-time: PT60S
        retry-interval: PT0.1S
        state-key-expiration: PT5M
        pub-sub-wait-timeout: PT5S
        max-retries: 3
        acquire-timeout: PT10S
        response-cache-ttl: PT1M
        lock-owner-id-validation-regex: "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"
        max-lock-name-length: 255
        max-bucket-name-length: 100
        max-scope-length: 100
        watchdog:
          interval: PT5S
          max-retries: 3
          timeout: PT10S
