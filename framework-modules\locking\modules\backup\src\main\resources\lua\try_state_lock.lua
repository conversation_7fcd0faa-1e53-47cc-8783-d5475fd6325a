-- KEYS[1] lockName
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] leaseTimeMs
-- ARGV[3] lockOwnerId
-- ARGV[4] expectedState
-- ARGV[5] stateKeySuffix
-- ARGV[6] initIfNotExist (string "true" or "false")
-- ARGV[7] stateExpirationMs (string "nil" if not set)

local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    if cachedResult == 'nil' then return nil else return tonumber(cachedResult) end;
end;

local stateKey = KEYS[1] .. ARGV[5];
local currentState = redis.call('get', stateKey);

if (currentState == false) then
    if (ARGV[6] == 'true') then
        redis.call('set', stateKey, ARGV[4]); -- Set state to expectedState
        if (ARGV[7] ~= 'nil') then
            redis.call('pexpire', stateKey, ARGV[7]);
        end;
        currentState = ARGV[4];
    else
        redis.call('set', KEYS[2], -3, 'px', 300); -- Cache -3 for State Not Found
        return -3; -- State Not Found
    end;
end;

if (currentState ~= ARGV[4]) then
    redis.call('set', KEYS[2], -1, 'px', 300); -- Cache -1 for State Mismatch
    return -1; -- State Mismatch
end;

-- Proceed with lock acquisition logic (similar to try_lock.lua)
if (redis.call('exists', KEYS[1]) == 0) then
    redis.call('hset', KEYS[1], 'owner', ARGV[3]);
    redis.call('hset', KEYS[1], 'count', 1); -- Assume reentrancy for state locks
    redis.call('pexpire', KEYS[1], ARGV[2]);
    redis.call('set', KEYS[2], 'nil', 'px', 300);
    return nil;
end;

local currentLockOwner = redis.call('hget', KEYS[1], 'owner');
if (currentLockOwner == ARGV[3]) then
    redis.call('hincrby', KEYS[1], 'count', 1);
    redis.call('pexpire', KEYS[1], ARGV[2]);
    redis.call('set', KEYS[2], 'nil', 'px', 300);
    return nil;
end;

local pttl = redis.call('pttl', KEYS[1]);
redis.call('set', KEYS[2], pttl, 'px', 300);
return pttl;