# Redis Locking Module: Configuration

## 1. Introduction

This document details the configuration property hierarchy and key settings for the `locking-redis-lock` module. It aligns with the **Async-First (Virtual Threads)** approach, **centralized idempotency**, **refined watchdog**, and **lock-type specific key schemas**.

Configuration principles:
- **Strict Guideline Adherence**.
- **Clear Override Precedence**: Instance-specific (builders) > Programmatic Bucket (builders) > Global YAML (`RedisLockProperties.Defaults`).
- **Separation of Concerns**: System-level (`RedisLockProperties.WatchdogProperties`) vs. overridable lock defaults (`RedisLockProperties.Defaults`).
- **Mandatory Unlock Notifications** (Redis Pub/Sub).
- **Leveraging `redis-core` Properties**.
- **Redis Key Construction**: Via `redis-core` utilities, with mandatory lock-type segments.
- **Lua-Only Redis Operations**: All lock state/TTL/metadata changes via Lua.
- **Idempotency**: All mutating operations are idempotent (`requestUuid`, `responseCacheTtl`).

## 2. Configuration Hierarchy and Flow

Global defaults are loaded from YAML into `RedisLockProperties`. This class contains two nested static classes: `RedisLockProperties.WatchdogProperties` (for system-level watchdog settings like `interval`, `factor`) and `RedisLockProperties.Defaults` (for overridable lock implementation defaults like `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`).

`LockBucketRegistry` uses `RedisLockProperties.Defaults` and `RedisCoreProperties` to initialize a `LockBucketConfig`. Builders allow programmatic overrides.

```mermaid
graph TD
    subgraph YAML_Configuration [\"YAML Configuration (`1000-locking-redis-lock.application.yml`)\"]
        direction LR
        A_WatchdogYAML[\"Watchdog Settings<br>(destilink.fw.locking.redis.watchdog.*)\"]
        B_DefaultsYAML[\"Lock Implementation Defaults<br>(destilink.fw.locking.redis.defaults.*)\"]
        C_GlobalRedisCoreYAML[\"Global Redis Core Settings<br>(destilink.fw.redis.core.*)\"]
        D_TopLevelYAML[\"Top-Level Locking Settings<br>(destilink.fw.locking.redis.*)\"]
    end

    subgraph Java_Configuration_Classes [\"Java Configuration Classes\"]
        direction LR
        E_RedisLockProps[\"<code>RedisLockProperties.java</code><br>(@ConfigurationProperties)<br>Contains WatchdogProperties, Defaults, & top-level\"]
        F_WatchdogProps[\"<code>RedisLockProperties.WatchdogProperties</code><br>(Holds interval, factor, etc.)\"]
        G_DefaultsProps[\"<code>RedisLockProperties.Defaults</code><br>(Holds leaseTime, retryInterval, maxRetries, acquireTimeout)\"]
        H_RedisCoreProps[\"<code>RedisCoreProperties.java</code><br>(from redis-core)\"]
        I_LockBucketConfig[\"<code>LockBucketConfig.java</code><br>(Resolved bucket settings)\"]
    end

    subgraph Builder_Chain [\"Builder Chain (Programmatic Configuration)\"]
        direction LR
        J_LockBucketRegistry[\"<code>LockBucketRegistry</code>\"]
        K_LockBucketBuilder[\"<code>LockBucketBuilder</code><br>(Bucket defaults override global Defaults)\"]
        L_LockConfigBuilder[\"<code>LockConfigBuilder</code>\"]
        M_AbstractLockTypeConfigBuilder[\"<code>AbstractLockTypeConfigBuilder</code><br>(Instance overrides bucket/global Defaults)\"]
    end

    subgraph Lock_Instance_And_Watchdog [\"Lock Instance & Watchdog Service\"]
        N_LockInstance[\"Actual Lock Instance<br>(Operates with resolved Defaults, uses Virtual Threads)\"]
        O_LockWatchdog[\"<code>LockWatchdog</code> Service<br>(Always active, configured by WatchdogProperties, uses Virtual Threads)\"]
    end

    A_WatchdogYAML -- \"populates\" --> F_WatchdogProps;
    B_DefaultsYAML -- \"populates\" --> G_DefaultsProps;
    C_GlobalRedisCoreYAML -- \"populates\" --> H_RedisCoreProps;
    D_TopLevelYAML -- \"populates top-level fields in\" --> E_RedisLockProps;
    
    E_RedisLockProps -- \"contains\" --> F_WatchdogProps;
    E_RedisLockProps -- \"contains\" --> G_DefaultsProps;

    J_LockBucketRegistry -- \"uses global defaults from\" --> G_DefaultsProps;
    J_LockBucketRegistry -- \"uses\" --> H_RedisCoreProps;
    J_LockBucketRegistry -- \"initializes `LockBucketConfig` for\" --> K_LockBucketBuilder;
    
    K_LockBucketBuilder -- \"Can override bucket-level defaults in its\" --> I_LockBucketConfig;
    K_LockBucketBuilder -- \"Passes config to\" --> L_LockConfigBuilder;
    L_LockConfigBuilder -- \"Passes config to\" --> M_AbstractLockTypeConfigBuilder;
    M_AbstractLockTypeConfigBuilder -- \".build() creates\" --> N_LockInstance;

    M_AbstractLockTypeConfigBuilder -- \"Applies instance overrides over\" --> I_LockBucketConfig;
    N_LockInstance -- \"Receives final effective configuration from\" --> M_AbstractLockTypeConfigBuilder;
    O_LockWatchdog -- \"is configured by\" --> F_WatchdogProps;
```

## 3. Watchdog Activation Logic

The `LockWatchdog` service is **always active** when the `locking-redis-lock` module is enabled. It monitors locks that meet specific conditions:
1.  **Application Instance Binding**: `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returns `true`.
2.  **Sufficient Lease Time**: The `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) for the lock instance must be greater than `safetyBufferMillis` (calculated as `watchdog.interval * watchdog.factor`).

If met, the lock is registered with the watchdog. The watchdog sets the initial lock TTL to `safetyBufferMillis` and then maintains it, respecting the `originalLeaseTimeMillis` for the final expiry.

## 4. Key Configuration Properties

### 4.1. Top-Level Properties (`destilink.fw.locking.redis.*`)

Defined directly in `RedisLockProperties`.

| Property (YAML Key)               | Java Field (`RedisLockProperties`) | Description                                                                                                                             | Default (Java) | Notes                                                    |
| :-------------------------------- | :--------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :------------------------------------------------------- |
| `enabled`                         | `enabled`                          | Globally enables or disables the entire Redis locking feature.                                                                          | `true`         | Master switch.                                           |
| `state-key-expiration`            | `stateKeyExpiration`               | TTL for auxiliary state keys in Redis (e.g., for `StateLock`). Can be overridden at bucket level.                                       | `PT5M`         | Ensures cleanup of auxiliary state keys.                 |
| `response-cache-ttl`              | `responseCacheTtl`                 | TTL for idempotency response cache records in Redis (keyed by `requestUuid`). **Critical for idempotency of all mutating operations.**  | `PT5M`         | Must be long enough for client retries.                  |
| `prefix`                          | `prefix`                           | **DEPRECATED & REMOVED.** Global prefix is derived from `redis-core`.                                                                   | N/A            | Use `destilink.fw.redis.core.keyspace-prefixes.locking`. |
| `unlock-message-listener-enabled` | `unlockMessageListenerEnabled`     | **REMOVED.** Pub/Sub listener is now a mandatory core component and always enabled if the module is active.                             | N/A            | -                                                        |
| `retry-executor-supplier`         | `retryExecutorSupplier`            | `Supplier<ScheduledExecutorService>` for internal delayed tasks (e.g., `retryInterval` waits). Not a YAML property. Internal mechanism. | N/A            | Uses Virtual Threads for its tasks.                      |

### 4.2. System-Level Watchdog Configuration (`destilink.fw.locking.redis.watchdog.*`)

Defined in `RedisLockProperties.WatchdogProperties`. Not overridable per lock.

| Property (YAML Key)          | Java Field (`WatchdogProperties`) | Description                                                                                           | Default (Java)      | Notes                                                                                                                           |
| :--------------------------- | :-------------------------------- | :---------------------------------------------------------------------------------------------------- | :------------------ | :------------------------------------------------------------------------------------------------------------------------------ |
| `interval`                   | `interval`                        | Interval at which the watchdog checks and attempts to extend leases for monitored locks.              | `PT5S`              | Independent duration.                                                                                                           |
| `factor`                     | `factor`                          | Multiplier for `interval` to calculate `safetyBufferMillis`.                                          | `3.0`               | `safetyBufferMillis = interval * factor`. Locks with `userLeaseTime > safetyBufferMillis` are monitored.                        |
| `core-pool-size`             | `corePoolSize`                    | Core number of threads for the watchdog's `ScheduledExecutorService` (uses Virtual Threads per task). | `2`                 | Adjust based on expected number of monitored locks if platform threads were used; with VTs, this is less critical for blocking. |
| `thread-name-prefix`         | `threadNamePrefix`                | Prefix for watchdog executor threads.                                                                 | `dl-lock-watchdog-` | Observability.                                                                                                                  |
| `shutdown-await-termination` | `shutdownAwaitTermination`        | Maximum time to wait for the watchdog executor to terminate during application shutdown.              | `PT30S`             | Graceful shutdown.                                                                                                              |

### 4.3. Lock Implementation Defaults (`destilink.fw.locking.redis.defaults.*`)

Defined in `RedisLockProperties.Defaults`. These are global defaults that can be overridden by lock builders.

| Property (YAML Key) | Java Field (`Defaults`) | Description                                                                                                                                                                 | Default (Java)     | Programmatic Bucket Override (Builder Method)           | Programmatic Instance Override (Builder Method)              | Notes                                                                                                                                                                                                                |
| :------------------ | :---------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- | :------------------------------------------------------ | :----------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `lease-time`        | `leaseTime`             | Default `relativeLeaseTimeMillis` a lock should be held.                                                                                                                    | `PT60S`            | `LockBucketBuilder.withDefaultLeaseTime(Duration)`      | `AbstractLockTypeConfigBuilder.withLeaseTime(Duration)`      | This is the user's intended lease. Watchdog uses this to calculate `userIntendedExpireTimeMillis`.                                                                                                                   |
| `retry-interval`    | `retryInterval`         | Default wait time between retry attempts for *individual Redis operations* within `RedisLockOperationsImpl` if a transient error occurs. Also used as fallback for Pub/Sub. | `PT0.1S` (`100ms`) | `LockBucketBuilder.withDefaultRetryInterval(Duration)`  | `AbstractLockTypeConfigBuilder.withRetryInterval(Duration)`  | `Thread.sleep()` on Virtual Threads.                                                                                                                                                                                 |
| `max-retries`       | `maxRetries`            | Default maximum number of retry attempts for *individual failing Redis operations* within `RedisLockOperationsImpl`.                                                        | `3`                | `LockBucketBuilder.withDefaultMaxRetries(int)`          | `AbstractLockTypeConfigBuilder.withMaxRetries(int)`          | Does NOT limit attempts to acquire a busy lock.                                                                                                                                                                      |
| `acquire-timeout`   | `acquireTimeout`        | Default overall timeout for a lock acquisition operation (e.g., for `tryLock(timeout, unit)`). Approximate limit.                                                           | `PT5S`             | `LockBucketBuilder.withDefaultAcquireTimeout(Duration)` | `AbstractLockTypeConfigBuilder.withAcquireTimeout(Duration)` | Does not interrupt in-flight Redis commands; their result takes precedence. `LockAcquisitionTimeoutException` only if deadline passed AND last Redis op (if any) didn't acquire lock AND didn't throw a Redis error. |

**Removed Properties from `RedisLockProperties.Defaults`**:
*   `fairLockBehavior`: Fairness is complex in distributed systems and not directly configurable this way.
*   `asyncExecutorName`: Replaced by a dedicated Virtual Thread executor.
*   `redisOperationTimeout`: Timeout for individual Redis operations is now implicitly handled by Lettuce/`ClusterCommandExecutor`'s lower-level settings, and `RedisLockOperationsImpl` retries transient failures. The overall control is `acquireTimeout`.

## 5. Java Configuration Classes

- **`RedisLockProperties.java`**: `@ConfigurationProperties(prefix = "destilink.fw.locking.redis")`. Contains top-level settings and nested `WatchdogProperties` and `Defaults` static classes.
- **`LockBucketConfig.java`**: Non-Spring managed, configured by `LockBucketBuilder`, holds resolved settings for a bucket.

endpoint:http://kyverno-svc-metrics.kyverno:8000/metrics
endpoint:http://kyverno-reports-controller-metrics.kyverno:8000/metr



atlas-registry-source-tui-add-pod-imagepullsecrets

go.opentelpentelemetry.io/contrib/instrumentation/net/http/otelhttp