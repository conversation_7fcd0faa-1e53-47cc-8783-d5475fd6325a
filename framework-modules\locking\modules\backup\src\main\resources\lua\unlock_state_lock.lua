-- KEYS[1] lockName
-- KEYS[2] unlockChannelBaseName
-- KEYS[3] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] lockOwnerId
-- ARGV[3] newStateOnUnlock
-- ARGV[4] stateKeySuffix
-- ARGV[5] stateExpirationMs (string "nil" if not set)
-- ARGV[6] uuidCacheTtlSeconds (TTL for response cache)

local cachedResult = redis.call('get', KEYS[3]);
if cachedResult ~= false then
    return tonumber(cachedResult);
end;

local lockNameSuffix = string.match(KEYS[1], "{([^}]+)}$"); -- Extract {lockName} part
local unlockChannel = KEYS[2] .. ':' .. lockNameSuffix;

local result = 0;

local currentOwner = redis.call('hget', KEYS[1], 'owner');
local count = tonumber(redis.call('hget', KEYS[1], 'count'));

if (currentOwner == false or currentOwner ~= ARGV[2]) then
    redis.call('set', KEYS[3], 0, 'px', ARGV[6]); -- Cache 0 for not owned
    return 0; -- Not held by this owner
end;

if (count == nil or count <= 1) then
    -- Full release
    redis.call('del', KEYS[1]); -- Delete main lock hash

    local stateKey = KEYS[1] .. ARGV[4];
    local oldState = redis.call('get', stateKey); -- Get old state before updating

    redis.call('set', stateKey, ARGV[3]);
    if (ARGV[5] ~= 'nil') then
        redis.call('pexpire', stateKey, ARGV[5]);
    end;

    local unlockType = 'STATE_LOCK_RELEASED_STATE_UNCHANGED';
    if (oldState ~= ARGV[3]) then
        unlockType = 'STATE_LOCK_RELEASED_STATE_UPDATED';
    end;

    redis.call('publish', unlockChannel, unlockType);
    result = 1;
else
    -- Partial release (reentrancy)
    redis.call('hincrby', KEYS[1], 'count', -1);
    result = 0;
end;

redis.call('set', KEYS[3], result, 'px', ARGV[6]);
return result;