# Redis Locking Module: Architecture Overview

## 1. Introduction

This document provides a comprehensive architectural overview of the `locking-redis-lock` module within the Destilink Framework. The module implements distributed locking mechanisms using Redis as the coordination backend, designed with an Async-First approach and strict adherence to framework guidelines.

## 2. Core Design Principles

*   **Async-First Operations**: All core lock operations are fundamentally asynchronous, returning `CompletableFuture`s. Synchronous methods are provided as blocking wrappers for compatibility with `java.util.concurrent.locks.Lock`.
*   **Non-Polling Lock Acquisition**: Primary reliance on Redis Pub/Sub for unlock notifications to minimize Redis load and improve efficiency.
*   **Atomic Operations**: All critical Redis operations are performed using Lua scripts to ensure atomicity and prevent race conditions.
*   **Mandatory `redis-core` Integration**: All Redis communication occurs exclusively through `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`.
*   **Structured Exception Handling**: Comprehensive exception hierarchy with `ExceptionMarkerProvider` integration for structured logging.
*   **Comprehensive Metrics**: Detailed operational metrics via Micrometer for monitoring and troubleshooting.

## 3. High-Level Architecture

```mermaid
graph TB
    subgraph Client_Application [Client Application]
        A[Application Code] --> B[Lock Instance<br/>(e.g., RedisReentrantLock)]
        B --> C[AbstractRedisLock<br/>(Base Implementation)]
    end

    subgraph Framework_Components [Framework Components]
        D[LockBucketRegistry<br/>(Entry Point)]
        E[LockComponentRegistry<br/>(Service Holder)]
        F[RedisLockOperations<br/>(Redis Abstraction)]
        G[ScriptLoader<br/>(Lua Scripts)]
        H[LockWatchdog<br/>(Lease Extension)]
        I[UnlockMessageListener<br/>(Pub/Sub Handler)]
        J[LockSemaphoreHolder<br/>(Wait Management)]
    end

    subgraph Redis_Infrastructure [Redis Infrastructure]
        K[(Redis Server/Cluster)]
        L[Lua Scripts<br/>(Atomic Operations)]
        M[Pub/Sub Channels<br/>(Unlock Notifications)]
    end

    subgraph Configuration [Configuration]
        N[RedisLockProperties<br/>(YAML Config)]
        O[LockBucketConfig<br/>(Runtime Config)]
    end

    A --> D
    D --> E
    C --> E
    E --> F
    E --> H
    E --> I
    F --> G
    F --> K
    G --> L
    H --> F
    I --> J
    I --> M
    K --> M
    N --> E
    O --> C

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#fff3e0
    style K fill:#ffebee
    style L fill:#ffebee
    style M fill:#ffebee
    style N fill:#f3e5f5
    style O fill:#f3e5f5
```

## 4. Component Layers

### 4.1. Client Interface Layer
*   **Lock Instances**: Concrete implementations (`RedisReentrantLock`, `RedisStateLock`, etc.)
*   **AbstractRedisLock**: Base class providing common functionality and synchronous wrappers
*   **AsyncLock Interface**: Primary asynchronous API

### 4.2. Framework Service Layer
*   **LockBucketRegistry**: Entry point for creating lock builders
*   **LockComponentRegistry**: Centralized access to shared services
*   **RedisLockOperations**: Abstraction for Redis communication via `redis-core`
*   **ScriptLoader**: Manages Lua script loading and caching

### 4.3. Operational Components
*   **LockWatchdog**: Automatic lease extension for long-running locks
*   **UnlockMessageListener**: Handles Redis Pub/Sub unlock notifications
*   **LockSemaphoreHolder**: Manages waiting threads/futures per lock key

### 4.4. Configuration Layer
*   **RedisLockProperties**: Global YAML-based configuration
*   **LockBucketConfig**: Runtime configuration for specific lock buckets

## 5. Key Architectural Patterns

### 5.1. Builder Pattern
Fluent API for lock configuration:
```
LockBucketRegistry → LockBucketBuilder → LockConfigBuilder → SpecificLockBuilder → Lock Instance
```

### 5.2. Registry Pattern
*   `LockBucketRegistry` for lock creation
*   `LockComponentRegistry` for service access
*   `UnlockMessageListenerManager` for listener lifecycle

### 5.3. Strategy Pattern
*   `LockOwnerSupplier` for owner ID generation
*   Different lock types implementing common interfaces

### 5.4. Observer Pattern
*   Redis Pub/Sub for unlock notifications
*   `UnlockMessageListener` observing channel messages

## 6. Data Flow

### 6.1. Lock Acquisition Flow
1. Client calls `lockAsync()` or `lock()`
2. `AbstractRedisLock` generates unique `requestUuid`
3. `RedisLockOperations` executes Lua script via `ClusterCommandExecutor`
4. If lock unavailable, `LockSemaphoreHolder` manages waiting
5. `UnlockMessageListener` signals waiters on unlock notifications

### 6.2. Lock Release Flow
1. Client calls `unlockAsync()` or `unlock()`
2. Lua script atomically releases lock and publishes unlock message
3. `UnlockMessageListener` receives message and signals waiters
4. `LockWatchdog` removes lock from monitoring

### 6.3. Watchdog Flow
1. Successful lock acquisition registers with `LockWatchdog`
2. Scheduled task periodically extends lease via `extend_lock.lua`
3. Lock release unregisters from watchdog monitoring

## 7. Integration with Destilink Framework

### 7.1. Spring Boot Auto-Configuration
*   `RedisLockAutoConfiguration` with explicit bean definitions
*   No `@ComponentScan` usage
*   Conditional bean creation based on properties

### 7.2. Redis Core Integration
*   Mandatory use of `ClusterCommandExecutor` for all Redis operations
*   Key construction using `redis-core` utilities
*   Shared configuration via `RedisCoreProperties`

### 7.3. Structured Logging
*   `AbstractRedisLockException` with `ExceptionMarkerProvider`
*   `LockContextDecorator` for MDC enrichment
*   Parameterized SLF4J logging throughout

### 7.4. Metrics Integration
*   `LockMonitor` bean for Micrometer metrics
*   Comprehensive operational visibility
*   Integration with Spring Boot Actuator

## 8. Scalability and Performance Considerations

*   **Non-blocking operations**: Async-first design prevents thread pool exhaustion
*   **Reduced Redis load**: Pub/Sub eliminates continuous polling
*   **Atomic operations**: Lua scripts minimize network round trips
*   **Efficient waiting**: Semaphore-based blocking with intelligent signaling
*   **Cluster compatibility**: Hash tags ensure proper key distribution

This architecture provides a robust, scalable, and maintainable distributed locking solution that integrates seamlessly with the Destilink Framework while adhering to its design principles and best practices.