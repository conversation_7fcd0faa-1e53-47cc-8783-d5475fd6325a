-- Script to try to acquire a lock (non-blocking)
-- KEYS[1] = Lock key
-- ARGV[1] = Lock value (owner ID)
-- ARGV[2] = Lease time in milliseconds
-- ARGV[3] = Enable reentrancy ("true" or "false")

local lockKey = KEYS[1]
local lockValue = ARGV[1]
local leaseTimeMillis = tonumber(ARGV[2])
local enableReentrancy = ARGV[3] == "true"

-- Check if the lock already exists
local currentValue = redis.call("GET", lockKey)

-- If lock doesn't exist, acquire it
if currentValue == false then
    redis.call("SET", lockKey, lockValue, "PX", leaseTimeMillis, "NX")
    return nil -- Success (lock acquired)
end

-- If reentrancy is enabled and the current lock holder is the same as the requester
if enableReentrancy and currentValue == lockValue then
    -- Refresh the lease time
    redis.call("PEXPIRE", lockKey, leaseTimeMillis)
    return nil -- Success (lock refreshed)
end

-- Lock exists and is held by someone else or reentrancy is disabled
return currentValue -- Return the current lock holder (failure to acquire)